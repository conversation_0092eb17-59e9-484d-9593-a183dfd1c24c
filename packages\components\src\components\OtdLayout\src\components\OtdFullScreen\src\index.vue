<template>
  <Tooltip :title="getTitle" placement="bottom" :mouseEnterDelay="0.5">
    <span class="otd-layout-action" @click="toggle">
      <FullscreenOutlined :style="{ fontSize: '20px' }" v-if="!isFullscreen" />
      <FullscreenExitOutlined :style="{ fontSize: '20px' }" v-else />
    </span>
  </Tooltip>
</template>
<script lang="ts" setup name="OtdFullScreen">
  import { computed, unref } from 'vue';
  import { Tooltip } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useFullscreen } from '@vueuse/core';

  import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons-vue';

  const { t } = useI18n();
  const { toggle, isFullscreen } = useFullscreen();

  const getTitle = computed(() => {
    return unref(isFullscreen) ? t('layout.header.tooltipExitFull') : t('layout.header.tooltipEntryFull');
  });
</script>
