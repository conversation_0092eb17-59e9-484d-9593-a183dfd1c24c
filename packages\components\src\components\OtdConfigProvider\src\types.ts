import { MessageType, OtdListRequestType, OtdPageParamsType, ResetPasswordType } from '/@/components/OtdLayout';
import { Recordable } from '/#/global';
import { TaskOptionItemType } from '/@/components/OtdTaskSearch';
import { UserOptionItemType } from '/@/components/OtdUserSearch';
import { VNode } from 'vue';
import { NotifyCountType } from '/@/storage/notifyStorage';

export type NotifyParamsType = OtdPageParamsType & { messageType: MessageType };

export type OtdLayoutConfigType = {
  // 根容器
  getRootContainer?: () => HTMLElement;
  // 应用名称
  appTitle?: string;
  // 用户信息
  userInfo?: Recordable;
  // 判断是否存在权限方法
  judgePremission?: (key?: string) => boolean;
  // 返回登录逻辑 => 在锁屏时使用
  logout?: () => Promise<any>;
  // 重置密码接口
  resetPassword?: (form: ResetPasswordType) => Promise<any>;
  // 上传头像接口
  uploadAvatar?: (data) => Promise<any>;
  // 长连接链接
  webSocketUrl?: () => string;
  // AI长连接链接
  aiSocketUrl?: () => string;
  // 通知每页条数
  notifyPageSize?: number;
  // 获取通知数量
  getNotifyCountRequest?: () => Promise<NotifyCountType>;
  // 通知请求接口
  notificationRequest?: (data: NotifyParamsType) => Promise<OtdListRequestType>;
  // 通知全部已读接口
  readAllNotify?: (type: MessageType) => Promise<any>;
  // 通知设置单条已读接口
  setReadNotify?: (id: string | number) => Promise<any>;
  // 远程搜索任务
  getFetchTask: (data?) => Promise<TaskOptionItemType[]>;
  // 远程搜索用户
  getFetchUser: (data?) => Promise<UserOptionItemType[]>;
  // 获取用户列表（一次）
  getUserData: (data?) => Promise<UserOptionItemType[]>;
  // 获取用户头像
  getAvatar: (id: string) => string;
  // 帮助文档链接
  helpDocument?: string;
  // 更新日志链接
  changeLog?: string;
  HistoryRecordHandler: Recordable<(data: VNode, record: Recordable) => VNode>;
  //左上角logo点击
  logoClick?: () => void;
};
