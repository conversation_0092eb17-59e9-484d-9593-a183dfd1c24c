.otd-color-weak {
  filter: invert(80%);
}

.otd-gray-mode {
  filter: grayscale(100%);
  filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
}

.otd-gradient-button {
  border-width: 0;
  color: var(--otd-white-text);
  background-image: var(--otd-gradient-bg);
  padding: 8px;
  line-height: 20px;
  height: auto;

  &:not(:disabled):hover {
    color: var(--otd-white-text);
    filter: opacity(0.8);
  }
}

.text-title {
  color: var(--otd-icon-text);
}

.text-center {
  text-align: center;
}

.placeholder-text {
  color: var(--otd-gray4-color);
  font-size: 14px;
}
.otd-primary-color {
  color: var(--otd-primary-color);
}
.otd-column-gap {
  --gap: 10px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  column-gap: var(--gap);
}
.otd-word-break {
  display: inline-block;
  word-break: break-word;
}

.otd-box-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.otd-box-left {
  display: flex;
  align-items: center;
}

.box-bottom-border {
  border-bottom: 1px solid var(--otd-border-color);
}

.box-top-border {
  position: relative;
  border-top: 1px solid var(--otd-border-color);
}

.otd-truncate {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.otd-desc-text {
  font-size: 12px;
  font-weight: normal;
  line-height: 16px;
  color: var(--otd-gray3-color);
}

.otd-title-text {
  font-size: 16px;
  font-weight: bold;
  line-height: 22px;
  color: var(--otd-basic-text);
}

.enterTransform(@name) {
  .enter(@index) when (@index < 6) {
    * > .enter-@{name}:nth-child(@{index}) {
      transform: e('translate@{name}(50px)');
    }

    * > .-enter-@{name}:nth-child(@{index}) {
      transform: e('translate@{name}(-50px)');
    }

    * > .enter-@{name}:nth-child(@{index}),
    * > .-enter-@{name}:nth-child(@{index}) {
      z-index: 10- @index;
      opacity: 0;
      animation: e('enter-@{name}-animation 0.4s ease-in-out 0.3s');
      animation-fill-mode: forwards;
      @delay: @index * (1 / 10);
      animation-delay: e('@{delay}s');
    }

    .enter(@index + 1);
  }

  .enter(0);
}

.enterTransform(x);
.enterTransform(y);

@keyframes enter-x-animation {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes enter-y-animation {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* fade-slide */
.fade-slide-leave-active,
.fade-slide-enter-active {
  transition: all 0.3s;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.font-bold {
  font-weight: bold;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.text-4xl {
  font-size: 2.25rem;
}

.text-5xl {
  font-size: 3rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.ml-6px {
  margin-left: 6px;
}

.mt-4px {
  margin-top: 4px;
}

.is-flash {
  animation: flashing 1s infinite steps(1);
}

@keyframes flashing {
  50% {
    color: transparent;
  }
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.otd-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.grid-full {
  grid-column: 1 / -1;
}

.grid-form-auto {
  display: grid;
  --width: 200px;
  grid-template-columns: repeat(auto-fill, minmax(var(--width), 1fr));
  column-gap: 10px;
}

.otd-selete-title {
  font-size: 14px;
  color: var(--otd-gray3-color);
  line-height: 16px;
  margin-bottom: 4px;
  padding: 0 10px;
}

.otd-text-link {
  color: var(--otd-primary-color);
  cursor: pointer;
}
