<template>
  <BasicModal
    :title="t('layout.header.forgetFormTitle')"
    v-bind="$attrs"
    :canFullscreen="false"
    :minHeight="100"
    @ok="submit"
    @cancel="cancel"
    @register="register"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup name="ResetPassword">
  import { useI18n } from '/@/hooks/web/useI18n';
  import { BasicModal, useModalInner } from '/@/components/BasicModal';
  import { BasicForm, useForm } from '/@/components/BasicForm';
  import { message } from 'ant-design-vue';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
  import { ResetPasswordType } from '../types';

  const { t } = useI18n();

  const [register, { changeOkLoading, closeModal }] = useModalInner();
  const { getGlobalProvide } = useGlobalConfig();
  const { resetPassword } = getGlobalProvide();

  const [registerForm, { getFieldsValue, validate, resetFields }] = useForm({
    showActionButtonGroup: false,
    labelWidth: 130,
    schemas: [
      {
        field: 'currentPassword',
        label: t('layout.header.currentPassword'),
        component: 'InputPassword',
        componentProps: {
          autocomplete: 'off',
          bordered: false,
        },
        required: true,
        colProps: {
          span: 22,
        },
      },
      {
        field: 'newPassword',
        label: t('layout.header.newPassword'),
        component: 'InputPassword',
        componentProps: {
          autocomplete: 'off',
          bordered: false,
        },
        required: true,
        colProps: {
          span: 22,
        },
      },
      {
        field: 'confirmPassword',
        label: t('layout.header.confirmPassword'),
        component: 'InputPassword',
        componentProps: {
          autocomplete: 'off',
          bordered: false,
        },
        required: true,
        colProps: {
          span: 22,
        },
      },
    ],
    layout: 'vertical',
  });
  const submit = async () => {
    try {
      changeOkLoading(true);
      await validate();
      const request = getFieldsValue() as ResetPasswordType;
      if (request.newPassword != request.confirmPassword) {
        message.error(t('layout.header.editPasswordMessage'));
        changeOkLoading(false);
        return;
      }
      await resetPassword?.(request);
      changeOkLoading(false);
      closeModal();
      await resetFields();
    } catch (error) {
      changeOkLoading(false);
    }
  };

  const cancel = () => {
    resetFields();
  };
</script>
