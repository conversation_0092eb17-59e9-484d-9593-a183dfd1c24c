<template>
  <div class="otd-lock-page" @click="handleShowForm(false)">
    <div class="otd-lock-page__unlock" v-show="showDate">
      <LockOutlined />
      <span>{{ t('layout.lock.unlock') }}</span>
    </div>

    <div class="otd-lock-page__time">
      <div class="otd-lock-page__hour">
        <span>{{ hour }}</span>
        <span class="meridiem" v-show="showDate">
          {{ meridiem }}
        </span>
      </div>
      <div class="otd-lock-page__minute">
        <span> {{ minute }}</span>
      </div>
    </div>
    <transition name="fade-slide">
      <div class="otd-lock-page-entry" v-show="!showDate">
        <div class="otd-lock-page-entry-content" @click.stop>
          <div class="otd-lock-page-entry__header enter-x">
            <OtdAvatar :url="userInfo.avatar" size="70px" />
            <p class="otd-lock-page-entry__header-name">
              {{ userInfo.username }}
            </p>
          </div>
          <InputPassword
            :placeholder="t('layout.lock.placeholder')"
            class="enter-x"
            v-model:value="password"
            @press-enter="unLock()"
          />
          <span class="otd-lock-page-entry__err-msg enter-x" v-if="errMsg">
            {{ t('layout.lock.alert') }}
          </span>
          <div class="otd-lock-page-entry__footer enter-x">
            <Button type="link" size="small" class="enter-x" :disabled="loading" @click="handleShowForm(true)">
              {{ t('common.back') }}
            </Button>
            <Button type="link" size="small" class="enter-x" :disabled="loading" @click="goLogin" v-if="logout">
              {{ t('layout.lock.backToLogin') }}
            </Button>
            <Button type="link" size="small" @click="unLock()" :loading="loading">
              {{ t('layout.lock.entry') }}
            </Button>
          </div>
        </div>
      </div>
    </transition>

    <div class="otd-lock-page__date">
      <div class="text-5xl enter-x" v-show="!showDate">
        {{ hour }}:{{ minute }} <span class="text-3xl">{{ meridiem }}</span>
      </div>
      <div class="text-2xl">{{ year }}/{{ month }}/{{ day }} {{ week }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { OtdAvatar } from '/@/components/OtdAvatar';
  import { Input, Button } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useNow } from './useNow';
  import { LockOutlined } from '@ant-design/icons-vue';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';

  const InputPassword = Input.Password;

  const password = ref('');
  const loading = ref(false);
  const errMsg = ref(false);
  const showDate = ref(true);

  const { getGlobalProvide } = useGlobalConfig();
  const { getUserInfo, getLockStorage: lockStore, logout } = getGlobalProvide();

  const { hour, month, minute, meridiem, year, day, week } = useNow(true);

  const { t } = useI18n();

  const userInfo = computed(() => {
    return getUserInfo || {};
  });

  /**
   * @description: unLock
   */
  function unLock() {
    if (!password.value) {
      return;
    }
    let pwd = password.value;
    try {
      loading.value = true;
      const res = lockStore.unLock(pwd);
      errMsg.value = !res;
    } finally {
      loading.value = false;
    }
  }

  function goLogin() {
    if (logout) {
      logout();
      lockStore.resetLockInfo();
    }
  }

  function handleShowForm(show = false) {
    showDate.value = show;
  }
</script>
<style lang="less" scoped>
  @import '/@/style/color/breakpoint.less';

  @prefix-cls: ~'@{namespace}-lock-page';

  .@{prefix-cls} {
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
    background-color: var(--otd-black-bg);
    z-index: var(--otd-lock-z-index);

    &__unlock {
      position: absolute;
      top: 0;
      left: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-top: 1.25rem;
      height: 4rem;
      transform: translateX(-50%);
      color: var(--otd-white-text);
      font-size: 1.25rem;
      line-height: 1.75rem;
      cursor: pointer;
    }
    &__time {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100vw;
      height: 100vh;
    }

    &__hour {
      position: relative;
      margin-right: 1.25rem;
      width: 40%;
      height: 40%;
      .meridiem {
        position: absolute;
        top: 1.25rem;
        left: 1.25rem;
        //  xl:text-xl
      }
    }
    &__minute {
      width: 40%;
      height: 40%;
    }
    &__hour,
    &__minute {
      display: flex;
      font-weight: 700;
      color: #bababa;
      background-color: #141313;
      border-radius: 30px;
      justify-content: center;
      align-items: center;

      @media screen and (max-width: @screen-md) {
        span:not(.meridiem) {
          font-size: 160px;
        }
      }

      @media screen and (min-width: @screen-md) {
        span:not(.meridiem) {
          font-size: 160px;
        }
        height: 80%;
      }

      @media screen and (max-width: @screen-sm) {
        span:not(.meridiem) {
          font-size: 90px;
        }
      }
      @media screen and (min-width: @screen-lg) {
        span:not(.meridiem) {
          font-size: 220px;
        }
      }

      @media screen and (min-width: @screen-xl) {
        span:not(.meridiem) {
          font-size: 260px;
        }
      }
      @media screen and (min-width: @screen-2xl) {
        span:not(.meridiem) {
          font-size: 320px;
        }
      }
    }
    &__date {
      //  text-gray-300  2xl:text-3xl text-center enter-y
      position: absolute;
      bottom: 1.25rem;
      width: 100%;
      font-size: 1.25rem;
      line-height: 1.75rem;
      text-align: center;
      color: #bababa;
      > div:first-of-type {
        margin-bottom: 1.5rem;
      }
    }

    &-entry {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(8px);
      justify-content: center;
      align-items: center;

      &-content {
        width: 260px;
      }

      &__header {
        display: flex;
        flex-direction: column;
        align-items: center;
        // enter-x

        &-img {
          width: 70px;
          margin: 0 auto;
          border-radius: 50%;
        }

        &-name {
          margin-top: 5px;
          font-weight: 500;
          color: #bababa;
        }
      }

      &__err-msg {
        display: inline-block;
        margin-top: 10px;
        color: var(--otd-error-color);
      }

      &__footer {
        display: flex;
        justify-content: space-between;
        margin-top: 0.5rem;
      }
    }
  }
</style>
