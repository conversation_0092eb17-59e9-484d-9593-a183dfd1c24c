<template>
  <OtdHistoryCard
    ref="dataRef"
    height="500px"
    v-model:value="data"
    :load-more="loadMore"
    :immediate-load="false"
    @click-item="handleClickItem"
  >
    <template #header>
      <OtdHistoryFilter v-model:value="checked" :checkedAll="true" />
    </template>
  </OtdHistoryCard>
</template>
<script lang="ts" setup>
  import { OtdHistoryCard, OtdHistoryFilter } from '@otd/otd-ui';
  import * as dayjs from 'dayjs';
  import { ref, watch } from 'vue';

  const data = ref([]);
  const dataRef = ref();
  const checked = ref([]);

  watch(
    () => checked.value,
    () => {
      dataRef.value.refresh();
    },
  );

  function handleClickItem(item) {
    console.log(item);
  }

  function loadMore({ page }) {
    return new Promise<{ items: Record<string, any>[]; totalCount: number }>((resolve) => {
      setTimeout(() => {
        resolve({
          items: [
            {
              relatedId: '3a14d8cf-5e7b-68ea-8ee8-531984741a8a',
              relatedTitle: '测试任务',
              status: 10,
              relatedName: null,
              relatedType: 4,
              subRelatedType: null,
              taskType: 2,
              taskCustomTypeId: null,
              taskCustomType: null,
              i18NRelatedType: null,
              i18NSubRelatedType: null,
              content: null,
              module: 'TMS',
              objectName: 'TaskItem',
              type: 'TaskType',
              actionType: 'Edit',
              creatorName: '邢黎敏',
              creatorId: '3a0c30de-4720-871e-c121-852419671480',
              creationTime: '2024-09-09T15:41:01.679145',
              prevValue:
                '[{"Color":null,"Value":"1","Label":"TaskType"},{"Color":null,"Value":null,"Label":"CustomIcon"},{"Color":null,"Value":null,"Label":"CustomName"}]',
              newValue:
                '[{"Color":null,"Value":"2","Label":"TaskType"},{"Color":null,"Value":null,"Label":"CustomIcon"},{"Color":null,"Value":null,"Label":"CustomName"}]',
              data: null,
            },
            {
              relatedId: '3a14d8cf-5e7b-68ea-8ee8-531984741a8a',
              relatedTitle: '测试任务',
              status: 10,
              relatedName: null,
              relatedType: 4,
              subRelatedType: null,
              i18NRelatedType: null,
              i18NSubRelatedType: null,
              content: null,
              module: 'TMS',
              objectName: 'TaskItem',
              type: 'TaskType',
              actionType: 'Edit',
              creatorName: '邢黎敏',
              creatorId: '3a0c30de-4720-871e-c121-852419671480',
              creationTime: '2024-09-09T13:55:03.597735',
              prevValue:
                '[{"Color":null,"Value":"Project","Label":"TaskType"},{"Color":null,"Value":null,"Label":"CustomIcon"},{"Color":null,"Value":null,"Label":"CustomName"}]',
              newValue:
                '[{"Color":null,"Value":"Task","Label":"TaskType"},{"Color":null,"Value":null,"Label":"CustomIcon"},{"Color":null,"Value":null,"Label":"CustomName"}]',
              data: null,
            },
            {
              relatedId: '3a136b72-fa2f-6903-4564-575a4aa50543',
              relatedTitle: '12121',
              content: null,
              module: 'TMS',
              objectName: 'TaskItem',
              type: 'WorkSpend',
              actionType: 'PlanTaskHourEditCategory',
              creatorName: 'limin',
              creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              creationTime: '2024-09-02T10:51:08.303034',
              prevValue:
                '[{"Color":null,"Value":null,"Label":"WorkDate"},{"Color":null,"Value":"22.0","Label":"WorkHour"},{"Color":null,"Value":"False","Label":"IsShowWorkByDay"},{"Color":null,"Value":"limin","Label":"UserName"},{"Color":null,"Value":"Default","Label":"Category"}]',
              newValue:
                '[{"Color":null,"Value":null,"Label":"WorkDate"},{"Color":null,"Value":"22.0","Label":"WorkHour"},{"Color":null,"Value":"False","Label":"IsShowWorkByDay"},{"Color":null,"Value":"limin","Label":"UserName"},{"Color":null,"Value":"Outfield","Label":"Category"}]',
              data: null,
            },
            {
              relatedId: '3a14b41d-4d36-6306-f3a8-74501915a4e8',
              relatedTitle: '22',
              status: 10,
              relatedName: null,
              relatedType: 4,
              subRelatedType: null,
              i18NRelatedType: null,
              i18NSubRelatedType: null,
              content: null,
              module: 'TMS',
              objectName: 'TaskItem',
              type: 'WorkSpend',
              actionType: 'PlanTaskHourEditTime',
              creatorName: 'limin',
              creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              creationTime: '2024-08-30T14:33:02.820405',
              prevValue:
                '[{"Color":null,"Value":"2024-08-30","Label":"WorkDate"},{"Color":null,"Value":"2.0","Label":"WorkHour"},{"Color":null,"Value":"False","Label":"IsShowWorkByDay"},{"Color":null,"Value":"limin","Label":"UserName"},{"Color":null,"Value":"Default","Label":"Category"}]',
              newValue:
                '[{"Color":null,"Value":null,"Label":"WorkDate"},{"Color":null,"Value":"3.0","Label":"WorkHour"},{"Color":null,"Value":"False","Label":"IsShowWorkByDay"},{"Color":null,"Value":null,"Label":"UserName"},{"Color":null,"Value":"Default","Label":"Category"}]',
              // prevValue:
              //   '[{"Color":null,"Value":null,"Label":"WorkDate"},{"Color":null,"Value":"2.0","Label":"WorkHour"},{"Color":null,"Value":"False","Label":"IsShowWorkByDay"},{"Color":null,"Value":null,"Label":"UserName"},{"Color":null,"Value":"Default","Label":"Category"}]',
              // newValue:
              //   '[{"Color":null,"Value":"2024-08-30","Label":"WorkDate"},{"Color":null,"Value":"3.0","Label":"WorkHour"},{"Color":null,"Value":"False","Label":"IsShowWorkByDay"},{"Color":null,"Value":"limin","Label":"UserName"},{"Color":null,"Value":"Default","Label":"Category"}]',
              data: null,
            },
            {
              relatedId: '3a14b41d-4d36-6306-f3a8-74501915a4e8',
              relatedTitle: '22',
              status: 10,
              relatedName: null,
              relatedType: 4,
              subRelatedType: null,
              i18NRelatedType: null,
              i18NSubRelatedType: null,
              content: null,
              module: 'TMS',
              objectName: 'TaskItem',
              type: 'WorkSpend',
              actionType: 'RealTaskHourCreate',
              creatorName: 'limin',
              creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              creationTime: '2024-08-30T14:32:46.665617',
              prevValue: null,
              newValue:
                '[{"Color":null,"Value":"2024-08-30","Label":"WorkDate"},{"Color":null,"Value":"2.0","Label":"WorkHour"},{"Color":null,"Value":"False","Label":"IsShowWorkByDay"},{"Color":null,"Value":"limin","Label":"UserName"},{"Color":null,"Value":"Default","Label":"Category"}]',
              data: null,
            },
            {
              relatedId: '3a14b41d-4d36-6306-f3a8-74501915a4e8',
              relatedTitle: '22',
              status: 10,
              relatedName: null,
              relatedType: 4,
              subRelatedType: null,
              i18NRelatedType: null,
              i18NSubRelatedType: null,
              content: null,
              module: 'TMS',
              objectName: 'TaskItem',
              type: 'WorkSpend',
              actionType: 'PlanTaskHourCreate',
              creatorName: 'limin',
              creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              creationTime: '2024-08-30T14:32:42.811544',
              prevValue: null,
              newValue:
                '[{"Color":null,"Value":"2024-08-30","Label":"WorkDate"},{"Color":null,"Value":"2.0","Label":"WorkHour"},{"Color":null,"Value":"False","Label":"IsShowWorkByDay"},{"Color":null,"Value":"limin","Label":"UserName"},{"Color":null,"Value":"Default","Label":"Category"}]',
              data: null,
            },
            ...new Array(10).fill(0).map(() => ({
              relatedId: Math.floor(Math.random() * 2),
              relatedTitle: '测试责任人',
              content: null,
              module: 'TMS',
              objectName: 'TaskItem',
              type: 'Priority',
              actionType: 'Edit',
              creatorName: '邢黎敏',
              creatorId: '3a0c30de-4720-871e-c121-852419671480',
              creationTime: dayjs()
                .add(-1 * (page - 1), 'day')
                .format('YYYY-MM-DDTMM:hh'),
              prevValue: null,
              newValue: '{"Color":null,"Value":"2","Label":"High"}',
              data: null,
              status: 10,
            })),
          ],
          totalCount: 30,
        });
      }, 500);
    });
  }
</script>
<style lang="less" scoped></style>
