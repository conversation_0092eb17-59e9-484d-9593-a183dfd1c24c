import type { ProjectConfigType } from '/#/config';
import { computed, ref, unref } from 'vue';
import { projectSetting } from '/@/settings';
import { APP_DARK_MODE_KEY_, PROJ_CFG_KEY } from '/@/enums/cacheEnum';
import { ThemeEnum } from '/@/enums/appEnum';
import { Persistent } from '/@/utils/cache/persistent';

const config = Persistent.getLocal(PROJ_CFG_KEY);
export const themeMedia = window.matchMedia('(prefers-color-scheme: dark)');

export const getCurrentThemeModa = () => {
  const isDark = themeMedia.matches;
  return (localStorage.getItem(APP_DARK_MODE_KEY_) as ThemeEnum) || (isDark ? ThemeEnum.DARK : ThemeEnum.LIGHT);
};

const ProjectConfig = ref<ProjectConfigType>(
  Object.assign({}, projectSetting, config, { darkMode: getCurrentThemeModa() }),
);
export function useProjectConfigStorage() {
  return {
    getProjectConfig: computed(() => unref(ProjectConfig)),
    setProjectConfig(config: Partial<ProjectConfigType>) {
      Object.assign(ProjectConfig.value, config);
      Persistent.setLocal(PROJ_CFG_KEY, unref(ProjectConfig.value), true);
    },
  };
}

export function useRootSetting() {
  const { getProjectConfig, setProjectConfig } = useProjectConfigStorage();
  const getGrayMode = computed(() => unref(getProjectConfig).grayMode);
  const getColorWeak = computed(() => unref(getProjectConfig).colorWeak);
  const getDarkMode = computed(() => unref(getProjectConfig).darkMode);
  const getIsDarkMode = computed(() => unref(getDarkMode) === ThemeEnum.DARK);
  const setDarkMode = (theme: ThemeEnum) => setProjectConfig({ darkMode: theme });
  return {
    getGrayMode,
    getColorWeak,
    getDarkMode,
    getIsDarkMode,
    setDarkMode,
    setProjectConfig,
  };
}
