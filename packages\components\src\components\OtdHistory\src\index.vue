<template>
  <div class="otd-history" v-loading="loading">
    <HistorySimple v-if="simple" :data="data" v-bind="$props" @open-user="openUser" @open-content="openContent">
      <template #[slot]="data" v-for="(_, slot) in $slots" :key="slot">
        <slot :name="slot" v-bind="data"></slot>
      </template>
    </HistorySimple>
    <HistoryTimeline v-else :data="data" v-bind="$props" />
  </div>

  <Empty v-if="!loading && (!data || !data.length)" />
</template>
<script lang="ts" setup>
  import { getEmits, getProps } from './props';
  import HistorySimple from './components/HistorySimple.vue';
  import HistoryTimeline from './components/HistoryTimeline.vue';
  import { Empty } from 'ant-design-vue';

  defineProps(getProps());

  const emit = defineEmits(getEmits());

  function openUser(val, item) {
    emit('openUser', val, item);
  }
  function openContent(val, item) {
    emit('openContent', val, item);
  }
</script>
<style lang="less" scoped>
  .otd-history {
    height: 100%;
  }
</style>
