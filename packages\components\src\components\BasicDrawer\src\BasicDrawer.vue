<template>
  <Drawer :class="getClass" @close="onClose" v-bind="getBindValues">
    <div class="otd-drawer-hide-header__close" v-if="hideHeader" @click="onClose">
      <CloseOutlined />
    </div>
    <template #title v-if="!$slots.title">
      <DrawerHeader :title="getMergeProps.title" :isDetail="isDetail" :showDetailBack="showDetailBack" @close="onClose">
        <template #titleToolbar>
          <slot name="titleToolbar"></slot>
        </template>
      </DrawerHeader>
    </template>
    <template v-else #title>
      <slot name="title"></slot>
    </template>

    <BasicScrollContainer
      :style="getScrollContentStyle"
      :wrap-style="getScrollWrapStyle"
      v-loading="getLoading"
      :loading-tip="loadingText || t('common.loadingText')"
    >
      <slot></slot>
    </BasicScrollContainer>
    <DrawerFooter v-bind="getProps" @close="onClose" @ok="handleOk" :height="getFooterHeight">
      <template #[item]="data" v-for="item in Object.keys($slots)">
        <slot :name="item" v-bind="data || {}"></slot>
      </template>
    </DrawerFooter>
  </Drawer>
</template>
<script lang="ts" setup name="BasicDrawer">
  import { CloseOutlined } from '@ant-design/icons-vue';
  import type { DrawerInstance, BasicDrawerProps } from './typing';
  import type { CSSProperties } from 'vue';
  import type { Nullable, Recordable } from '/#/global';
  import { ref, computed, watch, unref, nextTick, getCurrentInstance } from 'vue';
  import { Drawer } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { isFunction, isNumber } from '/@/utils/is';
  import { deepMerge } from '/@/utils';
  import DrawerFooter from './components/DrawerFooter.vue';
  import DrawerHeader from './components/DrawerHeader.vue';
  import { BasicScrollContainer } from '/@/components/BasicContainer';
  import { basicProps } from './props';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useAttrs } from '/@/hooks/core/useAttrs';

  const props = defineProps(basicProps);

  const emit = defineEmits(['open-change', 'ok', 'close', 'register']);

  const visibleRef = ref(false);
  const attrs = useAttrs();
  const propsRef = ref<Partial<Nullable<BasicDrawerProps>>>(null);

  const { t } = useI18n();
  const { prefixVar, prefixCls } = useDesign('basic-drawer');
  const getClass = computed(() => [prefixCls, props.hideHeader && 'otd-drawer-hide-header']);

  const drawerInstance: DrawerInstance = {
    setDrawerProps: setDrawerProps,
    emitVisible: undefined,
  };

  const instance = getCurrentInstance();

  instance && emit('register', drawerInstance, instance.uid);

  const getMergeProps = computed(() => {
    return {
      ...props,
      ...unref(propsRef),
    } as BasicDrawerProps;
  });

  const getProps = computed((): BasicDrawerProps => {
    const opt = {
      placement: 'right',
      ...unref(attrs),
      ...unref(getMergeProps),
      open: unref(visibleRef),
    };
    opt.title = undefined;
    const { isDetail, width, wrapClassName, getContainer } = opt;
    if (isDetail) {
      if (!width) {
        opt.width = '100%';
      }
      const detailCls = `${prefixCls}__detail`;
      opt.class = wrapClassName ? `${wrapClassName} ${detailCls}` : detailCls;

      if (!getContainer) {
        opt.getContainer = `.${prefixVar}-layout-content` as any;
      }
    }
    return opt as BasicDrawerProps;
  });

  const getBindValues = computed((): BasicDrawerProps => {
    return {
      ...attrs,
      ...unref(getProps),
    };
  });

  // Custom implementation of the bottom button,
  const getFooterHeight = computed(() => {
    const { footerHeight, showFooter } = unref(getProps);
    if (showFooter && footerHeight) {
      return isNumber(footerHeight) ? `${footerHeight}px` : `${footerHeight.replace('px', '')}px`;
    }
    return `0px`;
  });
  const getScrollWrapStyle = computed(() => [
    {
      padding: '16px',
      marginBottom: 0,
      ...props.scrollWrapStyle,
    },
  ]);

  const getScrollContentStyle = computed((): CSSProperties => {
    const footerHeight = unref(getFooterHeight);
    return {
      position: 'relative',
      height: `calc(100% - ${footerHeight})`,
    };
  });

  const getLoading = computed(() => {
    return !!unref(getProps)?.loading;
  });

  watch(
    () => props.open,
    (newVal, oldVal) => {
      if (newVal !== oldVal) visibleRef.value = newVal;
    },
    { deep: true },
  );

  watch(
    () => visibleRef.value,
    (open) => {
      nextTick(() => {
        emit('open-change', open);
        instance && drawerInstance.emitVisible?.(open, instance.uid);
      });
    },
  );

  // Cancel event
  async function onClose(e: Recordable) {
    const { closeFunc } = unref(getProps);
    emit('close', e);
    if (closeFunc && isFunction(closeFunc)) {
      const res = await closeFunc();
      visibleRef.value = !res;
      return;
    }
    visibleRef.value = false;
  }

  function setDrawerProps(props: Partial<BasicDrawerProps>): void {
    // Keep the last setDrawerProps
    propsRef.value = deepMerge(unref(propsRef) || ({} as any), props);

    if (Reflect.has(props, 'open')) {
      visibleRef.value = !!props.open;
    }
  }

  function handleOk() {
    emit('ok');
  }
</script>
<style lang="less">
  @header-height: 60px;
  @detail-header-height: 40px;
  @prefix-cls: ~'@{namespace}-basic-drawer';
  @prefix-cls-detail: ~'@{namespace}-basic-drawer__detail';

  .@{prefix-cls} {
    .ant-drawer-wrapper-body {
      overflow: hidden;
    }

    .ant-drawer-close {
      &:hover {
        color: var(--otd-error-color);
      }
    }

    .ant-drawer-body {
      height: calc(100% - @header-height);
      padding: 0;
      background-color: var(--otd-basic-bg);

      > .scrollbar > .scrollbar__bar.is-horizontal {
        display: none;
      }
    }
  }

  .@{prefix-cls-detail} {
    position: absolute;

    .ant-drawer-header {
      width: 100%;
      height: @detail-header-height;
      padding: 0;
      border-top: 1px solid var(--otd-border-color);
      box-sizing: border-box;
    }

    .ant-drawer-title {
      height: 100%;
    }

    .ant-drawer-close {
      height: @detail-header-height;
      line-height: @detail-header-height;
    }

    .scrollbar__wrap {
      padding: 0 !important;
    }

    .ant-drawer-body {
      height: calc(100% - @detail-header-height);
    }
  }
</style>
