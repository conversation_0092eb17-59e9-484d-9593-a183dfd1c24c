import { computed, getCurrentInstance, ref } from 'vue';
import { useInjectBody } from '../../../../useBasicTable';
import { BasicTableEmitsType } from '../../../type';
import { BasicTableCellPropsType } from './type';

export class ResizeCell {
  removeDom: HTMLDivElement | null;
  removeInfo: DOMRect | null;
  space: number = 0;
  parentInfo: DOMRect | null;
  tableDom: HTMLElement | null;
  tableInfo: DOMRect | null;
  sizeMap = { max: 0, min: 0 };
  line: HTMLElement | null;
  mouseUpFn: (data) => void;
  constructor(event: MouseEvent, mouseUpFn: (data) => void) {
    this.mouseUpFn = mouseUpFn;
    this.removeDom = event.target as HTMLDivElement;

    this.tableDom = this.removeDom?.parentElement?.parentElement?.parentElement!;
    this.parentInfo = this.removeDom!.parentElement!.getClientRects()[0];
    this.removeInfo = this.removeDom!.getClientRects()[0];
    this.tableInfo = this.tableDom!.getClientRects()[0];
    this.line = document.createElement('div');
    const computedStyle = getComputedStyle(this.removeDom!.parentElement!);
    const maxWidth = computedStyle.getPropertyValue('max-width');
    const minWidth = computedStyle.getPropertyValue('min-width');
    this.sizeMap.max = maxWidth !== 'none' ? +maxWidth.split('px')[0] : 300;
    this.sizeMap.min = minWidth !== 'auto' ? +minWidth.split('px')[0] : 100;
    this.space = event.x - this.removeInfo.x - this.removeInfo.width;
    this.resizeDown(event);
  }
  // 移动宽度按下
  resizeDown = (e: MouseEvent) => {
    const { removeDom, space, parentInfo, tableInfo, tableDom, line } = this;
    line!.className = 'otd-basic-table-line';
    line!.style.left = e.x - space - 5 - tableInfo!.x + 'px';
    tableDom?.append(line!);
    removeDom!.style.left = e.x - space - parentInfo!.x + 'px';
    document.body.style.cursor = 'col-resize';
    document.addEventListener('mouseup', this.resizeUp);
    document.addEventListener('mousemove', this.resizeMove);
  };
  // 移动宽度抬起
  resizeUp = () => {
    this.mouseUpFn(+this.removeDom!.style.left.split('px')[0]);
    this.removeDom = null;
    this.removeInfo = null;
    this.parentInfo = null;
    this.tableDom = null;
    this.tableInfo = null;
    this.line?.remove();
    this.line = null;
    document.body.style.cursor = 'initial';
    document.removeEventListener('mouseup', this.resizeUp);
    document.removeEventListener('mousemove', this.resizeMove);
  };
  // 移动宽度
  resizeMove = (e: MouseEvent) => {
    const { removeDom, space, parentInfo, tableInfo, sizeMap, line } = this;
    if (removeDom) {
      const lineSpace = parentInfo!.x - tableInfo!.x;
      let width = e.x - space - parentInfo!.x;
      if (width >= sizeMap.max) {
        width = sizeMap.max;
      } else if (width <= sizeMap.min) {
        width = sizeMap.min;
      }
      line!.style.left = width - 5 + lineSpace + 'px';
      removeDom.style.left = width + 'px';
    }
  };
}

export function useBasicTableCell() {
  const { props, emit } = getCurrentInstance() as unknown as {
    props: BasicTableCellPropsType;
    emit: BasicTableEmitsType;
  };
  const BodyContent = useInjectBody();

  const getCssStyle = computed(() => {
    const { minWidth, align, maxWidth, width } = props.column;
    return {
      minWidth: minWidth && `${minWidth}px`,
      maxWidth: maxWidth && `${maxWidth}px`,
      width: width && `${width}px`,
      textAlign: align,
    };
  });

  const getCellClass = computed(() => {
    const { isMain } = props.column;
    return {
      'otd-basic-table__main': isMain,
      'is-expand': props.showExpand,
      'otd-truncate': !props.isHeader,
    };
  });

  function handleSetExpand(event) {
    if (!props.showExpand) return false;
    BodyContent?.onTriggerExpand(props.record, event);
  }

  const isMoveResize = ref(false);
  // 移动宽度按下
  function handleResizeDown(e) {
    isMoveResize.value = true;
    new ResizeCell(e, (value) => {
      isMoveResize.value = false;
      props.column.width = value;
      emit('resizeColumn', { column: props.column });
    });
  }

  return {
    getCssStyle,
    getCellClass,
    isMoveResize,
    handleSetExpand,
    handleResizeDown,
  };
}
