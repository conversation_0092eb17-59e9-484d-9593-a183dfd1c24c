import { PropType } from 'vue';
import { Recordable } from '/#/global';
import { mutable } from '/@/utils/props';
import { HistoryOptionType, OtdHistoryFieldsType } from './type';

export const DefaultFields: OtdHistoryFieldsType = {
  creatorId: 'creatorId',
  creatorName: 'creatorName',
  content: 'content',
  creationTime: 'creationTime',
  type: 'type',
  prevValue: 'prevValue',
  newValue: 'newValue',
  actionType: 'actionType',
};

export const getProps = () => ({
  fields: {
    type: Object as PropType<Partial<OtdHistoryFieldsType>>,
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array as PropType<Recordable[]>,
    default: () => [],
  },
  simple: {
    type: Boolean,
    default: false,
  },
  historyOptions: {
    type: Object as PropType<Recordable<HistoryOptionType>>,
  },
  moreHistoryOptions: {
    type: Object as PropType<Recordable<HistoryOptionType>>,
  },
  placeholder: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  popoverTitle: {
    type: String,
    default: '',
  },
  popoverLabel: {
    type: String,
    default: '',
  },
});

const emit = ['openUser', 'openContent'] as const;
export const getEmits = () => mutable(emit);
