<template>
  <OtdTag
    placeholder="添加标签"
    v-model:value="tags"
    :options="tagOptions"
    :update-tags="getTagsOptions"
    :disabled="disabled"
    :max-tag-count="0"
    @change="handleTagChange"
    @delete="handleTagDelete"
    @rename="handleTagRename"
    @save="handleTagSave"
    @change-type="handleTagChangeType"
    @create="handleTagCreate"
  />
  <OtdTag placeholder="添加标签" :options="tagOptions" :max-tag-count="2" />
  <OtdTag v-model:value="tags" placeholder="添加标签" is-simple :options="tagOptions" :max-tag-count="2" />
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { OtdTag } from '@otd/otd-ui';
  import { TagDto } from 'otd-ui/src/components/OtdTag/src/types';
  const disabled = ref(false);
  const tags = ref<TagDto[]>([
    {
      color: 'yellow',
      id: '1',
      tagAuth: 1,
      tagName: '新太阳',
      tagType: 0,
    },
    {
      color: '#2980B9',
      id: '2',
      tagAuth: 0,
      tagName: '夏普',
      tagType: 0,
    },
  ]);

  const tagOptions = ref<TagDto[]>([
    {
      color: 'yellow',
      id: '1',
      tagAuth: 1,
      tagName: '新太阳',
      tagType: 0,
    },
    {
      color: '#2980B9',
      id: '2',
      tagAuth: 0,
      tagName: '夏普',
      tagType: 0,
    },
    {
      color: '#9B59B6',
      id: '3',
      tagAuth: 0,
      tagName: '松下',
      tagType: 0,
    },
    {
      color: '#A9A9A9',
      id: '4',
      tagAuth: 1,
      tagName: '日世',
      tagType: 0,
    },
    {
      color: '#2980B9',
      id: '5',
      tagAuth: 0,
      tagName: '看板中心',
      tagType: 0,
    },
    {
      color: '#9B59B6',
      id: '6',
      tagAuth: 0,
      tagName: '系统优化',
      tagType: 0,
    },
  ]);
  function getTagsOptions() {
    return Promise.resolve();
  }
  function handleTagChange(data) {
    console.log(data, 'change');
  }
  function handleTagDelete() {
    console.log(111);
  }
  function handleTagRename(data) {
    console.log(data, 'rename');
  }
  function handleTagChangeType(data) {
    console.log(data);
  }
  function handleTagSave() {}
  function handleTagCreate(data) {
    tags.value?.unshift(data);
  }
</script>
<style lang="less" scoped></style>
