<template>
  <Popover
    v-model:open="openPopover"
    trigger="click"
    placement="bottomRight"
    :arrow="false"
    :align="{ offset: PopoverOffset }"
    :overlayStyle="{ width: '256px' }"
  >
    <div class="otd-quick-action__picker otd-layout-action">
      <i class="otdIconfont otd-icon-Menu"></i>
    </div>
    <template #content>
      <div class="otd-quick-action__menu">
        <div
          class="otd-quick-action__menu-item otd-box-center"
          v-for="item in QuickActionMenuData"
          :key="item.id"
          @click="handlerAction(item)"
        >
          <div class="otd-quick-action__menu-item-icon otd-box-center">
            <div
              class="otd-quick-action__menu-item-pin otd-box-center"
              @click.stop="setPinAction(item)"
              v-if="!item.fixed"
            >
              <i class="otdIconfont otd-icon-relieve-full" v-if="item.pin"></i>
              <i class="otdIconfont otd-icon-relieve" v-else></i>
            </div>
            <IconComponent :data="item" v-if="item.icon" />
          </div>
          <span class="otd-quick-action__menu-item-name otd-truncate">
            {{ item.name }}
          </span>
        </div>
      </div>
    </template>
  </Popover>
</template>
<script lang="tsx" setup>
  import { Popover } from 'ant-design-vue';
  import { useQuickActionMenu, IconComponent } from './useQuickActionMenu';
  import { MoreActionItem } from '/@/components/OtdMoreAction';
  import { PropType, ref, watchEffect } from 'vue';

  const props = defineProps({
    customQuickActions: {
      type: Array as PropType<MoreActionItem[]>,
      default: () => [],
    },
  });
  const PopoverOffset = [50, 16];
  const { openPopover, QuickActionMenu, defaultQuickActionMenu, getPinAction, setPinAction, handlerAction } =
    useQuickActionMenu();

  watchEffect(() => {
    getPinAction();
  });

  const QuickActionMenuData = ref<MoreActionItem[]>([]);
  watchEffect(() => {
    QuickActionMenu.clear();
    defaultQuickActionMenu.map((item) => QuickActionMenu.set(item.id, item));
    props.customQuickActions.map((item) => QuickActionMenu.set(item.id, item));
    QuickActionMenuData.value = Array.from(QuickActionMenu.values()).sort((curr, next) =>
      curr.fixed && next.fixed ? 0 : curr.fixed ? -1 : 1,
    );
  });
</script>
<style lang="less" scoped>
  .otd-quick-action {
    &__picker.otd-layout-action.ant-popover-open {
      background-color: var(--otd-gray-hover);
    }
    &__menu {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      max-height: calc(100vh - 200px);
      overflow-y: auto;
      margin: -12px;
      padding: 6px;

      &-item {
        height: 100px;
        cursor: pointer;
        border-radius: var(--otd-border-radius);
        flex-direction: column;
        overflow: hidden;
        &:hover,
        &.is-active {
          background-color: var(--otd-gray-hover);
        }
        &-icon {
          position: relative;
          border: 1px solid var(--otd-border-gray);
          border-radius: var(--otd-middle-radius);
          width: 56px;
          height: 56px;
          background-color: var(--otd-basic-bg);
          .otdIconfont {
            font-size: 24px;
          }
        }
        &-pin {
          position: absolute;
          right: -4px;
          top: -4px;
          transform: translate();
          width: 20px;
          height: 20px;
          border: 1px solid var(--otd-border-gray);
          background-color: var(--otd-basic-bg);
          border-radius: var(--otd-default-radius);
          .otdIconfont {
            font-size: 16px;
          }
          &:hover {
            background-color: var(--otd-gray-hover);
          }
        }
        &-name {
          margin-top: 10px;
          width: 100%;
          text-align: center;
          font-size: 12px;
        }
      }
    }
  }
</style>
