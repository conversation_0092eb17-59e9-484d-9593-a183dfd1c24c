/**
 * Multi-language related operations
 */
import { unref, computed, isRef } from 'vue';
import type { LocaleType } from '/#/config';
import type { Recordable } from '/#/global';
import { loadLocalePool, setHtmlPageLang } from './helper';
import { useLocaleStorage } from '/@/storage/localeStorage';
import { i18n } from './setI18n';

interface LangModule {
  message: Recordable;
  dateLocale: Recordable;
  dateLocaleName: string;
}

function setI18nLanguage(locale: LocaleType) {
  const localeStore = useLocaleStorage();

  if (unref(i18n).mode === 'legacy') {
    i18n.value.global.locale = locale;
  } else {
    if (isRef(i18n.value.global.locale)) {
      i18n.value.global.locale.value = locale;
    } else {
      i18n.value.global.locale = locale;
    }
  }
  localeStore.setLocaleInfo({ locale });
  setHtmlPageLang(locale);
}

export function useLocale() {
  const localeStore = useLocaleStorage();
  const { getLocale } = localeStore;
  const getShowLocalePicker = computed(() => localeStore.getShowPicker);

  const getAntdLocale = computed((): any => {
    return (unref(i18n)?.global?.getLocaleMessage(unref(getLocale)) as any)?.antdLocale ?? {};
  });

  // Switching the language will change the locale of useI18n
  // And submit to configuration modification
  async function changeLocale(locale: LocaleType) {
    const globalI18n = unref(i18n).global;
    const currentLocale = unref(globalI18n.locale);
    if (currentLocale === locale) {
      return locale;
    }

    if (loadLocalePool.includes(locale)) {
      setI18nLanguage(locale);
      return locale;
    }
    const langModule = ((await import(`./lang/${locale}.ts`)) as any).default as LangModule;
    if (!langModule) return;

    const { message } = langModule;

    globalI18n.setLocaleMessage(locale, message);
    loadLocalePool.push(locale);

    setI18nLanguage(locale);
    return locale;
  }

  return {
    getLocale,
    getShowLocalePicker,
    changeLocale,
    getAntdLocale,
  };
}
