import { getProps as getSearchProps } from '/@/components/BasicSearchPopover/src/props';
import { PropType } from 'vue';
import { TaskOptionItemType } from './type';
import { mutable } from '/@/tool';
import { QueryType, Recordable } from '/#/global';

export const getProps = () => ({
  ...getSearchProps<TaskOptionItemType>(),
  placeholder: {
    type: String,
  },
  icon: {
    type: String,
    default: 'otd-icon-add-2',
  },
  remoteQuery: {
    type: Object as PropType<Recordable>,
  },
  // 远程搜索方法
  remoteMethod: {
    type: Function as PropType<(data?: QueryType) => Promise<TaskOptionItemType[]>>,
  },
});

const emits = ['update:value'] as const;
export const getEmits = () => mutable(emits);
