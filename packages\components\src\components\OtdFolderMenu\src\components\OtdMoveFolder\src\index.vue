<template>
  <BasicModal
    :title="title || t('common.folder.moveTo')"
    :after-close="handleCancel"
    :okButtonProps="{ disabled: !chooseFolder }"
    :okText="t('common.okText')"
    :can-fullscreen="false"
    @ok="handleOk"
    @register="registerModal"
  >
    <div class="otd-move-folder">
      <OtdScrollbar v-if="(data || treeData || [])?.length > 0">
        <OtdFolderList
          :data="data || treeData"
          :loadData="loadData || onLoadData"
          v-model:selected="selectedKeys"
          v-model:expanded="expandedKeys"
          hide-action
          :folder-click="folderClick"
          :not-root="notRoot"
          @select="handleSelect"
        />
      </OtdScrollbar>
      <div class="otd-move-folder__empty" v-else><Empty /></div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { BasicModal } from '/@/components/BasicModal';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { Empty } from 'ant-design-vue';
  import { OtdFolderList } from '../../OtdFolderList';
  import { useMoveFolder } from './useMoveFolder';
  import { getProps } from '../../../props';
  import { useI18n } from '/@/hooks/web/useI18n';

  defineProps(getProps());
  defineEmits(['reload', 'register']);

  const { t } = useI18n();

  const {
    registerModal,
    treeData,
    onLoadData,
    handleRefrush,
    selectedKeys,
    expandedKeys,
    chooseFolder,
    handleSelect,
    handleCancel,
    handleOk,
  } = useMoveFolder();

  handleRefrush(undefined);
</script>
<style lang="less" scoped>
  .otd-move-folder {
    height: 500px;
    &__empty {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
</style>
