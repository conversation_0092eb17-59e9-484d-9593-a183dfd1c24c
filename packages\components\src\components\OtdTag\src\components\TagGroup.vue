<template>
  <div class="otd-tags-content" :class="[`is-${direction}`, className]">
    <template v-if="!isVirtual">
      <TagItem
        v-for="(item, index) in showData"
        :item="item"
        :index="index"
        :list="showData"
        :key="item.tagName"
        :hide-action="hideAction"
        :closeable="closeable"
        v-bind="$attrs"
      />
    </template>
    <OtdVirtualScroll v-else :itemHeight="48" :items="data" :max-height="300" :bench="20">
      <template #default="{ item, index }">
        <TagItem
          style="box-sizing: content-box; height: 30px"
          :item="item"
          :index="index"
          :list="data"
          :hide-action="hideAction"
          :closeable="closeable"
          is-full
          v-bind="$attrs"
        />
      </template>
    </OtdVirtualScroll>

    <Tooltip :title="hideContentText.join(', ')" v-if="data.length && !expendAll">
      <div class="otd-tags-content__item min-w-0 more-num" v-if="(maxTagCount ?? -1) > 0 && hideContent.length">
        +{{ data.length - (maxTagCount ?? 0) }}
      </div>
      <div class="placeholder-hover" v-else-if="maxTagCount === 0">
        <i class="otdIconfont otd-icon-a-cattagsize24"></i>
        <span>{{ data.length }}</span>
        <span>{{ t('common.tag.tag') }}</span>
      </div>
    </Tooltip>
  </div>
</template>
<script lang="ts" setup>
  import { computed, PropType } from 'vue';
  import { Tooltip } from 'ant-design-vue';
  import TagItem from './TagItem.vue';
  import { OtdVirtualScroll } from '/@/components/OtdVirtualScroll';
  import { TagDto } from '../types';
  import { useI18n } from '/@/hooks/web/useI18n';

  const props = defineProps({
    data: {
      type: Array as PropType<TagDto[]>,
      default: () => [],
    },
    direction: {
      type: String as PropType<'horizontal' | 'vertical'>,
      default: 'horizontal',
    },
    className: {
      type: String,
      default: undefined,
    },
    maxTagCount: {
      type: Number,
      default: undefined,
    },
    expendAll: {
      type: Boolean,
      default: false,
    },
    isVirtual: {
      type: Boolean,
      default: false,
    },
    hideAction: {
      type: Boolean,
      default: false,
    },
    closeable: {
      type: Boolean,
      default: false,
    },
  });

  const { t } = useI18n();
  // 展示数据
  const showData = computed(() => {
    if (typeof props.maxTagCount === 'number') {
      return props.data.slice(0, props.maxTagCount);
    }
    return props.data;
  });

  // 隐藏数据
  const hideContent = computed(() => {
    if (typeof props.maxTagCount === 'number') {
      return props.data.slice(props.maxTagCount);
    }
    return [];
  });

  // 隐藏数据文本
  const hideContentText = computed(() => {
    return hideContent.value.map((item) => item.tagName);
  });
</script>
<style lang="less" scoped>
  .otd-tags-content {
    max-width: 100%;
    .placeholder-hover {
      font-size: 12px;
      > span:nth-of-type(1) {
        font-size: 14px;
      }
    }
  }
  .min-w-0 {
    min-width: 0px;
  }
  .more-num {
    padding: 0 4px;
    border-radius: 8px;
    color: var(--otd-basic-bg);
    background-color: var(--otd-help-color);
    font-size: 14px;
    line-height: 24px;
  }
</style>
