<template>
  <div class="otd-upload-file">
    <div class="otd-upload-file__title-box">
      <div class="otd-upload-file__title-box__title">{{ headerText }}</div>
      <uploadAttachment id="uploadAttachment" v-bind="$props" :file-list="tableFileList" @change="handleFileChange" />
    </div>

    <div class="otd-upload-file__body">
      <template v-if="tableFileList.length > 0">
        <OtdTable :data-source="tableFileList" :scroll="{ y: 360 }" :pagination="false" @register="register" />
      </template>

      <div v-else class="otd-upload-file__body__btn-box">
        <div class="placeholder-hover otd-action-btn" @click="handleUpload">
          <i class="otdIconfont otd-icon-add-2"></i>
          <span>{{ props.placeholder || t('common.add') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
  import type { UploadFile } from 'ant-design-vue';
  import { getProps, getEmits } from './props';
  import { OtdTable, useTable } from '/@/components';
  import { useUploadFile } from './useUploadFile';
  import uploadAttachment from './components/UploadAttachment.vue';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const props = defineProps(getProps());
  const emit = defineEmits(getEmits());
  const { columns, tableFileList } = useUploadFile();

  function handleFileChange(list: UploadFile[]) {
    emit('update:fileList', list);
  }

  function handleUpload() {
    const btn = document.querySelector('#uploadAttachment') as HTMLElement;
    btn.click();
  }

  const [register] = useTable(
    Object.assign(
      {
        columns: columns.concat(props.tableColumn),
        bordered: true,
        rowKey: 'key',
      },
      { ...props.allocation },
    ),
  );
</script>
<style lang="less" scoped>
  .otd-upload-file {
    &__title-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
      &__title {
        color: var(--otd-header-text);
        font-size: 14px;
      }
    }
    &__body {
      :deep(.ant-progress) {
        margin: 0;
      }
      &__btn-box {
        position: relative;
        .otd-action-btn {
          width: 100%;
        }
      }
    }
  }
</style>
