<template>
  <BasicModal
    v-bind="$attrs"
    :mask="false"
    :mask-closable="false"
    :min-height="600"
    :footer="null"
    :after-close="closeConnect"
    @register="register"
  >
    <template #title>
      <div class="otd-question-modal__title">
        <BasicIcon icon="otd-ai|svg" :size="26" />
        <span>{{ title ?? t('layout.askAi') }}</span>
      </div>
    </template>
    <div class="otd-question-modal" v-loading="loading">
      <div
        class="otd-question-answer-item"
        :class="{ 'is-right': item.userInfo }"
        v-for="(item, index) in chatHistory"
        :key="index"
      >
        <div class="otd-question-answer-item__userInfo">
          <img :src="item.userInfo.avatar" alt="" v-if="item.userInfo" />
          <BasicIcon icon="otd-ai|svg" :size="40" v-else />
        </div>
        <div class="otd-question-answer-item__content">
          <h2>{{ item.userInfo?.name ?? 'AI' }}</h2>
          <div
            class="otd-question-answer-item__content-card"
            :class="{ 'is-entering': answerInfo.answering && answerInfo.index === index }"
            v-html="item.content"
          ></div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { PropType, reactive, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/BasicModal';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { BasicIcon } from '/@/components/BasicIcon';
  import { Recordable } from '/#/global';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
  import { getSummarizeTask } from '/@/components/OtdAiQuestion/src/useAiQuestion';
  import { useSignalRAi } from '/@/hooks/web/useSignalRAi';

  const props = defineProps({
    detail: { type: Object as PropType<Recordable> },
    title: { type: String },
  });

  type chatHistoryType = {
    userInfo?: {
      name: string;
      avatar: string;
    };
    content: string;
  };

  const chatHistory = reactive<chatHistoryType[]>([]);
  const { getGlobalProvide } = useGlobalConfig();
  const { getUserInfo, aiSocketUrl, logout } = getGlobalProvide();

  const { t } = useI18n();
  const questionContent = ref('');
  const loading = ref(false);
  const answerInfo = reactive({
    answering: false,
    index: 0,
  });
  const SummarizeTask = getSummarizeTask();

  const { startConnect, closeConnect } = useSignalRAi?.({
    socketUrl: aiSocketUrl?.(),
    token: getUserInfo?.token,
    logout,
    handler: (message, status) => {
      answerInfo.answering = status;
      const chat = chatHistory[answerInfo.index];
      if (chat) {
        chat.content += JSON.stringify(message).slice(1, -1).replace(/\\n/g, '<br/>').replace(/\\/, '');
      }
    },
  });
  const [register] = useModalInner(() => {
    questionContent.value = '';
    chatHistory.length = 0;
    loading.value = true;
    startConnect?.()
      .then(() => {
        chatHistory.push({
          userInfo: { name: getUserInfo!.realName, avatar: getUserInfo!.avatar },
          content: t('common.ai.summaryTask'),
        });
        createChatContent();
      })
      .finally(() => {
        loading.value = false;
      });
  });

  // 创建AI内容
  function createChatContent(index?: number) {
    if (index !== undefined) {
      chatHistory[index] = { content: '' };
    } else {
      chatHistory.push({ content: '' });
    }
    answerInfo.index = chatHistory.length - 1;
    answerInfo.answering = true;
    SummarizeTask?.(props.detail?.id)
      .catch(({ error }) => {
        chatHistory[answerInfo.index].content =
          error.message +
          `<a
            style="margin-left: 6px;"
            class='iconfont icon-caozuo-xunhuan1' 
            onclick='reloadAiRequest()' 
            title='${t('routes.taskMgmt.tryAgain')}'
          ><a>`;
      })
      .finally(() => {
        answerInfo.answering = false;
      });
  }

  (window as any).reloadAiRequest = function () {
    createChatContent(answerInfo.index);
  };
</script>
<style lang="less" scoped>
  .otd-question-modal {
    min-height: 100%;
    &__title {
      display: flex;
      align-items: center;
      column-gap: 4px;
    }
  }
  .otd-question-answer-item {
    display: flex;
    column-gap: 10px;
    &.is-right {
      flex-direction: row-reverse;
      h2 {
        text-align: right;
      }
    }
    & + & {
      margin-top: 10px;
    }
    &__userInfo {
      width: 30px;
      min-width: 30px;
      height: 30px;
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &__content {
      > h2 {
        margin-bottom: 4px;
        line-height: 20px;
        padding: 0 4px;
      }
      &-card {
        background-color: var(--otd-content-bg);
        border-radius: 8px;
        padding: 6px 10px;
        &.is-entering {
          &::after {
            content: '|';
            display: inline-block;
            animation: 0.8s blink step-end infinite;
          }
        }
      }
    }
  }

  @keyframes blink {
    0%,
    100% {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
  }
</style>
