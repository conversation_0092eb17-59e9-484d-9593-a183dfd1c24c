<template>
  <Popover
    :title="currentStatus?.title"
    :open="disabled ? false : undefined"
    trigger="click"
    placement="bottomLeft"
    overlay-class-name="otd-status-popover"
    @openChange="openChange"
  >
    <template #content>
      <div class="otd-status-subTitle">
        <slot name="sub-title" :option="currentStatus"></slot>
      </div>
      <div class="otd-status-steps" v-if="stepList">
        <Steps class="otd-status-step" size="small" :current="currentStep" progress-dot :items="stepList"> </Steps>
      </div>

      <Space wrap>
        <Button
          class="otd-status-btn"
          size="small"
          v-for="item in currentStatus?.actions"
          :key="item.id"
          :type="item.type ?? 'default'"
          @click="item.action({ item, status: currentStatus })"
        >
          {{ item.label }}
        </Button>
      </Space>
    </template>
    <div class="otd-data-bubble" :class="{ 'placeholder-disabled': disabled }" data-bubble>
      <StatusTrigger v-bind="$props" :value="value" :option="getOptions" :rotate="rotate" />
    </div>
  </Popover>
</template>
<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { Popover, Steps, Space, Button } from 'ant-design-vue';
  import { OtdStatusOptionType } from './type';
  import { TaskStatusEnum, useStatus } from './useStatus';
  import { getProps } from './props';
  import StatusTrigger from './StatusTrigger.vue';

  const props = defineProps(getProps());

  defineEmits(['update:value', 'change']);
  const { defaultOptions } = useStatus();

  const getOptions = computed<OtdStatusOptionType<TaskStatusEnum>[]>(() => {
    return props.options ?? defaultOptions;
  });

  function openChange(val: boolean) {
    if (props.disabled) return;
    rotate.value = val;
  }
  const rotate = ref<boolean>(false);
  const currentStatus = computed(() => {
    return unref(getOptions).find((item) => item.id === props.value);
  });

  const stepList = computed(() => {
    const list = unref(getOptions).filter((item) => {
      item.sort = item?.isProgress?.({ current: props.value as TaskStatusEnum });
      return item.sort;
    });

    return list.sort((last, next) => (last.sort ?? 0) - (next.sort ?? 0));
  });
  const currentStep = computed(() => {
    return stepList.value.findIndex((item) => item.id === props.value);
  });
</script>
<style lang="less" scoped>
  .otd-status-subTitle {
    margin-bottom: 9px;
    margin-top: 12px;
  }
  .otd-status-steps {
    margin-bottom: 13px;
    position: relative;
    width: 320px;
    height: 32px;

    .otd-status-step {
      position: absolute;
      left: -64px;
    }
    :deep(.ant-steps-item-content) {
      margin-top: 6px;
    }
    :deep(.ant-steps-small .ant-steps-item-tail)::after {
      width: calc(100% - 2px);
      height: 1px;
      margin-inline-start: -2px;
    }
    :deep(.ant-steps.ant-steps-dot.ant-steps-small .ant-steps-item-icon) {
      width: 6px;
      height: 5px;
      top: 1px;
    }
    :deep(.ant-steps-item-container) {
      cursor: default !important;
    }
    :deep(.ant-steps-item-title) {
      margin-left: 28px;
      cursor: default;
      font-size: 13px;
    }
  }
</style>
