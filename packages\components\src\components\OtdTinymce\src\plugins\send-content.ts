import tinymce, { Editor } from 'tinymce';
import { useI18n } from '/@/hooks/web/useI18n';
import { debounce } from 'lodash-es';

const editorEventCallback = function (editor: Editor, button) {
  return debounce(() => {
    const content = editor.getContent({ format: 'html' }); // 获取编辑器中的文本内容
    if (content.trim() === '') {
      // 判断内容是否为空
      button.setDisabled(true); // 如果为空，禁用按钮
    } else {
      button.setDisabled(false); // 如果不为空，启用按钮
    }
  }, 300);
};

tinymce.PluginManager.add('send', function (editor) {
  const { t } = useI18n();
  const buttonAction: {
    send: any | undefined;
  } = {
    send: undefined,
  };
  editor.on('keydown', function (e) {
    if (e.shiftKey && e.keyCode === 13) {
      editor.fire('send', buttonAction.send);
      e.preventDefault();
    }
  });

  editor.ui.registry.addButton('send', {
    tooltip: `${t('common.send')} (Shift + Enter)`,
    icon: 'redo',
    disabled: true,
    onAction: function (button) {
      editor.fire('send', button);
    },
    onSetup: function (button) {
      buttonAction.send = button;
      editor.on('input', editorEventCallback(editor, button));
      editor.on('paste', editorEventCallback(editor, button));
      editor.on('ExecCommand', editorEventCallback(editor, button));
      /* onSetup should always return the unbind handlers */
      return function () {
        editor.off('input', editorEventCallback(editor, button));
        editor.off('paste', editorEventCallback(editor, button));
        editor.off('ExecCommand', editorEventCallback(editor, button));
      };
    },
  });
});
