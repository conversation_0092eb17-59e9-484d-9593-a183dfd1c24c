import { computed, getCurrentInstance } from 'vue';
import { DefaultCardFields } from './props';
import { OtdHistoryCardPropsType } from './type';
import { ComponentInternalInstance } from 'vue';

export function useHistoryCard() {
  const { props, emit } = getCurrentInstance() as ComponentInternalInstance & { props: OtdHistoryCardPropsType };
  const getFields = computed(() => ({ ...DefaultCardFields, ...props.fields }));

  function handleClickItem(data) {
    emit('click-item', data);
  }
  return {
    getFields,
    handleClickItem,
  };
}
