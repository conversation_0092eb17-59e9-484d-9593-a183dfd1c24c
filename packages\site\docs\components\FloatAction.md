## 悬浮操作

<demo src="../demo/FloatAction/basic.vue" title="悬浮操作"></demo>

## 属性

| 参数    | 说明                             | 类型             | 可选值 | 默认值 | 版本 |
| ------- | -------------------------------- | ---------------- | ------ | ------ | ---- |
| actions | 操作列表（第一个操作会永久固定） | MoreActionItem[] | --     | --     | 1.0  |

### <span id='actions'>Actions</span>

| 参数  | 说明 | 类型       | 可选值 | 默认值 | 版本 |
| ----- | ---- | ---------- | ------ | ------ | ---- |
| pin   | 固定 | boolean    | --     | false  | 1.0  |
| badge | 角标 | ()=>number | --     | --     | 1.0  |

其他属性[参考](/components/MoreAction.html#actions)
