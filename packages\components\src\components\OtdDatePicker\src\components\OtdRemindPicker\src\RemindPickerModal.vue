<template>
  <BasicModal
    :title="t('common.RemindPicker.custom')"
    :min-height="100"
    :height="300"
    :width="480"
    :can-fullscreen="false"
    :after-close="handleAfterClose"
    :cancelBubble="true"
    wrapClassName="otd-repeat-model"
    centered
    @register="register"
    @ok="handleOk"
  >
    <Form ref="formRef" :model="remindForm">
      <div class="otd-remind__form-grid">
        <FormItem class="otd-remind__form-item" :label="t('common.RemindPicker.reminderRule')" name="days">
          <InputNumber class="otd-remind-select" v-model:value="remindForm.days" :controls="false" :min="0" :max="30">
            <template #addonAfter>
              <span>{{ t('common.repeatPicker.days') }}</span>
            </template>
          </InputNumber>
        </FormItem>
        <FormItem class="otd-remind__form-item" name="time">
          <TimePicker
            v-model:value="remindForm.time"
            class="otd-remind-select"
            :placeholder="t('common.time')"
            :dropdownStyle="{ minWidth: '160px' }"
          />
        </FormItem>
      </div>
    </Form>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { Form, FormItem, InputNumber } from 'ant-design-vue';
  import { BasicModal, useModalInner } from '/@/components/BasicModal';
  import { reactive, ref, unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import TimePicker from '../../TimePicker.vue';
  import { getTipStringToData } from './useRemindPicker';

  const emit = defineEmits(['register', 'ok']);
  const { t } = useI18n();

  const [register] = useModalInner(({ value }) => {
    if (!value) return;
    const [days, time] = getTipStringToData(value)!;
    remindForm.days = days;
    remindForm.time = time;
  });
  const remindForm = reactive({
    days: 0,
    time: '18:00',
  });

  const formRef = ref();
  function handleAfterClose() {
    unref(formRef).resetFields();
    return Promise.resolve();
  }

  function handleOk() {
    emit('ok', unref(remindForm));
  }
</script>
<style lang="less" scoped>
  .otd-remind {
    &__form-grid {
      display: flex;
      column-gap: 10px;
      :deep(> .ant-form-item):first-of-type {
        flex: 1;
      }
    }
    &__form-item {
      margin-bottom: 12px;
      :deep(.ant-form-item-control-input-content) {
        justify-content: flex-end;
      }
    }
    &-select {
      width: 120px !important;
    }
  }
</style>
