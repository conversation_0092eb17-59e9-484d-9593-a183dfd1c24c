<template>
  <div class="otd-basic-table-cell" :class="getCellClass" :style="getCssStyle">
    <slot name="indent"></slot>
    <template v-if="column[render]">
      <div
        class="otd-arrow"
        :class="{
          'otdIconfont otd-icon-a-Statusdefault2': record[childrenColumnName],
          'otd-arrow-active': expanded,
        }"
        v-if="showExpand"
        @click="handleSetExpand"
      ></div>
      <CustomRender :component="column[render]" :record="record" :column="column" :groupData="groupData" />
    </template>
    <slot v-else></slot>
    <div
      v-if="isHeader && column.resizable"
      class="otd-basic-table-cell__resize"
      :class="{ 'is-move': isMoveResize }"
      :style="{ left: column.width && `${column.width}px` }"
      @mousedown="handleResizeDown"
    >
    </div>
  </div>
</template>
<script lang="ts" setup>
  import type { PropType } from 'vue';
  import type { TableColumnPropsType } from '/@/components/OtdTable/src/OtdTable';
  import { CustomRender } from '/@/utils/domUtils';
  import { useBasicTableCell } from './useBasicTableCell';
  import { getEmits, getProps } from '../../../props';
  import { Recordable } from '/#/global';

  defineProps({
    ...getProps(),
    record: {
      type: Object as PropType<Recordable>,
      default: () => ({}),
    },
    render: {
      type: String as PropType<'customRender' | 'headerRender'>,
      default: 'customRender',
    },
    column: {
      type: Object as PropType<TableColumnPropsType>,
      default: () => ({}),
    },
    groupData: {
      type: Object as PropType<Recordable>,
    },
    showExpand: {
      type: Boolean,
      default: false,
    },
    expanded: {
      type: Boolean,
      default: false,
    },
    isHeader: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(getEmits());

  const { getCssStyle, getCellClass, handleSetExpand, isMoveResize, handleResizeDown } = useBasicTableCell();
</script>
<style lang="less" scoped>
  .otd-basic-table {
    &-cell {
      position: relative;
      padding: 14px 16px;
      background-color: var(--otd-basic-bg);
      line-height: 1;
      vertical-align: top;
      user-select: none;
      &.is-expand {
        .otd-arrow {
          margin-right: 6px;
          margin-left: -16px;
          margin-top: 2px;
        }
      }
      &__resize {
        position: absolute;
        left: 100%;
        top: 50%;
        transform: translate(-100%, -50%);
        padding: 0 4px;
        cursor: col-resize;
        z-index: 2;
        &::before {
          content: '';
          display: block;
          width: 1px;
          height: 18px;
          margin-right: 2px;
          background-color: var(--otd-disabled-color);
        }
        &.is-move,
        &:hover {
          &::before {
            width: 4px;
            height: 24px;
            margin-right: 0;
            border-radius: var(--otd-small-radius);
            background-color: var(--otd-primary-text);
          }
        }
      }
    }
    &__main {
      max-width: 55cqw;
      padding-left: 56px;
      position: sticky;
      left: 0;
      z-index: 2;
    }
  }
</style>
