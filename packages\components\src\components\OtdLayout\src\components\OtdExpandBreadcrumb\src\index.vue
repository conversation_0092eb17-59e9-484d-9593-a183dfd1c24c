<template>
  <Tooltip :title="getTitle" placement="bottom" :mouseEnterDelay="0.5">
    <span class="otd-layout-action otd-expand-breadcrumb" @click="handleExpand">
      <VerticalAlignBottomOutlined :style="{ fontSize: '20px' }" v-if="!isShowBreadcrumb" />
      <VerticalAlignTopOutlined :style="{ fontSize: '20px' }" v-else />
    </span>
  </Tooltip>
</template>
<script lang="ts" setup name="OtdFullScreen">
  import { computed, unref } from 'vue';
  import { Tooltip } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { VerticalAlignBottomOutlined, VerticalAlignTopOutlined } from '@ant-design/icons-vue';
  import { createLocalStorage } from '/@/utils/cache';
  import { Show_Breadcrumb } from '/@/setting';

  const props = defineProps({
    expand: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['update:expand']);

  const ls = createLocalStorage();

  const isShowBreadcrumb = computed({
    get: () => props.expand,
    set: (expand) => {
      ls.set(Show_Breadcrumb, expand);
      emit('update:expand', expand);
    },
  });

  const { t } = useI18n();

  const getTitle = computed(() => {
    return unref(isShowBreadcrumb) ? t('layout.header.collapseHeader') : t('layout.header.expandHeader');
  });

  function handleExpand() {
    isShowBreadcrumb.value = !unref(isShowBreadcrumb);
  }
</script>

<style lang="less" scoped>
  .otd-expand-breadcrumb {
    .otdIconfont {
      font-size: 16px;
    }
  }
</style>
