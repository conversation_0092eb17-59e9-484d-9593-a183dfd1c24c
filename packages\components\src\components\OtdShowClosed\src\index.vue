<template>
  <Dropdown
    trigger="click"
    :align="PublicAlignConfig"
    :overlayStyle="{ minWidth: '150px' }"
    :disabled="options.length <= 1"
  >
    <div class="otd-show-closed__trigger" :class="{ 'otd-radius-border': bordered }" @click="handleSelectClosed">
      <Checkbox :checked="isChecked">
        <span class="otd-show-closed__trigger-text" @click.stop>{{ placeholder || t('common.showClosed.title') }}</span>
      </Checkbox>
      <CloseOutlined :style="{ fontSize: '12px' }" v-if="isChecked" @click.stop="handleCancelClosed" />
    </div>
    <template #overlay v-if="options.length > 1">
      <Menu>
        <MenuItem v-for="option in options" :key="option.value">
          <div
            class="otd-show-closed__item otd-box-left"
            @click.stop="handleChangeItem(value[option.value], option.value)"
          >
            <span>{{ option.label }}</span>
            <Switch :checked="value[option.value]" size="small" />
          </div>
        </MenuItem>
      </Menu>
    </template>
  </Dropdown>
</template>
<script lang="ts" setup>
  import { Checkbox, Dropdown, Menu, MenuItem, Switch } from 'ant-design-vue';
  import { computed, PropType, ref, unref } from 'vue';
  import { LabelValueOptions, Recordable } from '/#/global';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { CloseOutlined } from '@ant-design/icons-vue';
  import { PublicAlignConfig } from '/@/setting';

  const props = defineProps({
    value: {
      type: Object as PropType<Recordable<boolean>>,
      required: true,
    },
    options: {
      type: Array as PropType<LabelValueOptions>,
      default: () => [],
    },
    bordered: {
      type: Boolean,
    },
    placeholder: {
      type: String,
    },
  });

  const emit = defineEmits(['update:value', 'change', 'clear']);

  const isOpenDropdown = ref(false);

  const { t } = useI18n();
  const isChecked = computed(() => Object.values(props.value).some(Boolean));

  function handleSelectClosed() {
    if (props.options.length > 1) return;
    if (props.options.length <= 0) return;
    const item = props.options[0];
    if (props.options.length === 1) {
      props.value[item.value] = !props.value[item.value];
      return emit('change', props.value[item.value], item.value);
    } else {
      isOpenDropdown.value = !isOpenDropdown.value;
    }
    if (unref(isChecked)) return;
    props.value[item.value] = true;
    emit('change', true, item.value);
  }
  function handleCancelClosed() {
    for (const key in props.value) {
      props.value[key] = false;
    }
    emit('clear');
  }
  function handleChangeItem(checked, value: string) {
    props.value[value] = !checked;
    emit('change', props.value[value], value);
  }
</script>
<style lang="less" scoped>
  .otd-show-closed {
    &__trigger {
      line-height: 1;
      position: relative;
      width: fit-content;
      &-text {
        padding: 8px;
        margin-inline: -8px;
        white-space: nowrap;
      }
      .ant-checkbox-wrapper {
        line-height: 1;
        display: flex;
        align-items: center;
      }
      .anticon-close {
        position: absolute;
        right: 4px;
        top: 50%;
        transform: translateY(-50%);
        background-color: var(--otd-basic-bg);
        cursor: pointer;
        display: none;
        padding: 2px;
      }
      &:hover {
        .anticon-close {
          display: block;
        }
      }
      &.ant-dropdown-open {
        .anticon-close {
          display: none;
        }
      }
    }
    &__item {
      justify-content: space-between;
      margin: -5px -12px;
      padding: 5px 12px;
    }
  }
</style>
