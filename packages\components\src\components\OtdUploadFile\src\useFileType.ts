import Zip from '/@/assets/images/icon/zip.png';
import Pic from '/@/assets/images/icon/pic.png';
import Pdf from '/@/assets/images/icon/pdf.png';
import Txt from '/@/assets/images/icon/txt.png';
import Excel from '/@/assets/images/icon/excel.png';
import Ppt from '/@/assets/images/icon/ppt.png';
import Word from '/@/assets/images/icon/word.png';
import Unknown from '/@/assets/images/icon/unknown.png';

export const FileIconMap = {
  // 压缩包
  zip: Zip,
  '7z': Zip,
  rar: Zip,
  // 图片
  png: Pic,
  jpg: Pic,
  jpeg: Pic,
  svg: Pic,
  gif: Pic,
  // PDF
  pdf: Pdf,
  // 文本
  txt: Txt,
  // Excel
  xlsx: Excel,
  xls: Excel,
  // PPT
  ppt: Ppt,
  pptx: Ppt,
  // Word
  dot: Word,
  docx: Word,
  // 未知
  unknown: Unknown,
};

export function useFileType() {
  /**
   * 文件类型转文件icon图片
   * @param fileName 文件名称带后缀的
   * @returns
   */
  function fileTypeToIcon(fileName) {
    const reg = /.([a-zA-Z]+)$/;
    let exe = '';
    fileName.replace(reg, (_value, exeName) => (exe = exeName));
    return FileIconMap[exe?.toLowerCase()] || FileIconMap['unknown'];
  }
  return {
    fileTypeToIcon,
  };
}
