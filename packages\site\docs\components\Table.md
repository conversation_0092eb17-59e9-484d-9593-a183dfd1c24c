# 表格

<p style="font-size:26px">代码演示</p>

## 基础表格

<demo src="../demo/Table/basic.vue" title="基础表格"></demo>

## 设置表格

<demo src="../demo/Table/settingTable.vue" title="设置表格"></demo>

## 边框表格

<demo src="../demo/Table/borderTable.vue" title="边框表格"></demo>

## 嵌套子表格

<demo src="../demo/Table/expandTable.vue" title="嵌套子表格"></demo>

## 树形嵌套子表格

<demo src="../demo/Table/expandTreeTable.vue" title="树形嵌套子表格"></demo>

## 分组表格

<demo src="../demo/Table/groupTable.vue" title="分组表格" desc="对表格内容进行分组"></demo>

## 属性

> ### Table

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| formBorder | 表单是否显示边框 | Boolean |  | `true` | 1.0 |
| showIndexColumn | 是否展示序号列 | Boolean |  | `false` | 1.0 |
| indexColumnProps | 序号列属性，参考列配置 | Object |  |  | 1.0 |
| actionColumn | 操作列属性，参考列配置 | Object |  |  | 1.0 |
| summary | 总结栏参数 | Boolean |  |  | 1.0 |
| canResize | 自适应高度 | Boolean |  | `false` | 1.0 |
| showTableSetting | 是否展示表格设置 | Boolean |  | `false` | 1.0 |
| settingConfig | 表格设置配置 | Array |  |  | 1.0 |
| formConfig | 表格表单配置 | Object |  |  | 1.0 |
| remoteRequest | 表格远程数据接口 | () => Promise<{item:Recordable[],totalCount:number}> |  |  | 1.0 |

其他属性请参考[Table](https://next.antdv.com/components/table-cn#api)组件

> ### GroupTable

| 参数     | 说明           | 类型   | 可选值 | 默认值    | 版本 |
| -------- | -------------- | ------ | ------ | --------- | ---- |
| groupKey | 分组数据字段   | String |        | groupData | 1.0  |
| height   | 分组表格总高度 | String |        |           | 1.0  |
| width    | 分组表格总宽度 | String |        | 100%      | 1.0  |

## 插槽

> ### GroupTable

| 参数          | 说明                   | 类型 | 版本 |
| ------------- | ---------------------- | ---- | ---- |
| group-header  | 分组 header 插槽       | Slot | 1.0  |
| group-footer  | 分组 footer 插槽       | Slot | 1.0  |
| expend-footer | 展开行底部 footer 插槽 | Slot | 1.0  |
