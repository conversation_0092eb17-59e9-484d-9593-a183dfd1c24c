<template>
  <Popover
    v-model:open="visible"
    overlay-class-name="otd-popover in-tab"
    trigger="click"
    placement="bottomRight"
    :z-index="20"
    @open-change="handleOpenChange"
  >
    <Badge :count="getNotifyTotal" :overflow-count="999">
      <div class="otd-layout-action">
        <i class="otdIconfont otd-icon-iconamoon_notification" id="notifyTip"></i>
      </div>
    </Badge>
    <template #content>
      <Tabs class="otd-notification-tab otd-tab no-bottom" v-model:activeKey="activeKey">
        <TabPane v-for="item in tabListData" :key="item.key">
          <template #tab>
            <div class="otd-box-left" style="column-gap: 4px">
              <span>{{ t(item.name) }}</span>
              <span>{{ getNotifyCount[MessageKeyMap[item.key]] }}</span>
            </div>
          </template>
          <NoticeList
            :type="item.key"
            :list="tabListDataMap[item.key].items"
            :total="tabListDataMap[item.key].totalCount"
            :loading="loading"
            :page-size="notifyPageSize"
            @load-more="(pageIndex, pageSize) => handleLoad({ pageIndex, pageSize, messageType: item.key })"
          />
        </TabPane>
      </Tabs>
    </template>
  </Popover>
</template>
<script lang="ts" setup>
  import { reactive, ref, onMounted } from 'vue';
  import { Popover, Badge, Tabs, TabPane } from 'ant-design-vue';
  import NoticeList from './NoticeList.vue';
  import { useNotifyStorage } from '/@/storage/notifyStorage';
  import { tabListData, MessageType } from './data';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
  import { NotifyParamsType, OtdListRequestType } from '/@/components';
  import { MessageKeyMap } from '/@/hooks/web/useSignalR';

  const props = defineProps({
    pageSize: {
      type: Number,
      default: 5,
    },
  });

  const { t } = useI18n();

  const { getNotifyTotal, getNotifyCount, setNotify } = useNotifyStorage();
  const visible = ref(false);
  const activeKey = ref<MessageType>(MessageType.Common);
  const { getGlobalProvide } = useGlobalConfig();
  const { notificationRequest, notifyPageSize, getNotifyCountRequest } = getGlobalProvide();
  const { Common, BroadCast, Custom } = MessageType;
  const loading = ref(false);

  const tabListDataMap = reactive<Record<MessageType, OtdListRequestType>>({
    [Common]: { items: [], totalCount: 0 },
    [BroadCast]: { items: [], totalCount: 0 },
    [Custom]: { items: [], totalCount: 0 },
  });

  async function handleLoad(params: NotifyParamsType) {
    if (!notificationRequest) return;
    tabListDataMap[params.messageType] = await loadNotifyContent(params);
  }
  // 打开
  function handleOpenChange(data) {
    if (!notificationRequest || !data) return;
    const dom = document.querySelector('#notifyTip');
    dom?.classList.remove('is-flash');
    const page = { pageIndex: 1, pageSize: notifyPageSize ?? props.pageSize };
    const notifyMap = {
      [Common]: async () => (tabListDataMap[Common] = await loadNotifyContent({ ...page, messageType: Common })),
      [BroadCast]: async () =>
        (tabListDataMap[BroadCast] = await loadNotifyContent({ ...page, messageType: BroadCast })),
    };
    tabListData.map((item) => {
      notifyMap[item.key]();
    });
  }

  function loadNotifyContent(params: NotifyParamsType): Promise<OtdListRequestType> {
    loading.value = true;
    if (!notificationRequest) return Promise.resolve({ items: [], totalCount: 0 });
    return notificationRequest(params).finally(() => {
      loading.value = false;
    });
  }
  // 获取通知数量
  function broadCastCount() {
    if (!getNotifyCountRequest) return;
    getNotifyCountRequest().then((res) => {
      setNotify(res);
    });
  }

  onMounted(() => {
    broadCastCount();
  });
</script>
<style lang="less" scoped>
  .otd-notification-tab {
  }
  .ant-badge {
    font-size: 18px;

    :deep(.ant-badge-multiple-words) {
      padding: 0 4px;
    }
  }
</style>
