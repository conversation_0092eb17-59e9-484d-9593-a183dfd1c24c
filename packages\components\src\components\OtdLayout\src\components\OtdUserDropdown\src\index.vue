<template>
  <Dropdown trigger="click" placement="bottomRight" overlayClassName="otd-dropdown">
    <div class="otd-user-dropdown">
      <OtdAvatar class="mr-12px" size="26px" :url="info.avatar" />
    </div>
    <template #overlay>
      <Menu>
        <UserInformation class="otd-user-dropdown__user" :userInfo="info" :upload-avatar="uploadAvatar" />
        <template v-for="(menu, index) in dropdownAction" :key="menu.id === 'divider' ? index : menu.id">
          <template v-if="menu.id === 'divider'">
            <MenuDivider />
          </template>
          <template v-else-if="menu.customRender">
            <CustomRender :component="menu.customRender" />
          </template>
          <template v-else>
            <MenuItem :key="menu.id" @click.stop="menu.action?.(menu)">
              <template #icon>
                <i class="otdIconfont" :class="[menu.icon]"></i>
              </template>
              <span>{{ menu.name }}</span>
            </MenuItem>
          </template>
        </template>
      </Menu>
    </template>
  </Dropdown>
  <OtdLockModal :user-info="info" @register="reigsterLock" />
  <ResetPassword @register="reigsterPassword" />
</template>
<script lang="tsx" setup>
  import { Dropdown, Menu, MenuItem, MenuDivider } from 'ant-design-vue';
  import { computed, PropType } from 'vue';
  import { OtdAvatar } from '/@/components/OtdAvatar';
  import UserInformation from './components/UserInformation.vue';
  import ResetPassword from './components/ResetPassword.vue';
  import { OtdLockModal } from '/@/components/OtdLock';
  import { useModal } from '/@/components/BasicModal';
  import { ActionItem } from '/@/utils/types';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { Recordable } from '/#/global';
  import { CustomRender } from '/@/utils/domUtils';
  import { OtdLanguage } from '../../../components/OtdLanguage';
  import { OtdProjectConfig } from '../../../components/OtdProjectConfig';
  import { OtdHelp } from '../../../components/OtdHelp';
  import { OtdChangeLog } from '../../OtdChangeLog';

  const props = defineProps({
    info: {
      type: Object as PropType<Recordable>,
      default: () => {},
    },
    uploadAvatar: {
      type: Function as PropType<(data) => Promise<any>>,
    },
    userDropdownAction: {
      type: Array as PropType<ActionItem[]>,
      default: () => [],
    },
  });

  defineEmits(['upload-avatar']);

  const { t } = useI18n();

  const [reigsterLock, { openModal: openLockModal }] = useModal();
  const [reigsterPassword, { openModal: openPasswordModal }] = useModal();

  const dropdownAction = computed(() => {
    const actions: ActionItem[] = [
      // 国际化
      {
        id: 'language',
        customRender: () => (
          <MenuItem>
            <OtdLanguage reload showText is-large={false} />
          </MenuItem>
        ),
      },
      // 帮助
      { id: 'help', customRender: () => <OtdHelp /> },
      // 更新日志
      { id: 'changelog', customRender: () => <OtdChangeLog /> },
      // 设置
      { id: 'setting', customRender: () => <OtdProjectConfig /> },
      { id: 'divider' },
      // 重置密码
      {
        id: 'resetPassword',
        name: t('layout.header.forgetFormTitle'),
        icon: 'otd-icon-a-catczmm',
        action: () => openPasswordModal(true),
      },
      // 锁屏
      {
        id: 'lock',
        name: t('layout.header.tooltipLock'),
        icon: 'otd-icon-a-catlocksize24',
        action: () => openLockModal(true),
      },
    ];
    return actions.concat(props.userDropdownAction);
  });
</script>
<style lang="less" scoped>
  .otd-user-dropdown {
    display: flex;
    align-items: center;
    &__user {
      margin-bottom: 8px;
    }
  }
</style>
