// 多选checkbox样式
.ant-checkbox-wrapper {
  &:not(.ant-checkbox-wrapper-disabled):hover {
    .ant-checkbox-checked:not(.ant-checkbox-disabled) .ant-checkbox-inner {
      background-color: var(--otd-icon-text);
    }

    .ant-checkbox-inner {
      border-color: var(--otd-icon-text);
    }
  }

  .ant-checkbox {
    &:hover {
      &:not(.ant-checkbox-disabled) {
        .ant-checkbox-inner {
          border-color: var(--otd-icon-text);
        }
      }
    }

    &.ant-checkbox-indeterminate {
      .ant-checkbox-inner {
        &::after {
          background-color: var(--otd-icon-text);
        }
      }
    }

    &.ant-checkbox-checked,
    &.ant-checkbox-indeterminate.ant-checkbox-checked {
      .ant-checkbox-inner {
        background-color: var(--otd-icon-text);
        border-color: var(--otd-icon-text);

        &::after {
          top: 42%;
          left: 22%;
          border-width: 1px;
          width: 5px;
          height: 10px;
        }
      }
    }
    &.ant-checkbox-disabled {
      .ant-checkbox-inner {
        &::after {
          border-color: var(--otd-white-text);
        }
      }
    }

    &::after {
      border-width: 0;
    }
  }
}

// 单选radio样式
.ant-radio-wrapper {
  &:hover {
    .ant-radio-inner {
      border-color: var(--otd-icon-text);
    }
  }

  &.ant-radio-wrapper-checked {
    .ant-radio-inner {
      background-color: var(--otd-icon-text);
      border-color: var(--otd-icon-text);
    }
  }
}

// 分页样式
.ant-pagination {
  display: flex;
  align-items: center;
  position: relative;
  li.ant-pagination-prev[title] {
    border-top-left-radius: var(--otd-border-radius);
    border-bottom-left-radius: var(--otd-border-radius);
    padding-left: 8px;

    .ant-pagination-item-link {
      min-width: 35px;
    }
  }
  li.ant-pagination-next[title] {
    border-top-right-radius: var(--otd-border-radius);
    border-bottom-right-radius: var(--otd-border-radius);
  }
  &:not(.ant-pagination-simple) {
    .ant-pagination-prev,
    .ant-pagination-jump-prev,
    .ant-pagination-next,
    .ant-pagination-jump-next,
    .ant-pagination-item {
      background-color: var(--otd-basic-bg) !important;
      height: 54px;
      min-width: 43px;
      line-height: 34px;
      border-width: 0;
      padding: 10px 8px 10px 0;
      margin-inline-end: 0;
      border-radius: 0;
      order: 3;

      > a,
      .ant-pagination-item-link,
      .ant-pagination-item-link .ant-pagination-item-container .ant-pagination-item-ellipsis {
        color: var(--otd-primary-text);
        font-weight: bold;
        border-radius: var(--otd-border-radius);
      }

      &:not(.ant-pagination-item-active) {
        &:hover {
          background-color: var(--otd-basic-bg);
          > a {
            background-color: var(--otd-gray-hover);
          }
        }
      }
    }

    .ant-pagination-prev:hover .ant-pagination-item-link,
    .ant-pagination-next:hover .ant-pagination-item-link {
      background-color: var(--otd-gray-hover);
    }

    .ant-pagination-total-text {
      order: 1;
      margin-inline-end: 12px;
      color: var(--otd-basic-text);
      font-weight: b;
    }

    .ant-pagination-options {
      margin-left: 0;
      margin-right: auto;
      order: 2;
    }

    .ant-pagination-item-active {
      &:hover > a {
        color: var(--otd-white-text);
      }

      > a {
        background-color: var(--otd-primary-main);
        color: var(--otd-white-text);
      }
    }

    .ant-select-single .ant-select-selector {
      height: 36px;
      padding: 0 8px;

      .ant-select-selection-item {
        line-height: 34px;
      }
    }

    .ant-select-item {
      padding: 5px 8px;
    }

    .ant-select-arrow {
      margin-top: -5px;
      font-family: otdIconfont;
      color: var(--otd-icon-text);

      &::after {
        content: '\e64b';
        font-size: 12px;
        transform: scale(0.8);
      }

      .anticon-down {
        display: none;
      }
    }
  }
  &.ant-pagination-simple {
    li.ant-pagination-prev,
    li.ant-pagination-next {
      background-color: var(--otd-basic-bg);
      padding: 10px 8px;
      height: 54px;
      margin: 0;
      display: flex;
      align-items: center;
      min-width: 50px;
      .ant-pagination-item-link {
        height: 100%;
      }
    }
    .ant-pagination-simple-pager {
      margin: 0 8px;
      background-color: var(--otd-basic-bg);
      height: 54px;
      line-height: 54px;
      margin: 0;
      > input {
        height: 32px;
        width: 42px;
      }
    }
  }
}

.ant-select-selector {
  border-color: var(--otd-border-gray);

  .ant-select-selection-item {
    color: var(--otd-basic-text);
  }
  .ant-select-selection-placeholder {
    color: var(--otd-gray4-color);
  }
}

.ant-select-dropdown {
  .ant-select-item-option-selected {
    &:not(.ant-select-item-option-disabled) {
      background-color: var(--otd-gray-hover);
    }
  }
  .ant-select-item-option-active {
    &:not(.ant-select-item-option-disabled) {
      background-color: var(--otd-gray3-hover);
    }
  }
}

.ant-input {
  &::placeholder {
    color: var(--otd-gray4-color);
  }
}

// tab样式
.ant-tabs {
  &:not(.ant-tabs-left) {
    .ant-tabs-nav {
      margin-bottom: 0;

      &::before {
        border-color: var(--otd-border-color);
      }

      .ant-tabs-nav-wrap {
        .ant-tabs-nav-list {
          padding: 8px 10px;

          .ant-tabs-tab {
            padding: 7px 9px;
            line-height: 20px;

            & + .ant-tabs-tab {
              margin-left: 8px;
            }

            // .ant-tabs-tab-btn {
            //   color: var(--otd-primary-text);
            // }

            &.ant-tabs-tab-active {
              .ant-tabs-tab-btn {
                color: var(--otd-primary-text);
              }
            }
          }
        }
      }
    }
  }
}

// 菜单
.ant-menu-submenu.ant-menu-submenu-popup {
  background-color: inherit;
  &::before {
    inset-inline-start: -12px;
  }
  .ant-menu {
    padding: 5px 0;
    > li {
      position: relative;
      & + li {
        &::before {
          content: '';
          position: absolute;
          z-index: 2;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          width: calc(100% - 20px);
          height: 1px;
          border-top: 1px solid var(--otd-dropdown-border);
        }
      }
    }
    > li.ant-menu-item {
      border-radius: 0;
      width: 100%;
      padding: 0;
      padding-top: 1px;
      display: flex;
      background-color: transparent !important;
      .ant-menu-title-content {
        width: 100%;
        padding: 8px 22px;
        line-height: 22px;
      }
    }
    .ant-menu-item,
    .ant-menu-submenu-title {
      height: auto;
      line-height: 22px;
      padding: 8px 22px;
      border-radius: 0;
      margin: 0;
      &:hover .ant-menu-title-content {
        background-color: var(--otd-gray-hover);
      }
      &.ant-menu-item-selected .ant-menu-title-content {
        background-color: var(--otd-basic-active);
        color: var(--otd-basic-text);
      }
    }
    .ant-menu-submenu {
      padding-bottom: 0;
      .ant-menu-submenu-title {
        margin: 0;
        width: 100%;
      }
      .ant-menu-submenu-arrow {
        inset-inline-end: 10px;
        &::before,
        &::after {
          height: 1px;
        }
      }
    }
  }
}

// 弹窗
.ant-modal-wrap {
  .ant-modal {
    .ant-modal-content {
      padding: 0;
      .ant-modal-close {
        top: 16px;
        width: auto;
        height: auto;
        &:hover {
          background-color: unset;
        }
      }
      .ant-modal-header {
        padding: 20px 20px 0 20px;
        margin-bottom: 0;
      }
      .ant-modal-body {
      }
      .ant-modal-footer {
        margin-top: 0;
        padding: 0 20px 20px 20px;
      }
    }
  }
}

.ant-btn-link {
  color: var(--otd-primary-text);
}

.ant-form-item {
  .ant-form-item-control-input {
    min-height: 24px;
    .ant-form-item-control-input-content {
      display: inline-flex;
      > div {
        width: 100%;
      }
    }
  }
}

.ant-checkbox-group {
  white-space: normal;
}

.ant-tree {
  background-color: var(--otd-basic-bg);
}

.ant-tree {
  .ant-tree-switcher {
    .ant-tree-switcher-icon {
      display: inline-flex;
      vertical-align: -0.125em;
      font-size: 14px;
    }
  }
}

.ant-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  line-height: 18px;
  > span {
    display: inline-flex;
  }
  &.ant-btn-sm {
    height: 22px;
  }
}
.ant-progress {
  .ant-progress-bg {
    background-color: var(--otd-primary-main);
  }
}
.driver-popover {
  background-color: var(--otd-basic-bg);
  color: var(--otd-basic-text);
  max-width: 800px;
  .driver-popover-footer {
    .driver-popover-progress-text {
      color: var(--otd-basic-text);
    }
    .driver-popover-navigation-btns {
      .driver-popover-next-btn,
      .driver-popover-prev-btn {
        background-color: var(--otd-primary-color);
        color: var(--otd-body-text);
        border-width: 0;
        text-shadow: unset;
        border-radius: var(--otd-small-radius);
        padding: 4px 8px;
        font-size: 13px;
        &:hover {
          // background-color: var(--otd-menu-text);
          filter: opacity(0.8);
        }
      }
    }
  }
}
