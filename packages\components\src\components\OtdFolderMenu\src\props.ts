import type { DataNode, EventDataNode } from 'ant-design-vue/es/tree';
import type { FolderRequestType } from './type';
import type { MoreActionItem } from '/@/components/OtdMoreAction';
import type { PropType } from 'vue';

export const getProps = () => ({
  title: {
    type: String,
    default: null,
  },
  data: {
    type: Array as PropType<DataNode[]>,
    default: undefined,
  },
  expanded: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  selected: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  hideAction: {
    type: Boolean,
    default: false,
  },
  resource: {
    type: Boolean,
    default: false,
  },
  actions: {
    type: Array as PropType<MoreActionItem[]>,
    default: () => [],
  },
  loadData: {
    type: Function as PropType<(treeNode: EventDataNode) => Promise<any>>,
    default: undefined,
  },
  folderClick: {
    type: Boolean,
    default: true,
  },
  notRoot: {
    type: Boolean,
    default: false,
  },
  folderRequest: {
    type: Object as PropType<FolderRequestType>,
    required: true,
  },
});
