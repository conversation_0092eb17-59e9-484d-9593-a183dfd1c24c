<template>
  <span class="otd-menu-item-content" :class="{ 'is-only-icon': !showTitle }" :title="item?.meta?.title">
    <i class="otdIconfont" :class="[item?.meta?.icon]" v-if="item?.meta?.icon"></i>
    <span class="otd-menu-item-content__text" v-if="showTitle">{{ item?.meta?.title }}</span>
  </span>
</template>
<script lang="ts" setup>
import { itemProps } from "../props";

defineProps(itemProps());
</script>
<style lang="less" scoped>
.otd-menu-item-content {
  display: inline-flex;
  align-items: center;
  max-width: 240px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  &.is-only-icon {
    i {
      font-size: 24px;
    }
  }
  i {
    & + .otd-menu-item-content__text {
      margin-left: 4px;
    }
  }
}
</style>
