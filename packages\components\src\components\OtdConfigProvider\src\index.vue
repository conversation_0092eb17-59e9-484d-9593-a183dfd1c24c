<template>
  <ConfigProvider v-bind="$attrs">
    <slot></slot>
  </ConfigProvider>
</template>
<script lang="ts" setup>
  import { ConfigProvider } from 'ant-design-vue';
  import { PropType } from 'vue';
  import { OtdLayoutConfigType } from './types';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';

  const props = defineProps({
    layoutConfig: {
      type: Object as PropType<OtdLayoutConfigType>,
      default: () => ({}),
    },
  });

  const { setGlobalProvide } = useGlobalConfig();

  setGlobalProvide(props.layoutConfig);
</script>
<style lang="less" scoped></style>
