<template>
  <Popover trigger="click" placement="bottomRight" @open-change="handleVisibleChange">
    <div class="otd-data-bubble" data-bubble>
      <i class="otdIconfont otd-icon-lieshezhi"></i>
    </div>
    <template #content>
      <div class="otd-table-column-setting__popover">
        <div class="otd-table-column-setting__popover__search">
          <Input v-model:value="searchKeyword" size="small" allow-clear :placeholder="t('common.searchText')">
            <template #prefix>
              <i class="otdIconfont otd-icon-sousuo"></i>
            </template>
          </Input>
          <Button type="link" size="small" @click="resetColumns">{{ t('common.resetText') }}</Button>
        </div>
        <OtdScrollbar>
          <CheckboxGroup
            ref="columnListRef"
            v-model:value="checkedList"
            class="otd-table-column-setting__popover-group"
            @change="handleChangeColumn"
          >
            <div
              class="otd-table-column-setting__popover-item"
              v-for="column in curretnColumns"
              :key="getColumnKey(column)"
              v-show="column.title?.toString().includes(searchKeyword)"
            >
              <DragOutlined class="table-column-drag-icon" />
              <Checkbox :value="getColumnKey(column)" :disabled="column.disabledSetting">{{ column.title }}</Checkbox>
              <template v-if="!hideFixed">
                <Tooltip :title="t('common.table.settingFixedLeft')">
                  <VerticalRightOutlined
                    :disabled="!checkedList.includes(getColumnKey(column))"
                    @click="handleColumnFixed(column, 'left')"
                  />
                </Tooltip>
                <Divider type="vertical" />
                <Tooltip :title="t('common.table.settingFixedRight')">
                  <VerticalLeftOutlined
                    :disabled="!checkedList.includes(getColumnKey(column))"
                    @click="handleColumnFixed(column, 'right')"
                  />
                </Tooltip>
              </template>
            </div>
          </CheckboxGroup>
        </OtdScrollbar>
      </div>
    </template>
  </Popover>
</template>
<script lang="ts" setup>
  import { Popover, CheckboxGroup, Checkbox, Tooltip, Divider, Input, Button } from 'ant-design-vue';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { DragOutlined, VerticalRightOutlined, VerticalLeftOutlined } from '@ant-design/icons-vue';
  import { computed, nextTick, PropType, Ref, ref, unref, watch } from 'vue';
  import { TableColumnPropsType } from '../types';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getColumnKey } from '../hooks/useOtdTable';
  import { cloneDeep } from 'lodash-es';
  import Sortablejs from 'sortablejs';
  import { isNullAndUnDef } from '/@/utils/is';
  import { ComponentRef } from '/#/global';
  import { INDEX_COLUMN_FLAG } from '../const';

  const props = defineProps({
    columnOptions: {
      type: Array as PropType<TableColumnPropsType[]>,
      default: () => [],
    },
    hideFixed: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['change-column']);

  const checkedList = ref<string[]>([]);
  const { t } = useI18n();
  const searchKeyword = ref('');
  const curretnColumns = ref(cloneDeep(handleColumns())) as unknown as Ref<TableColumnPropsType[]>;

  let inited = false;
  const columnListRef = ref<ComponentRef>(null);
  const pinColumns = computed(() => props.columnOptions.filter((item) => item.hideSetting));

  function handleColumns() {
    return props.columnOptions.filter((item) => !item.hideSetting);
  }

  function handleChange() {
    emit('change-column', sortColumnsFixed(getFilterColumns()));
  }

  // 列排序
  function handleVisibleChange() {
    if (inited) return;
    nextTick(() => {
      const columnListEl = unref(columnListRef);
      if (!columnListEl) return;
      const el = columnListEl.$el as any;
      if (!el) return;
      // Drag and drop sort
      Sortablejs.create(unref(el), {
        animation: 500,
        delay: 400,
        delayOnTouchOnly: true,
        handle: '.table-column-drag-icon ',
        onEnd: (evt) => {
          const { oldIndex, newIndex } = evt;
          if (isNullAndUnDef(oldIndex) || isNullAndUnDef(newIndex) || oldIndex === newIndex) {
            return;
          }
          // Sort column
          let columns: TableColumnPropsType[] | null = cloneDeep(unref(curretnColumns));
          if (columns) {
            if (oldIndex > newIndex) {
              columns.splice(newIndex, 0, columns[oldIndex]);
              columns.splice(oldIndex + 1, 1);
            } else {
              columns.splice(newIndex + 1, 0, columns[oldIndex]);
              columns.splice(oldIndex, 1);
            }
            curretnColumns.value = columns;
            handleChange();
          }

          columns = null;
        },
      });
      inited = true;
    });
  }

  // 这是默认列选中
  function setColumnsDefault() {
    checkedList.value.length = 0;
    props.columnOptions.map((column) => {
      if (!column.defaultHidden) {
        checkedList.value.push(column.flag ?? (column.dataIndex as string));
      }
    });
  }

  // 获取过滤的列
  function getFilterColumns(data?: TableColumnPropsType[]) {
    const columns = data ?? unref(curretnColumns);
    let result: TableColumnPropsType[] = [];
    if (unref(checkedList).length > 0) {
      result = columns.filter((item) => unref(checkedList).includes(getColumnKey(item)));
    }
    const orderIndex = result.findIndex((item) => item.flag === INDEX_COLUMN_FLAG);
    if (orderIndex >= 0) {
      result.splice(orderIndex + 1, 0, ...unref(pinColumns));
      return result;
    } else {
      const data: TableColumnPropsType[] = [];
      return data.concat(unref(pinColumns)).concat(result);
    }
  }

  // 排序固定列数据
  function sortColumnsFixed(data) {
    const resultMap: Record<string, TableColumnPropsType[]> = {
      left: [],
      center: [],
      right: [],
    };
    data.map((item) => {
      if (!item.fixed) {
        resultMap.center.push(item);
      } else {
        resultMap[item.fixed].push(item);
      }
    });
    return resultMap.left.concat(resultMap.center.concat(resultMap.right));
  }

  // 固定列
  function handleColumnFixed(column: TableColumnPropsType, fixed: 'left' | 'right') {
    if (!unref(checkedList).includes(getColumnKey(column))) return;
    const isFixed = column.fixed === fixed ? false : fixed;
    column.fixed = isFixed;
    if (isFixed && !column.width) {
      column.width = 100;
    }
    handleChange();
  }

  // 选择列
  function handleChangeColumn() {
    searchKeyword.value = '';
    handleChange();
  }

  // 重置
  function resetColumns() {
    searchKeyword.value = '';
    curretnColumns.value = cloneDeep(handleColumns());
    setColumnsDefault();
    handleChange();
  }

  watch(
    () => props.columnOptions,
    () => {
      setColumnsDefault();
    },
    { immediate: true, deep: true },
  );
</script>
<style lang="less" scoped>
  .otd-table-column-setting {
    &__popover {
      margin: -12px;
      &__search {
        display: flex;
        align-items: center;
        padding: 10px 10px 4px 10px;
        border-bottom: 1px solid var(--otd-border-color);
        .ant-input-affix-wrapper {
          border-width: 0;
          box-shadow: unset;
          padding: 0;
        }
      }
      &-group {
        padding: 10px;
        padding-top: 0;
        display: flex;
        flex-direction: column;
        gap: 10px;
      }
      &-item {
        display: flex;
        align-items: center;
        user-select: none;
        .table-column-drag-icon {
          margin-right: 5px;
          cursor: move;
        }
        .ant-checkbox-wrapper {
          flex: 1;
        }
        .anticon[disabled='true'] {
          color: var(--otd-disabled-color);
          cursor: not-allowed;
        }
      }
      .scrollbar {
        height: 220px;
        widows: 280px;
        margin-top: 10px;
        margin-bottom: 10px;
      }
    }
  }
</style>
