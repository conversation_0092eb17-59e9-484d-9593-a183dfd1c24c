<template>
  <div style="height: 500px">
    <OtdBoard
      v-model:list="boardGroup"
      :loading="false"
      hoverTip="创建任务"
      deleteTip="删除分组后，分组内的任务将回到默认分组中"
      :action="action"
      @deleteGroup="deleteGroup"
      @createGroup="createGroup"
      @editGroup="editGroup"
      @moveCard="moveCard"
      @moveGroup="moveGroup"
    >
      <template #group-header>
        <!-- 可覆写分组header -->
      </template>
      <template #card>
        <div class="card">
          <OtdPriority></OtdPriority>
        </div>
      </template>
      <template #group-footer>
        <!-- 右侧的创建分组可覆写 -->
      </template>
    </OtdBoard>
  </div>
</template>
<script lang="ts" setup>
  import { OtdBoard, OtdPriority } from '@otd/otd-ui';
  import { message } from 'ant-design-vue';

  const boardGroup = [
    {
      extraProperties: {},
      id: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
      creationTime: '2023-05-02T16:08:32.730807',
      creatorId: null,
      lastModificationTime: '2023-05-05T13:15:35.793905',
      lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
      name: '默认分组',
      color: null,
      order: 0,
      isDefault: true,
      items: [{}],
    },
    {
      extraProperties: {},
      id: '3a0b63a0-f0f0-d11c-1825-c9453315b70e',
      creationTime: '2023-05-25T13:49:03.10509',
      creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
      lastModificationTime: '2024-05-20T14:22:30.252814',
      lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
      name: '测试阶段',
      color: null,
      order: 1,
      isDefault: false,
    },
  ];
  const action = [
    {
      id: 3,
      name: '操作扩展',
      icon: 'otd-icon-a-cateditsize24',
      action: (data: any) => {
        console.log(data);
      },
    },
  ];

  function moveGroup(obj) {
    message.info(`移动分组`);
    console.log('移动分组', obj);
  }
  function moveCard(obj) {
    message.info(`移动卡片`);
    console.log('移动卡片', obj);
  }
  function editGroup(obj) {
    message.info(`编辑分组：${obj.name}`);
    console.log('编辑分组', obj);
  }
  function createGroup(name) {
    message.info(`创建分组：${name}`);
  }
  function deleteGroup(id, index) {
    message.info(`删除分组：${id},下标为：${index}`);
  }
</script>
<style lang="less" scoped>
  .card {
    width: 280px;
    height: 192px;
    background-color: var(--otd-basic-bg);
    border-radius: 8px;
  }
</style>
