<template>
  <div class="otd-line-up">
    <div class="otd-line-up__header">
      <div class="otd-line-up__header-title"> <slot name="title" /> </div>
      <div class="otd-line-up__header-action">
        <slot name="action" v-if="isSelect">
          <OtdTaskSearch
            v-model:value="modelValue"
            :clearable="clearable"
            :before-change="beforeChange"
            mode="multiple"
            placement="bottomRight"
            destroy-tooltip-on-hide
            @change="handleChange"
          >
            <div class="otd-task-search__trigger placeholder-hover">
              <i class="otdIconfont otd-icon-add-2"></i>
              <span>{{ t('common.lineUp.addTaskTip') }}</span>
            </div>
          </OtdTaskSearch>
        </slot>
      </div>
    </div>
    <div class="otd-line-up__body">
      <OtdTaskSearch
        v-if="!isSelect"
        v-model:value="modelValue"
        :placeholder="t('common.lineUp.addTaskTip')"
        :clearable="clearable"
        :before-change="beforeChange"
        mode="multiple"
        destroy-tooltip-on-hide
        @change="handleChange"
      >
        <div class="otd-line-up__body-trigger otd-box-center">
          <i class="otdIconfont otd-icon-add-2"></i>
          <span>{{ getPlaceholder }}</span>
        </div>
      </OtdTaskSearch>
      <template v-if="isSelect">
        <Button v-if="page > 1" type="link" class="otd-line-up-arrow is-left" @click="setPage(-1)" />
        <div ref="contentRef" class="otd-line-up__body-content">
          <div class="otd-line-up__body-content__box" v-for="(group, index) in CurrentValue" :key="index">
            <div
              class="otd-line-up__body-content__item"
              v-for="(item, index) in group"
              :key="item.value"
              @click.stop="handleClickItem(item)"
            >
              <!-- <i class="otdIconfont otd-icon-tuozhuai"></i> -->
              <OtdStatusTrigger :value="item.status" is-text hide-text />
              <span class="otd-truncate">{{ item.label }}</span>
              <i v-if="clearable" class="otdIconfont otd-icon-a-catthicksize24" @click.stop="removeModelValue(index)" />
            </div>
          </div>
        </div>
        <Button v-if="page < CurrentValue.length" type="link" class="otd-line-up-arrow" @click="setPage(1)" />
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { getEmits, getProps } from './props';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { OtdTaskSearch, TaskOptionItemType } from '/@/components/OtdTaskSearch';
  import { OtdStatusTrigger } from '/@/components/OtdStatus';
  import { SearchOptionType } from '/@/components/BasicSearchPopover';
  import { Button } from 'ant-design-vue';

  const props = defineProps(getProps<TaskOptionItemType>());
  const emit = defineEmits(getEmits());

  const { t } = useI18n();
  const getPlaceholder = computed(() => props.placeholder ?? t('common.lineUp.addTip'));
  const modelValue = computed({
    get: () => props.value ?? [],
    set: (value) => {
      emit('update:value', value);
    },
  });

  const isSelect = computed(() => (unref(modelValue)?.length ?? 0) > 0);
  const page = ref(1);
  const chunkSize = 4;
  const CurrentValue = computed(() => {
    if (chunkSize <= 0) return [];
    const result: SearchOptionType[] = [];
    for (let i = 0; i < modelValue.value.length; i += chunkSize) {
      result.push(modelValue.value.slice(i, i + chunkSize));
    }
    page.value = Math.min(page.value, result.length);
    return result;
  });

  async function removeModelValue(index) {
    try {
      const seat = (unref(page) - 1) * chunkSize + index;
      const data = modelValue.value[seat];
      if (props.beforeChange) {
        const status = await props.beforeChange(unref(modelValue), data);
        if (!status) return;
      }
      modelValue.value.splice(seat, 1);
      handleChange(unref(modelValue), data);
    } catch (error) {}
  }

  const contentRef = ref<HTMLDivElement>();
  function setPage(count) {
    page.value += count;
    contentRef.value!.scrollLeft = ((contentRef.value?.clientWidth ?? 0) - 6) * (page.value - 1);
  }

  function handleClickItem(item) {
    emit('click-item', item);
  }

  function handleChange(...data) {
    emit('change', ...data);
  }

  function resetLineUp() {
    page.value = 1;
    modelValue.value = [];
  }

  defineExpose({
    reset: resetLineUp,
  });
</script>
<style lang="less" scoped>
  .otd-line-up {
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      line-height: 26px;
      .otd-task-search__trigger {
        font-size: 13px;
      }
    }

    &__body {
      position: relative;
      margin-top: 6px;
      &-trigger {
        color: var(--otd-header-text);
        border: 1px dashed var(--otd-border-gray);
        border-radius: var(--otd-default-radius);
        height: 38px;
        cursor: pointer;
        font-size: 12px;
        column-gap: 6px;
        .otdIconfont {
          font-size: 13px;
        }
        &:hover {
          color: var(--otd-primary-text);
          border-color: var(--otd-primary-text);
        }
      }
      &-content {
        display: flex;
        padding: 6px;
        background-color: var(--otd-disabled-bg);
        column-gap: 6px;
        border-radius: var(--otd-small-radius);
        overflow-x: hidden;

        &__box {
          display: flex;
          column-gap: 6px;
          width: 100%;
          min-width: 100%;
        }
        &__item {
          position: relative;
          flex: 1;
          background-color: var(--otd-basic-bg);
          border-radius: var(--otd-small-radius);
          padding: 10px 8px 10px 16px;
          box-shadow: var(--otd-box-shadow);
          cursor: pointer;
          display: flex;
          column-gap: 4px;
          line-height: 16px;
          overflow: hidden;
          user-select: none;
          &:hover {
            background-color: var(--otd-gray-hover);
            .otd-icon-tuozhuai,
            .otd-icon-a-catthicksize24 {
              display: inline;
            }
          }
          .otd-icon-tuozhuai {
            position: absolute;
            left: 1px;
            display: none;
            cursor: move;
            color: var(--otd-gray4-color);
            font-size: 15px;
          }
          > span {
            flex: 1;
            font-size: 13px;
          }
          .otd-icon-a-catthicksize24 {
            font-size: 12px;
            color: var(--otd-gray4-color);
            display: none;
          }
        }
      }
    }

    &-arrow {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translate(100%, -50%);
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      text-shadow: 0 0 0 var(--otd-primary-color);
      font-family: otdIconfont;
      &::before {
        content: '\e661';
      }
      &.is-left {
        left: 0;
        right: unset;
        transform: translate(-100%, -50%) rotate(180deg);
      }
    }
  }
</style>
