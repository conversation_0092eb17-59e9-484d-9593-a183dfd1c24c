import { getCurrentInstance, reactive, ref, unref } from 'vue';
import { Frequency, RRule } from 'rrule';
import rruleLange from '/@/locales/RRule';
import { useLocale } from '/@/locales/useLocale';
import { useModal } from '/@/components/BasicModal';
import dayjs from 'dayjs';
import { judgeDateBefore } from '/@/tool';

const { DAILY, WEEKLY, MONTHLY, YEARLY } = Frequency;

const today = dayjs().startOf('day');
export const DateMap: Partial<Record<Frequency, RRule>> = {
  // 每天
  [DAILY]: new RRule({ freq: DAILY }),
  // 每周
  [WEEKLY]: new RRule({ freq: WEEKLY }),
  // 每月
  [MONTHLY]: new RRule({ freq: MONTHLY }),
  // 每年
  [YEARLY]: new RRule({ freq: YEARLY }),
};

export function useRepeatPicker() {
  const { props, attrs, emit } = getCurrentInstance()!;
  const { getLocale } = useLocale();
  const locale = unref(getLocale).replace('_', '-');
  const lang = rruleLange[locale];

  const [registerModal, { openModal, closeModal }] = useModal();

  const pickerOptions = reactive(
    [DAILY, WEEKLY, MONTHLY, YEARLY].map((key) => {
      const rule = DateMap[key]!;
      return {
        value: rule.toString(),
        label: getRepeatText(rule),
      };
    }),
  );

  function getRepeatText(item: RRule) {
    const text = item.toText(lang.getter.bind(lang), lang);
    return locale === 'zh-CN' ? text.replaceAll(' ', '') + '重复' : text;
  }

  const selectVisible = ref(false);
  function handleSelectCustom() {
    selectVisible.value = false;
    openModal(true, { value: attrs.value });
  }

  function handleChange(...arg) {
    const date = props.dates?.[1];
    if (!date) {
      const content = judgeDateBefore([props.dates![0], today]);
      props.dates![0] = content[0];
      props.dates![1] = content[1];
    }
    emit('change', ...arg);
  }

  function handleSelect() {
    pickerOptions.splice(4, 1);
  }

  function handleConfirm(rule: RRule) {
    const value = rule.toString();
    (attrs['onUpdate:value'] as any)(value);
    handleChange();
    pickerOptions[4] = {
      value,
      label: getRepeatText(rule),
    };
    closeModal();
  }
  return [
    registerModal,
    {
      pickerOptions,
      selectVisible,
      getRepeatText,
      handleSelectCustom,
      handleSelect,
      handleChange,
      handleConfirm,
    },
  ] as const;
}
