import { reactive, ref } from 'vue';
import { MoreActionItem } from '/@/components/OtdMoreAction';
// import { useI18n } from '/@/hooks/web/useI18n';
import { createLocalStorage } from '/@/utils/cache';
import { Quick_Action } from '/@/setting';

export const QuickActionMenu: Map<string | number, MoreActionItem> = reactive(new Map());
export const IconComponent = ({ data }: { data: MoreActionItem }) => {
  if (typeof data.icon === 'string') {
    return <i class={`otdIconfont ${data.icon}`} style={{ color: data.color }}></i>;
  } else {
    return data.icon?.({ data });
  }
};
export function useQuickActionMenu() {
  // const { t } = useI18n();
  const ls = createLocalStorage();
  const openPopover = ref(false);
  const defaultQuickActionMenu: MoreActionItem[] = reactive([]);

  // 处理展示固定内容
  function getPinAction() {
    const data = ls.get(Quick_Action);
    data?.map(([id, pin]) => {
      const action = QuickActionMenu.get(id);
      action && (action.pin = pin);
    });
  }

  function setPinAction(data: MoreActionItem) {
    data.pin = !data.pin;
    setPinLocalc();
  }

  function setPinLocalc() {
    ls.set(
      Quick_Action,
      Array.from(QuickActionMenu.values())
        .filter((item) => !item.fixed)
        .map((item) => [item.id, !!item.pin]),
    );
  }

  function handlerAction(item: MoreActionItem) {
    item.action?.({ option: item })?.then(() => {
      openPopover.value = false;
    });
  }
  return {
    openPopover,
    QuickActionMenu,
    defaultQuickActionMenu,
    getPinAction,
    setPinAction,
    setPinLocalc,
    handlerAction,
  };
}
