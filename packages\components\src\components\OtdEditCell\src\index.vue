<template>
  <Form ref="editFormRef" v-bind="$props">
    <slot />
  </Form>
</template>
<script lang="ts" setup>
  import { Form } from 'ant-design-vue';
  import { computed, ref, unref } from 'vue';
  import { getFormProps } from './props';
  import { setFormConfig } from './useProvideConfig';

  const props = defineProps(getFormProps());

  const editFormRef = ref();

  setFormConfig({
    loading: computed(() => unref(props.loading)),
    bordered: computed(() => unref(props.bordered)),
  });

  defineExpose({
    getForm: () => unref(editFormRef),
  });
</script>
<style lang="less" scoped></style>
