<template>
  <Select
    v-model:value="saveFilter"
    class="otd-save-filter otd-select"
    :open="SelectOpen"
    :dropdownMatchSelectWidth="false"
    :dropdownStyle="{ width: '250px' }"
    :placeholder="getPlaceholder"
    show-search
    allowClear
    @blur="SelectOpen = false"
    @click="handleSelectSelect"
    @search="handleSearch"
  >
    <template #dropdownRender>
      <div @mousedown.prevent class="otd-save-filter__content">
        <div class="otd-save-filter__content-save placeholder-hover" @click.stop="handleOpenFilterModal">
          <SaveFilled />
          <span> {{ t('common.seniorFilter.saveTheFilterResults') }} </span>
        </div>

        <div class="otd-save-filter__content__popover">
          <OtdSpin v-if="loading" class="spin" :tip="t('common.loadingText')" />
          <template v-else>
            <Empty v-if="FilterList.length === 0" :image="simpleImage" />
            <template v-else>
              <div
                v-for="(itemFilter, index) in FilterList"
                :key="itemFilter.id"
                @click="handleSelectItemClick(itemFilter)"
                :class="{ 'is-active': saveFilter === itemFilter.name }"
                class="otd-save-filter__content__item otd-table-action"
              >
                <div :title="itemFilter.name"> {{ itemFilter.name }} </div>
                <OtdMoreAction
                  :data="{ itemFilter, index }"
                  :list="FilterList"
                  :actions="Actions"
                  :expand-number="2"
                  actionType="icon"
                  hide-expand-name
                  destroy-popup-on-hide
                />
              </div>
            </template>
          </template>
        </div>
      </div>
    </template>
  </Select>

  <BasicModal
    v-model:open="saveFilterModal"
    :title="isEdit ? t('common.seniorFilter.saveTheFilterResults') : t('common.seniorFilter.editTheFilterResults')"
    :min-height="100"
    :height="300"
    :can-fullscreen="false"
    :maskClosable="false"
    :afterClose="handleFilterModalCancel"
    width="25%"
    cancel-bubble
  >
    <Form :model="FilterFormData" ref="formRef" labelAlign="right" name="basic" layout="vertical" autocomplete="off">
      <FormItem
        :label="t('common.seniorFilter.filterName')"
        :rules="[
          { required: true, message: t('common.seniorFilter.verifyFilterName', { verify: t('common.verify') }) },
        ]"
        name="name"
      >
        <Input v-model:value="FilterFormData.name" />
      </FormItem>
    </Form>
    <template #footer>
      <Button key="back" @click="saveFilterModal = false">{{ t('common.closeText') }}</Button>
      <Button key="submit" type="primary" @click="handleSaveFilter">{{ t('common.okText') }}</Button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { OtdMoreAction } from '/@/components/OtdMoreAction';
  import { OtdSpin } from '/@/components/OtdLoading';
  import { BasicModal } from '/@/components/BasicModal';

  import { Select, Empty, Form, FormItem, Input, Button } from 'ant-design-vue';
  import { SaveFilled } from '@ant-design/icons-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useSaveFilter } from './UseSaveFilter';
  import { getEmits, getProps } from '../../../props';
  import { computed } from 'vue';

  defineProps(getProps());
  defineEmits(getEmits());
  const {
    handleOpenFilterModal,
    handleFilterModalCancel,
    handleSelectSelect,
    handleSelectItemClick,
    handleSaveFilter,
    handleSearch,
    Actions,
    SelectOpen,
    saveFilter,
    saveFilterModal,
    FilterFormData,
    loading,
    FilterList,
    isEdit,
    formRef,
  } = useSaveFilter();

  const getPlaceholder = computed(() =>
    SelectOpen.value ? t('common.searchText') + '...' : t('common.seniorFilter.savedFilters'),
  );

  const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
  const { t } = useI18n();

  defineExpose({ saveFilter, openSave: handleOpenFilterModal });
</script>
<style lang="less" scoped>
  .otd-save-filter {
    width: 150px;
    margin-left: 10px;
    &__content {
      min-height: 250px;
      &-save {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: var(--otd-primary-text);
        padding: 0 10px;
        height: 35px;
        width: 100%;
        > span {
          margin-left: 10px;
        }
      }

      &__popover {
        max-height: 300px;
        overflow: auto;
      }
      &__item {
        height: 35px;
        padding: 5px 0 5px 5px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 4px;
        :deep(.otd-more-action) {
          right: 0;
        }
        &:hover {
          background-color: var(--otd-gray7-color);
        }
        > div {
          font-size: 14px;
          padding: 0 5px;
          color: var(--otd-basic-text);
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    &.is-active {
      background-color: var(--otd-gray-hover) !important;
    }
  }

  .spin {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .placeholder-hover .otdIconfont {
    margin-right: 0;
  }
</style>
