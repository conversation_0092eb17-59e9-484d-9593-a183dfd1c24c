<template>
  <Dropdown
    v-model:open="visible"
    :disabled="disabled"
    overlay-class-name="otd-popover in-tab"
    trigger="click"
    @openChange="handleVisibleChange"
  >
    <div
      class="otd-data-bubble otd-tag__trigger otd-tag-line"
      data-bubble
      :class="[className, bordered ? 'is-border' : '']"
    >
      <div class="otd-tags__placeholder">
        <slot name="prefix"></slot>
        <div class="otd-tags__content" :class="{ 'is-disabled': disabled }" v-if="value.length <= 0">
          <i class="otdIconfont otd-icon-a-cattagsize24 placeholder-hover" v-if="isSimple"></i>
          <div class="otd-tag placeholder-hover" v-else>
            <i class="otdIconfont otd-icon-a-cattagsize24"></i>
            <div class="otd-tag-text" v-if="placeholder">
              {{ placeholder }}
            </div>
          </div>
        </div>
        <div class="otd-tags-content otd-tag-item otd-tags-content__selected" v-else>
          <!-- 已选内容展示 -->
          <TagGroup :data="value" :max-tag-count="maxTagCount" :closeable="!disabled" hide-action />
        </div>
      </div>
    </div>
    <template #overlay>
      <div class="ant-dropdown-menu otd-tags__body">
        <div class="otd-tags__body__tags otd-tag-item" v-if="value.length > 0">
          <!-- 已选内容 -->
          <TagGroup :data="value" v-bind="$attrs" :hide-action="hideAction" closeable expend-all />
        </div>
        <Tabs class="otd-tab no-bottom" v-model:activeKey="activeKey" v-if="type === 'all'">
          <TabPane :key="-1" :tab="t('common.tag.all')" />
          <TabPane :key="TagAuthEnum.Public" :tab="t('common.tag.public')" />
          <TabPane :key="TagAuthEnum.Private" :tab="t('common.tag.private')" />
        </Tabs>
        <div class="otd-tags__body__search">
          <!-- 搜索区域 -->
          <div class="otd-tags__body__search-input">
            <Input
              ref="InputRef"
              v-model:value="createContent.tagName"
              class="otd-input px-6px"
              :placeholder="t('common.tag.tagCreateTip')"
              :bordered="false"
              allowClear
              @press-enter="handleSearchEnter"
            >
              <template #prefix>
                <i class="otdIconfont otd-icon-sousuo"></i>
              </template>
            </Input>
            <!-- 刷新 -->
            <redo-outlined class="ml-4px mr-10px" :spin="refrushLoad" @click="handleRefrush" />
          </div>
          <!-- 创建标签区域 -->
          <CreateTag
            :data="createContent"
            :default-value="activeKey"
            v-if="isCreateTag && !notCreate"
            @create="handleCreateTag"
          />
          <div class="otd-tags__body__search-content">
            <!-- 标签数据内容 -->
            <TagGroup
              :data="tagOptionFilter"
              direction="vertical"
              is-virtual
              :expand-number="0"
              v-bind="$attrs"
              :hide-action="hideAction"
              @item-click="handleChooseTag"
            />
          </div>
        </div>
      </div>
    </template>
  </Dropdown>
</template>
<script lang="ts" setup>
  import { ref, computed, reactive, provide, onMounted } from 'vue';
  import { Dropdown, TabPane, Tabs, Input } from 'ant-design-vue';
  import { RedoOutlined } from '@ant-design/icons-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import TagGroup from './components/TagGroup.vue';
  import CreateTag from './components/CreateTag.vue';
  import { randomColor } from '/@/components/OtdColor';
  import { TagAuthEnum, TagDto } from './types';
  import { getEmits, getProps } from './props';

  const props = defineProps(getProps());
  const emit = defineEmits(getEmits());

  const tagHandlerMap = {
    // 移除tag标签
    itemRemove: ({ value, index }) => {
      const list = props.value;
      list.splice(index, 1);
      emit('update:value', list);
      emit('change', { tag: value, list, isRemove: true });
    },
    // 删除
    delete: (data) => {
      emit('delete', data);
    },
    // 重命名
    rename: (data) => {
      emit('rename', data);
    },
    // 修改颜色
    changeColor: (data) => {
      emit('change-color', data);
    },
    // 修改类型
    changeType: (data) => {
      emit('change-type', data);
    },
    // 保存
    save: (data) => {
      emit('save', data);
    },
  };
  provide('tagHandlerMap', tagHandlerMap);

  const { t } = useI18n();

  const visible = ref(false);
  const activeKey = ref(-1);

  onMounted(() => {
    if (props.type === 'all') {
      activeKey.value = -1;
    } else if (props.type === 'public') {
      activeKey.value = TagAuthEnum.Public;
    } else if (props.type === 'private') {
      activeKey.value = TagAuthEnum.Private;
    }
  });

  const defaultContent = {
    tagName: '',
    tagAuth: TagAuthEnum.Public,
    color: randomColor(),
  };
  // 搜索内容
  const createContent = reactive({ ...defaultContent });

  // 是否创房间标签
  const isCreateTag = computed(() => {
    return (
      isTrim(createContent.tagName) &&
      props.options.findIndex((item) => item.tagName === isTrim(createContent.tagName)) < 0
    );
  });

  // tag选项筛选内容
  const tagOptionFilter = computed(() => {
    const activeKeyMap = {
      '-1': () => true,
      [TagAuthEnum.Public]: (item) => item.tagAuth === TagAuthEnum.Public,
      [TagAuthEnum.Private]: (item) => item.tagAuth === TagAuthEnum.Private,
    };
    const data = props.options.filter((item) => {
      return (
        activeKeyMap[activeKey.value](item) &&
        item.tagName?.includes(isTrim(createContent.tagName) ?? '') &&
        props.value.findIndex((tag: TagDto) => tag.id === item.id) < 0
      );
    });
    return data;
  });

  const searchCurrentTag = computed(() => {
    const index = tagOptionFilter.value.findIndex((item) => item.tagName === isTrim(createContent.tagName));
    return tagOptionFilter.value[index] ?? undefined;
  });

  function isTrim(value: string) {
    return value.trim();
  }

  const InputRef = ref();
  // 处理展开
  function handleVisibleChange(value) {
    if (!value) {
      createContent.tagName = '';
    } else {
      setTimeout(() => {
        InputRef.value.focus();
      }, 100);
    }
  }

  // 创建标签
  function handleCreateTag() {
    const data = { ...createContent };
    createContent.tagName = defaultContent.tagName;
    createContent.tagAuth = defaultContent.tagAuth;
    createContent.color = randomColor();
    emit('create', data);
  }

  // 标签搜索回车事件
  function handleSearchEnter() {
    if (searchCurrentTag.value) {
      chooseTag.value = [searchCurrentTag.value];
      createContent.tagName = '';
    } else if (!isCreateTag.value) {
      createContent.tagName = '';
    } else {
      handleCreateTag();
    }
  }

  // 选中tag内容
  const chooseTag = computed({
    get() {
      return props.value;
    },
    set(tag) {
      const data = [...props.value, ...tag];
      emit('update:value', data);
      emit('change', { tag: tag[0], data, isRemove: false });
    },
  });

  // 选择tag标签
  function handleChooseTag(tag) {
    chooseTag.value = [tag];
  }

  const refrushLoad = ref(false);
  // 刷新
  function handleRefrush() {
    try {
      refrushLoad.value = true;
      props?.updateTags?.().finally(() => {
        refrushLoad.value = false;
      });
    } catch (error) {
      refrushLoad.value = false;
    }
  }
</script>
<style lang="less" scoped>
  .otd-tag__trigger {
    display: flex;
    align-items: center;
    &.is-border {
      width: 100%;
      height: 100%;
      padding: 2px 4px;
      cursor: pointer;
      border: 1px solid var(--otd-border-gray);
      border-radius: var(--otd-default-radius);
      &:hover,
      &.ant-dropdown-open {
        border-color: var(--otd-primary-main);
      }
      .placeholder-hover {
        background-color: transparent !important;
      }
    }
  }
  .otd-tag {
    width: fit-content;
    padding: 0 8px;
    border-radius: var(--otd-default-radius);
    line-height: 24px;
    display: flex;
    align-items: center;
    color: var(--otd-gray4-color);
    border-width: 0;
    .otd-tag-text {
      font-size: 12px;
      color: var(--color);
    }
    &&__grey {
      //灰色  未开始，已取消
      background-color: var(--otd-tag-UnStarted-bg);
      --color: var(--otd-tag-UnStarted-color);
    }
  }
  .mt-10px {
    margin-top: 10px;
  }
  .mr-10px {
    margin-right: 10px;
  }
  .otd-tags {
    &__placeholder {
      display: flex;
      align-items: center;
      overflow: hidden;
      cursor: pointer;
    }
    &__content {
      cursor: pointer;
      display: flex;
      align-items: center;
      .otd-icon-a-cattagsize24 {
        padding: 3px;
      }
    }
    &__body {
      width: 280px;
      border-radius: 8px;
      padding: 0px;
      &__tags {
        padding: 6px 6px 4px 6px;
        &-title {
          font-size: 16px;
          font-weight: bold;
        }
      }
      &__search {
        &-input {
          display: flex;
          align-items: center;
        }
        &-content {
          margin-top: 4px;
          :deep(.scrollbar) {
            max-height: 216px;
            .scrollbar__wrap {
              max-height: 216px;
              padding-bottom: 6px;
            }
          }
        }
      }
    }
  }
</style>
