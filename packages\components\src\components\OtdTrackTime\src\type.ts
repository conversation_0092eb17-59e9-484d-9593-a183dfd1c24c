import { ExtractPropTypes } from 'vue';
import { getEmits, getProps, HourTypeEnum } from './props';
import { Dayjs } from 'dayjs';
import { EmitType } from '/#/global';

export type TrackTimeValueType = {
  totalTime: number;
  timeRecord: {
    userName: string;
    workHour: number;
    workDate: string | Date;
  }[];
};
export type TrackTimeDataType = {
  planTime?: TrackTimeValueType;
  actualTime?: TrackTimeValueType;
};

export type TrackTimeTableDataType = {
  key: HourTypeEnum;
  name: string;
  time: number;
  expandChildren?: TrackTimeValueType['timeRecord'];
};

export interface TrackTimeRecordType {
  id?: string;
  // 是否拆分工时
  isSplit?: boolean;
  /* 工时类型 */
  hourType?: HourTypeEnum;
  /* 工时类别 */
  categoryCode?: string;
  /* 工时工作日期 */
  workDate?: string | Dayjs | [Dayjs, Dayjs];
  /* 工作人员 */
  userId?: string;
  /* 工作人员 */
  userName?: string | undefined;
  /* 用户头像 */
  userAvatar?: string | undefined;
  /* 工作时长(小时) */
  workHour?: number;
  /* 工作时长(天)=8h */
  workDay?: number;
  /* 工作时长,单位毫秒，1小时即为3600000 */
  workTime?: number;
  /* 工作说明 */
  remark?: string;
}

export type ITaskHourDetailDto = {
  /** 工时显示为天 */
  isShowWorkByDay?: boolean;
  /** 预估工时（天） */
  planSpendDay?: number | undefined;
  /** 实际工时（天） */
  realSpendDay?: number | undefined;
  /** 预估工时（时） */
  planSpendHours?: number | undefined;
  planSpendHoursCache?: number | undefined;
  /** 实际工时（时） */
  realSpendHours?: number | undefined;
  realSpendHoursCache?: number | undefined;
  users?: ITaskHourDetailUserDto[] | undefined;
};

export type ITaskHourDetailUserDto = {
  userId?: string;
  categoryCode?: string;
  /** 用户名字 */
  userName?: string | undefined;
  /** 用户头像 */
  userAvatar?: string | undefined;
  /** 预估工时（天） */
  planSpendDay?: number | undefined;
  /** 实际工时（天） */
  realSpendDay?: number | undefined;
  /** 预估工时（时） */
  planSpendHours?: number | undefined;
  planSpendHoursCache?: number | undefined;
  /** 实际工时（时） */
  realSpendHours?: number | undefined;
  realSpendHoursCache?: number | undefined;
  taskHours?: TrackTimeRecordType[] | undefined;
  taskHoursCache?: TrackTimeRecordType[] | undefined;
};

export type TrackTimePropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
export type TrackTimeEmitsType = EmitType<ReturnType<typeof getEmits>[number]>;
