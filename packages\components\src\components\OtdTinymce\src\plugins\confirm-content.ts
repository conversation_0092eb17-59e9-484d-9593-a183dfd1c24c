import tinymce, { Editor } from 'tinymce';
import { useI18n } from '/@/hooks/web/useI18n';
import { debounce } from 'lodash-es';

const editorEventCallback = function (editor: Editor, button) {
  return debounce(() => {
    const content = editor.getContent({ format: 'text' }); // 获取编辑器中的文本内容
    if (content.trim() === '') {
      // 判断内容是否为空
      button.setDisabled(true); // 如果为空，禁用按钮
    } else {
      button.setDisabled(false); // 如果不为空，启用按钮
    }
  }, 300);
};

tinymce.PluginManager.add('confirm', function (editor) {
  const { t } = useI18n();
  editor.on('keydown', function (e) {
    if (e.shiftKey && e.keyCode === 13) {
      // 判断是否按下回车键
      // 在此处编写您想要触发的操作的代码
      editor.fire('confirm');
      e.preventDefault();
    }
  });

  editor.ui.registry.addButton('confirm', {
    tooltip: t('common.okText'),
    text: t('common.okText'),
    disabled: true,
    onAction: function () {
      editor.fire('confirm');
    },
    onSetup: function (buttonApi) {
      editor.on('init', editorEventCallback(editor, buttonApi));
      editor.on('input', editorEventCallback(editor, buttonApi));
      /* onSetup should always return the unbind handlers */
      return function () {
        editor.off('input', editorEventCallback(editor, buttonApi));
      };
    },
  });
  editor.ui.registry.addButton('cancel', {
    tooltip: t('common.cancelText'),
    text: t('common.cancelText'),
    onAction: function () {
      editor.fire('cancel');
    },
  });
});
