@tree-prefix-cls: ~'@{namespace}-tree';

.@{tree-prefix-cls} {
  &.is-overflow {
    .ant-tree-title {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  background-color: var(--otd-basic-bg);
  height: 100%;
  display: flex;
  flex-direction: column;
  .ant-spin-nested-loading {
    flex: 1;
    overflow: hidden;
    .ant-spin-container {
      height: 100%;
      white-space: nowrap;
    }
  }

  .ant-tree-node-content-wrapper {
    position: relative;
  }

  &__title {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    padding-right: 10px;

    &:hover {
      .@{tree-prefix-cls}__action {
        visibility: visible;
      }
    }
  }

  &__content {
    overflow: hidden;
  }

  &__actions {
    position: absolute;
    //top: 2px;
    right: 3px;
    display: flex;
  }

  &__action {
    margin-left: 4px;
    visibility: hidden;
  }

  &-header {
    border-bottom: 1px solid var(--otd-border-color);
  }
}
