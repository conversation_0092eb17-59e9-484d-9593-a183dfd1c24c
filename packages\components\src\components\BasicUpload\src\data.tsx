import { FileItem, PreviewFileItem, UploadResultStatus } from './typing';
import {
  // checkImgType,
  isImgTypeByName,
} from './helper';
import { Progress, TableColumnType, Tag } from 'ant-design-vue';
import { MoreActionItem, OtdMoreAction } from '/@/components/OtdMoreAction';
import ThumbUrl from './ThumbUrl.vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { Fn } from '/#/global';
import { ERROR_COLOR } from '/@/setting';

const { t } = useI18n();

// 文件上传列表
export function createTableColumns(): TableColumnType[] {
  return [
    {
      dataIndex: 'thumbUrl',
      title: t('common.upload.legend'),
      width: 100,
      customRender: ({ record }) => {
        const { thumbUrl } = (record as FileItem) || {};
        return thumbUrl && <ThumbUrl fileUrl={thumbUrl} />;
      },
    },
    {
      dataIndex: 'name',
      title: t('common.upload.fileName'),
      align: 'left',
      customRender: ({ text, record }) => {
        const { percent, status: uploadStatus } = (record as FileItem) || {};
        let status: 'normal' | 'exception' | 'active' | 'success' = 'normal';
        if (uploadStatus === UploadResultStatus.ERROR) {
          status = 'exception';
        } else if (uploadStatus === UploadResultStatus.UPLOADING) {
          status = 'active';
        } else if (uploadStatus === UploadResultStatus.SUCCESS) {
          status = 'success';
        }
        return (
          <span>
            <p class="otd-truncate mb-1" title={text}>
              {text}
            </p>
            <Progress percent={percent} size="small" status={status} />
          </span>
        );
      },
    },
    {
      dataIndex: 'size',
      title: t('common.upload.fileSize'),
      width: 100,
      customRender: ({ text = 0 }) => {
        return text && (text / 1024).toFixed(2) + 'KB';
      },
    },
    // {
    //   dataIndex: 'type',
    //   title: '文件类型',
    //   width: 100,
    // },
    {
      dataIndex: 'status',
      title: t('common.upload.fileStatue'),
      width: 100,
      customRender: ({ text }) => {
        if (text === UploadResultStatus.SUCCESS) {
          return <Tag color="green">{() => t('common.upload.uploadSuccess')}</Tag>;
        } else if (text === UploadResultStatus.ERROR) {
          return <Tag color="red">{() => t('common.upload.uploadError')}</Tag>;
        } else if (text === UploadResultStatus.UPLOADING) {
          return <Tag color="blue">{() => t('common.upload.uploading')}</Tag>;
        }

        return text;
      },
    },
  ];
}
export function createActionColumn(handleRemove: Function): TableColumnType {
  return {
    width: 120,
    title: t('common.upload.operating'),
    dataIndex: 'action',
    fixed: false,
    customRender: ({ record }) => {
      const actions: MoreActionItem[] = [
        {
          id: 1,
          name: t('common.upload.del'),
          color: ERROR_COLOR,
          action: () => handleRemove(record),
        },
      ];
      // if (checkImgType(record)) {
      //   actions.unshift({
      //     label: t('common.upload.preview'),
      //     onClick: handlePreview.bind(null, record),
      //   });
      // }
      return <OtdMoreAction actions={actions} outside={true} />;
    },
  };
}
// 文件预览列表
export function createPreviewColumns(): TableColumnType[] {
  return [
    {
      dataIndex: 'url',
      title: t('common.upload.legend'),
      width: 100,
      customRender: ({ record }) => {
        const { url } = (record as PreviewFileItem) || {};
        return isImgTypeByName(url) && <ThumbUrl fileUrl={url} />;
      },
    },
    {
      dataIndex: 'name',
      title: t('common.upload.fileName'),
      align: 'left',
    },
  ];
}

export function createPreviewActionColumn({
  handleRemove,
  handleDownload,
}: {
  handleRemove: Fn;
  handleDownload: Fn;
}): TableColumnType {
  return {
    width: 160,
    title: t('common.upload.operating'),
    dataIndex: 'action',
    fixed: false,
    customRender: ({ record }) => {
      const actions: MoreActionItem[] = [
        {
          id: 1,
          name: t('common.upload.del'),
          color: ERROR_COLOR,
          action: handleRemove(record),
        },
        {
          id: 2,
          name: t('common.upload.download'),
          action: handleDownload(record),
        },
      ];

      return <OtdMoreAction actions={actions} outside={true} />;
    },
  };
}
