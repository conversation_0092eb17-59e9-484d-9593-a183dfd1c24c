# Excel 操作

<p style="font-size:26px">代码演示</p>

## 导入

<demo src="../demo/ExcelAction/iptExcel.vue" title="导入"></demo>

### 属性

| 参数      | 说明                         | 类型     | 可选值 | 默认值                | 版本 |
| --------- | ---------------------------- | -------- | ------ | --------------------- | ---- |
| success   | 导入成功回调                 | Function | --     | (res:ExcelData)=>void | 1.0  |
| ExcelData | [ExcelData](#ExcelData) 参数 | --       | --     | --                    | 1.0  |

### <span id='ExcelData'>ExcelData</span>

| 参数    | 说明        | 类型 | 可选值                | 默认值 | 版本 |
| ------- | ----------- | ---- | --------------------- | ------ | ---- |
| header  | table 表头  | []   | string[];             | --     | 1.0  |
| results | table 数据  | []   | T[];                  | --     | 1.0  |
| meta    | table title | --   | { sheetName: string } | --     | 1.0  |

[其他参数参考 vben 组件](https://doc.vvbin.cn/components/excel.html#usage)
