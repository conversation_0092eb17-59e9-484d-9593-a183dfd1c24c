<template>
  <Card class="otd-card otd-dashboard-card" :bordered="false" :loading="loading" :canExpan="false">
    <template #title>
      <div class="otd-dashboard-card__header">
        <h1>{{ title }}</h1>
        <div class="otd-dashboard-card__header__action">
          <div class="otd-dashboard-card__header__button" v-if="actions">
            <Button
              v-for="item in actions"
              :key="item.id"
              :loading="item.loading"
              @click="() => item.action?.call(Object.assign(item, { ...componentProps, title }))"
            >
              {{ item.name }}
            </Button>
          </div>
          <div class="otd-dashboard-card__header__filter" v-if="filterRender">
            <CustomRender :component="() => filterRender?.(filterContent, requestRemoteData)" />
          </div>
        </div>
      </div>
    </template>
    <div class="otd-dashboard-card__body" :class="{ 'is-lock': isLock }">
      <component :is="component" v-if="component" v-bind="componentProps" />
      <div ref="echartRef" class="otd-dashboard-card__body__echart" v-else></div>
    </div>
  </Card>
</template>
<script lang="ts" setup>
  import { ref, shallowRef, onMounted, nextTick, unref } from 'vue';
  import { Button, Card } from 'ant-design-vue';
  import * as echarts from 'echarts';
  import { merge } from 'lodash-es';
  import { onUnmounted } from 'vue';
  import { DashboardConfig } from './config';
  import { CustomRender } from '/@/utils/domUtils';
  import { Recordable } from '/#/global';
  import { getProps } from './props';

  const props = defineProps(getProps());

  const emit = defineEmits(['register']);

  const echartRef = ref();
  const loading = ref(false);

  const option = DashboardConfig();

  merge(option, props.option);

  const EChart = shallowRef<echarts.ECharts>();

  const resizeObserver = new ResizeObserver(() => {
    EChart.value?.resize();
  });
  onUnmounted(() => {
    resizeObserver.disconnect();
  });

  onMounted(() => {
    if (!props.component && props.getObserve) {
      resizeObserver.observe(props.getObserve());
    }
    if (!props.component) {
      initEchart();
    }
    requestRemoteData();
  });

  // 初始化Echart
  function initEchart(theme = 'light') {
    EChart.value = echarts.init(echartRef.value, theme);
    option && EChart.value.setOption(option);
    EChart.value.on('click', (params) => {
      props?.barClick?.(params);
    });
  }
  const currentData = ref([]);
  const filterContent = ref<Recordable>({
    ...props.filter,
  });

  // 请求远程数据
  function requestRemoteData() {
    // const filter = defaultFilter.value[timeTab.value];
    // filterContent.time = filter.value();
    if (props?.request) {
      loading.value = true;
      props.request
        ?.apply(props.component ? props.componentProps : option, [unref(filterContent)])
        .then((res) => {
          currentData.value = res;
          loading.value = false;
          nextTick(() => {
            if (EChart.value && !props.component) {
              initEchart();
            }
            EChart.value?.setOption(option);
          });
        })
        .finally(() => {
          loading.value = false;
        });
    }
  }

  // 注册组件
  function registerComponent() {
    emit('register', {
      update: requestRemoteData,
      getData: () => currentData.value,
    });
  }
  registerComponent();
</script>
<style lang="less" scoped>
  .otd-dashboard-card {
    display: flex;
    flex-direction: column;
    border-radius: var(--otd-default-radius);
    position: relative;
    :deep(.ant-card-body) {
      flex: 1;
    }
    &__header {
      // padding: 10px 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      > h1 {
        font-size: 14px;
        margin-bottom: 0;
      }
      &__action {
        display: flex;
        align-items: center;
      }
      &__button {
        & + .otd-dashboard-card__header__filter {
          margin-left: 10px;
        }
      }
      &__filter {
        display: flex;
        .ant-radio-group {
          border-radius: var(--otd-default-radius);
          overflow: hidden;
        }
      }
    }
    &__body {
      width: 100%;
      height: auto;
      max-height: 400px;
      height: 100%;
      &.is-lock {
        aspect-ratio: 16/7;
      }

      &__echart {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
