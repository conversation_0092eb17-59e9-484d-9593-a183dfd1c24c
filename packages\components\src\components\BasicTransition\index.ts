import { createSimpleTransition, createJavascriptTransition } from "./src/CreateTransition";

import ExpandTransitionGenerator from "./src/ExpandTransition";

import BasicCollapseTransition from "./src/CollapseTransition.vue";

export const BasicFadeTransition = createSimpleTransition("fade-transition");
export const BasicScaleTransition = createSimpleTransition("scale-transition");
export const BasicSlideYTransition = createSimpleTransition("slide-y-transition");
export const BasicScrollYTransition = createSimpleTransition("scroll-y-transition");
export const BasicSlideYReverseTransition = createSimpleTransition("slide-y-reverse-transition");
export const BasicScrollYReverseTransition = createSimpleTransition("scroll-y-reverse-transition");
export const BasicSlideXTransition = createSimpleTransition("slide-x-transition");
export const BasicScrollXTransition = createSimpleTransition("scroll-x-transition");
export const BasicSlideXReverseTransition = createSimpleTransition("slide-x-reverse-transition");
export const BasicScrollXReverseTransition = createSimpleTransition("scroll-x-reverse-transition");
export const BasicScaleRotateTransition = createSimpleTransition("scale-rotate-transition");

export const BasicExpandXTransition = createJavascriptTransition(
  "expand-x-transition",
  ExpandTransitionGenerator("", true)
);

export const BasicExpandTransition = createJavascriptTransition("expand-transition", ExpandTransitionGenerator(""));

export { BasicCollapseTransition };
