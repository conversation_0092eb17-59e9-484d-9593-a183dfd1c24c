<template>
  <BasicModal
    :footer="null"
    :title="t('layout.header.lockScreen')"
    v-bind="$attrs"
    class="otd-header-lock-modal"
    :height="400"
    :can-fullscreen="false"
    @register="register"
  >
    <div :class="`otd-header-lock-modal__entry`">
      <div :class="`otd-header-lock-modal__header`">
        <OtdAvatar :url="userInfo.avatar" size="70px" />
        <p :class="`otd-header-lock-modal__header-name`">
          {{ userInfo.realName }}
        </p>
      </div>

      <BasicForm @register="registerForm" />

      <div :class="`otd-header-lock-modal__footer`">
        <Button type="primary" size="large" block class="mt-2" @click="handleLock">
          {{ t('layout.header.lockScreenBtn') }}
        </Button>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup name="LockModal">
  import { Button } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { OtdAvatar } from '/@/components/OtdAvatar';
  import { BasicModal, useModalInner } from '/@/components/BasicModal';
  import { BasicForm, useForm } from '/@/components/BasicForm';
  import { useLockStorage } from '/@/storage/lockStorage';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';

  defineProps({
    userInfo: {
      type: Object,
      default: () => ({}),
    },
  });

  const { t } = useI18n();
  const [register, { closeModal }] = useModalInner();

  const [registerForm, { validateFields, resetFields }] = useForm({
    showActionButtonGroup: false,
    schemas: [
      {
        field: 'password',
        label: t('layout.header.lockScreenPassword'),
        colProps: {
          span: 24,
        },
        component: 'InputPassword',
        required: true,
      },
    ],
  });
  const { getGlobalProvide } = useGlobalConfig();
  const { getLockStorage } = getGlobalProvide();

  const lockStorage = getLockStorage ?? useLockStorage();

  async function handleLock() {
    const values = (await validateFields()) as any;
    const password: string | undefined = values.password;
    closeModal();

    lockStorage.setLockInfo({
      isLock: true,
      pwd: password,
    });
    await resetFields();
  }
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-lock-modal';

  .@{prefix-cls} {
    &__entry {
      position: relative;
      padding: 130px 30px 30px;
      border-radius: var(--otd-border-radius);
    }

    &__header {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: auto;
      text-align: center;

      &-img {
        width: 70px;
        border-radius: 50%;
      }

      &-name {
        margin-top: 5px;
      }
    }

    &__footer {
      text-align: center;
      margin-top: 16px;
    }
  }
</style>
