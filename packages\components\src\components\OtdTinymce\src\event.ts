type EventType = {
  value: string;
  clearable: () => void;
};

export function bindEvent({ editor, props, emit, attrs }) {
  function clearable() {
    editor.setContent('');
  }

  function getFileContent() {
    const resourceFiles: string[] = [];
    const files = editor.selection
      .getNode()
      .querySelectorAll('img[src*="/api/resourceFile/getFileContent?id="], a[href*="/api/resourceFile/download?id="]');
    files.forEach((item) => {
      (item?.src || item?.href).replace(/\?id=(.*)$/, (_text, id) => {
        resourceFiles.push(id);
      });
    });

    return resourceFiles;
  }

  const eventResult: EventType = {
    value: '',
    clearable,
  };

  // 失去焦点事件
  editor.on('blur', function () {
    const value = editor.getContent({ format: 'raw' });
    emit('blur', {
      ...eventResult,
      value,
    });
  });
  // 发送事件
  editor.on('send', function (button) {
    const value = editor.getContent({ format: attrs.outputFormat });
    const resourceFiles = getFileContent();

    if (value) {
      emit('send', {
        ...eventResult,
        value,
        resourceFiles,
      });
      button?.setDisabled?.(true);
    }
  });
  // 确定事件
  editor.on('confirm', function () {
    if (props.modelValue ?? props.value) {
      const resourceFiles = getFileContent();
      emit('confirm', {
        ...eventResult,
        value: props.modelValue ?? props.value,
        resourceFiles,
      });
    }
  });
  // 取消事件
  editor.on('cancel', function () {
    emit('cancel', {
      ...eventResult,
      value: props.modelValue ?? props.value,
    });
  });
}
