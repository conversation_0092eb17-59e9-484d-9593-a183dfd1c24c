<template>
  <span class="otd-priority-icon-label">
    <i class="otdIconfont otd-icon-qizhi" :style="{ color: currentTagMap?.color }"></i>
    <span class="otd-priority-label" v-if="currentTagMap && !isSimple">{{ currentTagMap.label }}</span>
    <span v-else-if="placeholder">{{ placeholder }}</span>
  </span>
</template>
<script lang="ts" setup>
  import { computed, unref } from 'vue';
  import { getProps } from './props';
  import { ColorOptionType } from '../../OtdColor';
  import { usePriority } from './usePriority';

  const props = defineProps(getProps());

  const { PriorityDictionary } = usePriority();
  const getOption = computed(() => props.options ?? PriorityDictionary);

  // 当前优先级
  const currentTagMap = computed<ColorOptionType | undefined>(() => {
    const data = unref(getOption).find((item) => item.value === props.value);
    return data ?? props?.defaultValue ?? undefined;
  });
</script>
<style lang="less" scoped>
  .otd-priority-icon-label {
    display: flex;
    width: fit-content;
    align-items: center;
    > span {
      margin-left: 4px;
      font-size: 14px;
    }
    .otd-priority-label {
      color: var(--otd-basic-text);
    }
  }
</style>
