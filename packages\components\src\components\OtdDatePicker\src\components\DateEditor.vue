<template>
  <div class="otd-date-editor">
    <i class="otdIconfont otd-icon-riqi"></i>
    <AutoComplete
      class="otd-date-editor-auto"
      v-model:value="InputDateValue"
      :bordered="false"
      :placeholder="placeholder"
      :options="options"
      :autofocus="autofocus"
      :dropdownStyle="{ minWidth: '200px' }"
      @click="handleClick"
      @select="handleSelect"
      @search="handleSearch"
      @blur="handleBlur"
      @focus="handleFocus"
    />
    <Tooltip :title="t('common.clearText')" v-if="dateValue"> <CloseCircleFilled @click="handleClear" /></Tooltip>
    <!-- 时间 -->
    <TimePicker
      v-show="!isHidden"
      v-model:value="timeValue"
      :open="isShowTime ? undefined : openTime"
      class="otd-date-editor-time"
      :class="{ 'is-hide-time': !isShowTime }"
      :placeholder="t('common.addTime')"
      :show-arrow="false"
      :bordered="false"
      hide-first
      :dropdownStyle="{ minWidth: '160px' }"
      @select="handleSelectTime"
    />
  </div>
</template>
<script lang="ts" setup>
  import { AutoComplete, Tooltip } from 'ant-design-vue';
  import { CloseCircleFilled } from '@ant-design/icons-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { computed, PropType, ref, unref } from 'vue';
  import dayjs, { Dayjs } from 'dayjs';
  import { DateFormat, DateTimeFormat, getDateFormat } from '../useDatePicker';
  import { DateValueType } from '../type';
  import TimePicker from './TimePicker.vue';
  import { isToTime } from '/@/tool';

  const props = defineProps({
    value: {
      type: Object as PropType<DateValueType>,
    },
    placeholder: {
      type: String,
    },
    defaultTime: {
      type: String,
    },
    autofocus: {
      type: Boolean,
      default: false,
    },
    index: {
      type: Number as PropType<0 | 1>,
      default: 0,
    },
    defaultIndex: {
      type: Number as PropType<0 | 1>,
    },
  });
  const emit = defineEmits(['update:value', 'change', 'update:index']);
  const { t } = useI18n();
  const options = ref<{ value: string }[]>([]);
  const dateValue = computed({
    get: () => {
      const { value } = props;
      if (!value) return (InputDateValue.value = undefined);
      timeValue.value = getTime(value, unref(timeValue));
      InputDateValue.value = getDateFormat(value, unref(timeValue));
      if (!isToTime(value) && unref(timeValue)) {
        emit('update:value', dayjs(InputDateValue.value));
      }
      return value;
    },
    set: (value) => {
      emit('update:value', value);
      emit('change', value);
    },
  });
  const isShowTime = computed(() => props.value && !unref(timeValue));
  const timeValue = ref<string | undefined>(props.defaultTime);
  const InputDateValue = ref();
  const openTime = ref(false);
  const isHidden = ref(false);

  function getTime(date: Dayjs, defaultValue?: string) {
    return isToTime(date) ? date.format('HH:mm') : defaultValue;
  }

  // 选择事件
  function handleSelect(value) {
    options.value = [];
    InputDateValue.value = value;
    dateValue.value = dayjs(value);
    timeValue.value = undefined;
  }
  // 聚焦事件
  function handleFocus() {
    emit('update:index', props.defaultIndex);
  }
  // 失焦事件
  function handleBlur() {
    const date = unref(dateValue);
    InputDateValue.value = date ? getDateFormat(date, unref(timeValue)) : undefined;
    openTime.value = false;
  }
  // 搜索事件
  function handleSearch(text) {
    openTime.value = false;
    if (!text) return (options.value = []);
    const date = dayjs(text);
    if (date.isValid()) {
      let format = DateFormat;
      if (isToTime(date)) {
        format = DateTimeFormat;
      }
      const dateStr = date.format(format);
      options.value = [{ value: dateStr }];
    }
  }

  // 点击输入框
  function handleClick(e) {
    const { value, selectionStart: index } = e.target;
    const match = value?.match(/\d{2}:\d{2}/);
    if (!match) return;
    const clickedOnTime = index >= match.index && index <= match.index + match[0].length;
    if (clickedOnTime) {
      openTime.value = true;
    } else {
      openTime.value = false;
    }
  }

  // 清除事件
  function handleClear() {
    dateValue.value = InputDateValue.value = undefined;
    timeValue.value = undefined;
    openTime.value = false;
  }

  // 选择时间
  function handleSelectTime() {
    const time = unref(timeValue);
    if (time) {
      InputDateValue.value = `${props.value?.format(DateFormat)} ${time}`;
      dateValue.value = dayjs(unref(InputDateValue));
    }
    openTime.value = false;
    isHidden.value = true;
    setTimeout(() => {
      isHidden.value = false;
    }, 300);
  }
</script>
<style lang="less" scoped>
  .otd-date-editor {
    position: relative;
    line-height: 20px;
    flex: 1;
    padding: 6px 14px;
    display: flex;
    align-items: center;
    .otd-icon-riqi {
      font-size: 14px;
      margin-right: 4px;
    }
    &-auto {
      flex: 1;
      max-width: 114px;
      // width: 80px;
      margin-top: -1px;
    }
    .anticon-close-circle {
      margin: 0 8px 0 4px;
      font-size: 12px;
    }
    &-time {
      width: 60px;
      &.is-hide-time {
        opacity: 0;
        position: absolute;
        z-index: -1;
        :deep(.ant-select-selector) {
          > * {
            display: none;
          }
        }
      }
    }
    :deep(.ant-select-selector) {
      height: 20px;
      padding: 0;
      .ant-select-selection-search {
        inset-inline-start: 0;
        inset-inline-end: 0;
        line-height: 1;
        .ant-select-selection-search-input {
          height: 100%;
        }
      }
      .ant-select-selection-placeholder,
      .ant-select-selection-item {
        line-height: 18px;
      }
    }
  }
</style>
