<template>
  <div
    class="otd-basic-table__tr"
    :class="[`otd-basic-table__tr-${indent}`]"
    :style="{ top: `${index * rowHeight}px` }"
  >
    <BasicTableCell
      class="otd-basic-table__td"
      v-for="(field, fieldIndex) in columns"
      :key="field['dataIndex'] as string ?? fieldIndex"
      :column="field"
      :record="record"
      :group-data="groupData"
      :show-expand="+fieldIndex === 0"
      :expanded="expanded"
      :row-key="rowKey"
      :children-column-name="childrenColumnName"
    >
      <template #indent v-if="fieldIndex === 0">
        <Checkbox
          v-if="rowSelection"
          class="otd-basic-table__tr-checkbox"
          :checked="selectedKeyMap!.has(rowKey!(record))"
          @change="(e) => handleCheckRecord(e, record)"
        />
        <div class="otd-basic-table__tr-indent" :style="{ '--indent': indent }"></div>
      </template>
      {{ record[field['dataIndex'] as string] }}
    </BasicTableCell>
  </div>
</template>
<script lang="ts" setup>
  import { computed, PropType } from 'vue';
  import { getEmits, getProps } from '../../props';
  import { Key } from 'ant-design-vue/es/_util/type';
  import { Recordable } from '/#/global';
  import { BasicTableCell } from '../BasicTableCell';
  import { Checkbox } from 'ant-design-vue';

  const props = defineProps({
    ...getProps(),
    record: {
      type: Object as PropType<Recordable>,
      default: () => ({}),
    },
    index: {
      type: Number,
      default: 0,
    },
    groupData: {
      type: Object as PropType<Recordable>,
    },
    selectedKeyMap: {
      type: Object as PropType<Map<Key, Recordable>>,
    },
    expandedKeys: {
      type: Object as PropType<Set<Key>>,
    },
  });
  const emit = defineEmits([...getEmits(), 'change']);
  const expanded = computed(
    () => props.expandedKeys && props.expandedKeys.has(props.rowKey?.(props.record, props.index) ?? props.index),
  );

  function handleCheckRecord(...arg) {
    emit('change', ...arg);
  }
</script>
<style lang="less" scoped>
  @prefix: ~'otd-basic-table';
  .@{prefix} {
    &__tr {
      &:hover {
        background-color: var(--otd-gray-hover);
        .@{prefix}__td {
          background-color: var(--otd-gray-hover);
        }
      }
    }
  }
</style>
