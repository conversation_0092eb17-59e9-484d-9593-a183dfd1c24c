import { Dayjs } from 'dayjs';
import { Recordable } from '/#/global';

export type CanvasDrawType = {
  ctx: CanvasRenderingContext2D;
  canvas: HTMLCanvasElement;
};
export type PublicType = {
  cursor?: string;
  row?: number;
  column?: number;
  x: number;
  y: number;
  isDraw?: boolean;
  isIcon?: boolean;
  isDrap?: boolean;
  offset?: [number, number];
};
export type DirectionType = 'left' | 'right' | 'top' | 'bottom';
export type ThemeType = 'light' | 'dark';
export enum MouseActionEnum {
  Leave = 0,
  Enter = 1,
  Move = 2,
}
export type GraphType = {
  width: number;
  minWidth?: number;
  height: number;
  bgColor?: string;
  hoverBgColor?: string;
  drapBgColor?: string;
  border?: number | number[];
  borderWidth?: number;
  borderColor?: string;
  isCell?: boolean;
  isResise?: boolean;
  isMove?: boolean;
  resizeDirection?: DirectionType[];
  data?: Recordable;
  onDelete?: (data) => void;
  onClick?: (data) => void;
  onResize?: (e: MouseEvent, data) => void;
  onResizeEnd?: (e: MouseEvent, data, type: DirectionType) => void;
  onMove?: (e: MouseEvent, data) => void;
  onMoveEnd?: (e: MouseEvent, data) => void;
  onMouseEnter?: (data) => void;
  onMouseMove?: (e: MouseEvent, data) => void;
  onMouseLeave?: (data) => void;
  onMouseDown?: (e: MouseEvent, data) => void;
  onMouseUp?: (e: MouseEvent, data) => void;
  onDrapEnter?: (data) => void;
  onDrapLeave?: (data) => void;
  onDrop?: (data) => void;
  onSetArea?: (handler: () => void) => number[];
};

export type SquareInfoType = {
  // 圆角
  radius?: number | number[];
  realRadius?: number[];
  onFill?: () => void;
} & GraphType &
  PublicType;

export type TextConfigType = {
  text?: string;
  color?: string;
  borderColor?: string;
  font?: string;
  fontSize?: number;
  maxWidth?: number;
} & PublicType;

export type ImageInfoType = {
  url?: string;
  image?: HTMLImageElement;
} & SquareInfoType;

export type TimelineType = { record; date: [Dayjs, Dayjs]; planDate: [Dayjs, Dayjs]; days: number };

export type DrawTimelineType = {
  data: TimelineType;
  ySeat: number;
  index: number;
  row: number;
  showDays: number;
};
