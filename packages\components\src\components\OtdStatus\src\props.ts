import { PropType } from 'vue';
import { OtdStatusOptionType } from './type';

export const getProps = () => ({
  options: {
    type: Array as PropType<OtdStatusOptionType[]>, // 添加类型定义,
  },
  value: {
    type: [Number, String],
  },
  arrow: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  beforeSelect: {
    type: Function as PropType<(value) => Promise<{ taskStatus: string | number } | false>>,
  },
  iconType: {
    type: String,
    default: 'Task',
  },
});
