<template>
  <OtdScrollbar class="otd-board" view-class="otd-board-full" v-loading="loading">
    <OtdDraggable
      class="otd-board-content"
      item-key="groupId"
      v-model:list="boardGroupList"
      handle="board-group-title"
      :group="{ name: 'first', pull: false }"
      @end="handleMoveTaskGroup"
    >
      <template #item="{ element: group, index: groupIndex }">
        <DragTaskGroup
          :drag-rule="boardDrag"
          :group="group"
          :key="group.id"
          :index="groupIndex"
          :action="actionList"
          :show-action="showAction"
          :group-save="handleGroupSave"
          :drag-on-end="handleMoveTaskCard"
          :list-label="listLabel"
          :hide-task-create="hideTaskCreate"
        >
          <template #group-header="{ group }">
            <slot name="group-header" :group="group"></slot>
          </template>
          <template #card="{ element: data, index: dataIndex }">
            <slot name="card" v-bind="{ group, data, groupIndex, dataIndex }"></slot>
          </template>
        </DragTaskGroup>
      </template>
      <template #footer>
        <slot name="group-footer">
          <!-- 创建分组 -->
          <div class="create-btn" v-if="!hideGroupCreate">
            <Input
              ref="groupRef"
              class="otd-input"
              v-model:value="groupObject.name"
              v-if="groupObject.isAdd"
              :placeholder="t('common.inputText') + t('common.groupTitle')"
              @blur="handleGroupSave({})"
              @press-enter="($event.target as HTMLInputElement).blur()"
            />
            <div v-else class="placeholder-text placeholder-hover" @click="handleCreateGroup()">
              <i class="otdIconfont otd-icon-add-2"></i>
              <span class="ml-6px">{{ t('common.board.createGroup') }}</span>
            </div>
          </div>
        </slot>
      </template>
    </OtdDraggable>
  </OtdScrollbar>
</template>
<script lang="ts" setup>
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { OtdDraggable } from '/@/components/OtdDraggable';
  import DragTaskGroup from './DragTaskGroup.vue';
  import { computed } from 'vue';
  import { Input } from 'ant-design-vue';
  import { useBoard } from './useBoard';
  import { getEmits, getProps } from './props';

  const props = defineProps(getProps());
  const emit = defineEmits(getEmits());

  const {
    t,
    boardAction,
    handleMoveTaskCard,
    boardGroupList,
    handleMoveTaskGroup,
    groupObject,
    handleCreateGroup,
    handleGroupSave,
    boardDrag,
  } = useBoard();

  const showAction = computed(() => {
    return !props.hideAction && !props.loading;
  });
  const actionList = computed(() => {
    return boardAction.value.concat(props.action);
  });
</script>
<style lang="less" scoped>
  .otd-board {
    height: 100%;
    overflow: hidden;
    user-select: none;
    display: flex;
    flex-direction: column;
    :deep(&-full) {
      height: 100%;
    }
    .otd-board-content {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(282px, 282px));
      grid-auto-flow: column;
      column-gap: 16px;
      grid-auto-flow: column;
      height: 100%;
      :deep(> .drag-item) {
        overflow: hidden;
      }
      :deep(> div) {
        min-width: 282px;
      }
    }
  }
  :deep(.create-btn) {
    display: flex;
    width: 100%;
    align-items: center;
    height: 46px;
    min-height: 46px;
    line-height: 46px;
    border-radius: 8px;
    background-color: var(--otd-basic-bg);
    justify-content: center;
    color: var(--otd-gray3-color);
    padding: 4px;
    .otdIconfont {
      font-size: 18px;
    }
    .placeholder-hover {
      width: 100%;
      height: 100%;
      justify-content: center;
    }
  }
</style>
