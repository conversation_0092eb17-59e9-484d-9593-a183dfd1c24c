import { unref } from 'vue';
import { HandlerEnum } from './enum';
import { ProjectConfigType } from '/#/config';
import { APP_DARK_MODE_KEY_ } from '/@/enums/cacheEnum';
import {
  getCurrentThemeModa,
  themeMedia,
  useProjectConfigStorage,
  useRootSetting,
} from '/@/storage/projectConfigStorage';
import { addClass, hasClass, removeClass, toggleClass } from '/@/utils/domUtils';

export function baseHandler(event: HandlerEnum, value: any) {
  const appStore = useProjectConfigStorage();
  const config = handler(event, value);
  appStore.setProjectConfig(config);
}

themeMedia.addListener(() => {
  const RootSetting = useRootSetting();
  const mode = getCurrentThemeModa();
  if (unref(RootSetting.getDarkMode) === mode) return;
  updateDarkTheme(mode);
  RootSetting.setDarkMode(mode);
});

const ConfigEventMap: Record<HandlerEnum, (value: any) => Partial<ProjectConfigType>> = {
  // 灰色模式
  [HandlerEnum.GRAY_MODE]: (value) => {
    updateGrayMode(value);
    return { grayMode: value };
  },
  // 色弱模式
  [HandlerEnum.COLOR_WEAK]: (value) => {
    updateColorWeak(value);
    return { colorWeak: value };
  },
  // 黑夜模式
  [HandlerEnum.DARK_MODE]: (value) => {
    updateDarkTheme(value);
    localStorage.setItem(APP_DARK_MODE_KEY_, value);
    return { darkMode: value };
  },
};

export function handler(event: HandlerEnum, value: any): Partial<ProjectConfigType> {
  return ConfigEventMap[event](value);
}

// 更新灰色模式
export function updateGrayMode(gray: boolean) {
  toggleClass(gray, 'otd-gray-mode', document.documentElement);
}
// 更新色弱模式
export function updateColorWeak(colorWeak: boolean) {
  toggleClass(colorWeak, 'otd-color-weak', document.documentElement);
}

// 更新黑夜模式
export async function updateDarkTheme(mode: string | null = 'light') {
  const htmlRoot = document.querySelector('html');
  if (!htmlRoot) {
    return;
  }
  const hasDarkClass = hasClass(htmlRoot, 'dark');
  if (mode === 'dark') {
    htmlRoot.setAttribute('data-theme', 'dark');
    addClass(htmlRoot, 'dark');
  } else {
    htmlRoot.setAttribute('data-theme', 'light');
    if (hasDarkClass) {
      removeClass(htmlRoot, 'dark');
    }
  }
}
