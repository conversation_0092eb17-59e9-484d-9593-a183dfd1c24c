import { EChartsOption } from 'echarts';
import { PropType, VNode } from 'vue';
import { MoreActionItem } from '/@/components/OtdMoreAction';

export const getProps = () => ({
  title: {
    type: String,
  },
  option: {
    type: Object as PropType<EChartsOption>,
    default: () => ({}),
  },
  request: {
    type: Function as PropType<(data) => Promise<any>>,
  },
  barClick: {
    type: Function as PropType<(params) => void>,
  },
  filter: {
    type: Object,
  },
  filterRender: {
    type: Function as PropType<(filter, onChange) => VNode>,
  },
  actions: {
    type: Array as PropType<MoreActionItem[]>,
  },
  component: {
    type: Object,
  },
  componentProps: {
    type: Object,
  },
  isLock: {
    type: Boolean,
    default: true,
  },
  getObserve: {
    type: Function as PropType<() => HTMLElement>,
  },
});
