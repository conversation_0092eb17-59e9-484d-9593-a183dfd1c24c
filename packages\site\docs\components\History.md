## 历史记录

<demo src="../demo/History/basic.vue" title="历史记录"></demo>

## 属性

| 参数                | 说明                             | 类型     | 可选值 | 默认值 | 版本 |
| ------------------- | -------------------------------- | -------- | ------ | ------ | ---- |
| loading             | loading                          | boolean  | --     | false  | 1.0  |
| data                | 数据列表                         | array    | --     | []     | 1.0  |
| label               | 筛选条件的 label                 | string   | --     | ''     | 1.0  |
| placeholder         | 筛选条件的 placeholder           | string   | --     | ''     | 1.0  |
| popoverTitle        | 弹窗的标题                       | string   | --     | ''     | 1.0  |
| popoverLabel        | 弹窗的 checkbox 的 label         | string   | --     | ''     | 1.0  |
| simple              | 展示模式是否为简易模式           | boolean  | --     | false  | 1.0  |
| fields              | 简易模式下取值的字段             | object   | --     | {}     | 1.0  |
| historyOptions | 筛选下拉选项                     | array    | --     | []     | 1.0  |
| openUser            | 简易模式下点击用户名称的方法回调 | function | --     | --     | 1.0  |
| openContent         | 简易模式下点击内容的方法回调     | function | --     | --     | 1.0  |
