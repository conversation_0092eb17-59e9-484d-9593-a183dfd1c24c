import type { TableRowSelection } from 'ant-design-vue/es/table/interface';
import type { Recordable } from '/#/global';
import type { GetRowKey, Key } from 'ant-design-vue/es/vc-table/interface';
import type { Ref } from 'vue';
import { computed, ref } from 'vue';

interface UseSelectionConfig<Recordable> {
  pageData: Ref<Recordable[]>;
  getRowKey: Ref<GetRowKey<Recordable>>;
  childrenColumnName: Ref<string>;
  rowSelection: Ref<TableRowSelection | undefined>;
  expandedKeysRef: Ref<Set<Key>>;
}

export function flatRecord<T>(records: T[], handler: (record: T) => T[]) {
  records.map((record) => {
    const data = handler(record);
    if (record && Array.isArray(data)) {
      flatRecord(data, handler);
    }
  });
}

export function useSelectionRecord(configRef: UseSelectionConfig<Recordable>) {
  const { pageData, getRowKey, rowSelection } = configRef;
  const selectedKeyMap = ref<Map<Key, Recordable>>(new Map());

  const selectedRowKeys = computed(() => Array.from(selectedKeyMap.value.keys()));
  const selectedRow = computed(() => Array.from(selectedKeyMap.value.values()));
  const recordKeys = computed(() => pageData.value.map((record) => getRowKey.value(record)));
  const checkedCurrentAll = computed(() => {
    return pageData.value.length <= 0 ? false : recordKeys.value.every((key) => selectedKeyMap.value.has(key));
  });
  const indeterminate = computed(() => selectedKeyMap.value.size > 0 && !checkedCurrentAll.value);

  // 全选
  function handleCheckAll(event) {
    const { target } = event;
    pageData.value.map((record) => {
      handleCheckRecord(event, record, true);
    });
    rowSelection.value?.onSelectAll?.(target.checked, selectedRowKeys.value, selectedRow.value);
  }
  // 选中某条数据
  function handleCheckRecord(event, record, isAll = false) {
    const { target } = event;
    const key = getRowKey.value(record);
    if (target.checked) {
      selectedKeyMap.value.set(key, record);
    } else {
      selectedKeyMap.value.delete(key);
    }
    if (!isAll) {
      rowSelection.value?.onSelect?.(record, target.checked, selectedRow.value, event);
    }
    rowSelection.value?.onChange?.(selectedRowKeys.value, selectedRow.value);
  }
  return {
    selectedKeyMap,
    indeterminate,
    checkedCurrentAll,
    handleCheckAll,
    handleCheckRecord,
  };
}
