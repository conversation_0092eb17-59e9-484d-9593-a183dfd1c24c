<template>
  <OtdTable
    :columns="tableColumn"
    :scroll="{ y: 500 }"
    :data-source="dataSource"
    bordered
    :pagination="{
      total: 100,
      current: 5,
    }"
    :summary="SummaryData"
  >
  </OtdTable>
</template>
<script lang="tsx" setup>
  import { OtdTable, TableColumnsType } from '@otd/otd-ui';
  import { reactive } from 'vue';

  const tableColumn: TableColumnsType = [
    {
      title: '姓名',
      dataIndex: 'name',
      fixed: 'left',
      width: '100px',
      sorter: true,
      key: 'name',
      customRender: () => {
        return <div>123</div>;
      },
    },
    {
      title: '姓名',
      dataIndex: 'name',
      fixed: 'left',
      width: '100px',
      key: 'name',
      sorter: true,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: '1200px',
      align: 'center',
      key: 'age',
    },
    {
      title: '住址',
      // fixed: "right",
      dataIndex: 'address',
      width: '200px',
      key: 'address',
    },
    {
      title: '住址',
      fixed: 'right',
      dataIndex: 'address',
      width: '200px',
      key: 'address',
    },
  ];
  const SummaryData = [
    {
      age: '100',
      address: '1111',
    },
    {
      age: '101',
      address: '2222',
    },
  ];
  const dataSource = reactive(
    new Array(10).fill(0).map((_, i) => ({
      key: i,
      name: '胡彦斌',
      age: 32,
      address: '西湖区湖底公园1号',
      children: new Array(1).fill(0).map((_, index) => ({
        key: `${i}-${index}`,
        name: '胡彦斌',
        age: 32,
        address: '西湖区湖底公园2号',
      })),
    })),
  );
</script>
<style lang="less" scoped></style>
