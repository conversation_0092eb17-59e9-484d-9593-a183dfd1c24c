<template>
  <Timeline class="otd-timeline">
    <TimelineItem v-for="(item, index) in dataSource" :key="index" class="otd-timeline-item" color="gray">
      <template #dot><img class="img" :src="item.img" alt="" /></template>
      <div class="content">
        <div class="info">
          <span>{{ item.name }}</span>
          <span class="date">{{ item.date }}</span>
        </div>
        <div class="text">{{ item.text }}</div>
      </div>
    </TimelineItem>
  </Timeline>
</template>
<script lang="ts" setup>
import { Timeline, TimelineItem } from "ant-design-vue";
import { PropType } from "vue";
import { Recordable } from "/#/global";

defineProps({
  dataSource: {
    type: Array as PropType<Recordable>,
    default: () => [],
  },
});
</script>
<style lang="less" scoped>
.otd-timeline {
  .otd-timeline-item {
    display: flex;
    align-items: center;
    :deep(.ant-timeline-item-head) {
      background-color: inherit;
    }
    .img {
      width: 27px;
      height: 27px;
      border-radius: 20px;
    }
    .content {
      display: flex;
      flex-direction: column;
      margin-left: 10px;
      .info {
        .date {
          color: var(--otd-header-text);
          margin-left: 10px;
          font-size: 14px;
        }
      }
      .text {
        margin-top: 5px;
      }
    }
  }
}
</style>
