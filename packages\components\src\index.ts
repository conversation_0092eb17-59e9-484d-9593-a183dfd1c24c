import { createLocalStorage } from '/@/utils/cache';
import { ResourceCategoryEnum, FolderEnum } from '/@/utils/types';
import { ERROR_COLOR, PRIMARY_COLOR, LOAD_PAGE_SOURCE } from '/@/setting';
import 'virtual:svg-icons-register';
import 'viewerjs/dist/viewer.css';
import '/@/style/index.less';
import '/@/assets/icon/iconfont.css';
import { LocaleEnum } from '/@/enums/appEnum';
import { useRootSetting } from '/@/storage/projectConfigStorage';
import { setupI18n } from '/@/locales/setupI18n';
import { useSignalR, OtdSignalConfig } from '/@/hooks/web/useSignalR';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
import { useTitle } from '/@/hooks/web/useTitle';
import { setThemeClor } from '/@/utils/theme';
import { useLocale } from '/@/locales/useLocale';
import { Persistent as OtdPersistent } from '/@/utils/cache/persistent';
import { OtdInitConfig } from '/@/components/OtdConfigProvider';
import { setupGlobDirectives } from '/@/directives';
import { ModalSignle } from '/@/modal/ModalSignle';
import { App } from 'vue';

async function bootstrap() {
  setupI18n();
  OtdInitConfig();
}
export const install = (app: App) => {
  bootstrap();
  setupGlobDirectives(app);
  return app;
};

export * from 'ant-design-vue';
export * from '@ant-design/icons-vue';
export * from './components';
export * from '/@/enums/appEnum';
export * from '/@/hooks/dictionary/index';

export {
  LocaleEnum,
  ResourceCategoryEnum,
  FolderEnum,
  useRootSetting,
  useLocale,
  setupI18n,
  useSignalR,
  useTitle,
  OtdSignalConfig,
  useMessage,
  useGlobalConfig,
  setThemeClor,
  createLocalStorage,
  OtdPersistent,
  ModalSignle,
};
export { ERROR_COLOR, PRIMARY_COLOR, LOAD_PAGE_SOURCE };

export default {
  install,
};
