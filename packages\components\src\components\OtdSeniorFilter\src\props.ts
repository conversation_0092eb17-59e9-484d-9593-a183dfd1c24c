import { Mutable } from '@vueuse/core';
import { PropType } from 'vue';
import { FilterItem } from '/@/components';
import { Recordable } from '/#/global';

export const getProps = () => {
  return {
    value: {
      type: Array as PropType<Recordable[]>,
    },
    options: {
      type: Array as PropType<FilterItem[]>,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: 'Text',
    },
    icon: {
      type: String,
      default: 'otd-icon-shaixuan',
    },
    // 获取筛选列表
    getFilterList: {
      type: Function,
      default: () => {},
    },
    // 保存筛选
    saveFilter: {
      type: Function,
      default: () => {},
    },
    // 修改筛选
    updateFilterItem: {
      type: Function,
      default: () => {},
    },
    // 删除筛选
    removeFilterItem: {
      type: Function,
      default: () => {},
    },
    // 是否显示保存筛选
    isShowSaveFilter: {
      type: Boolean,
      default: true,
    },
  };
};

export const emit = ['change', 'update:value', 'item-click', 'close-filter'] as const;
export const getEmits = () => emit as Mutable<typeof emit>;
