<template>
  <div class="otd-subtask">
    <div class="otd-subtask__header">
      <div class="otd-subtask__header-title">{{ headerText }}</div>
      <div class="otd-subtask__header-action">
        <!-- 任务进度 -->
        <span class="otd-subtask__header-progress">
          <span>{{ taskComplete }} / {{ subtaskData.length ?? 0 }}</span>
          <Progress :percent="taskProgress" :format="(percent) => percent?.toFixed(0) + '%'" />
        </span>
        <OtdShowClosed
          :value="showClosed"
          :options="[{ label: t('common.task'), value: 'task' }]"
          :placeholder="t('common.showClosed.closed')"
        />
        <slot name="action"></slot>
        <OtdMoreAction :expand-number="3" :actions="topActions" action-type="icon" />
      </div>
    </div>

    <div class="otd-subtask__body">
      <div class="otd-mask" v-if="createTaskConfig.isAdd" @click="handleCancelCreate"></div>
      <OtdTable :data-source="showTasks" @register="register" v-bind="$attrs" v-if="subtaskData.length" />
      <div v-else class="placeholder-hover otd-action-btn" @click="handleCreateTask">
        <i class="otdIconfont otd-icon-add-2"></i>
        <span>{{ props.placeholder || t('common.add') }}</span>
      </div>
    </div>
    <AiSubtaskList
      :parentTaskId="parentTaskId"
      :subtaskList="aiTaskList"
      :loading="aiTaskListLoading"
      :confirmLoading="aiConfirmLoading"
      :responsibleUserId="responsibleUserId"
      :disabled="disabled"
      :cancelAiSubtask="cancelAiSubtask"
      :getAiSubtaskList="getAiSubtaskList"
      :confirmAiSubtask="confirmAiSubtask"
    />
  </div>
</template>
<script lang="tsx" setup>
  import { computed, reactive, unref, watch } from 'vue';
  import { Progress } from 'ant-design-vue';
  import { OtdMoreAction } from '/@/components/OtdMoreAction/index';
  import { OtdTable, useTable } from '/@/components/OtdTable/index';
  import { TaskStatusEnum } from '/@/components/OtdStatus';
  import { OtdShowClosed } from '/@/components/OtdShowClosed';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getProps, getEmits } from './props';
  import { useSubtask } from './useSubtask';
  import AiSubtaskList from './components/AiSubtaskList.vue';

  const props = defineProps(getProps());
  const emits = defineEmits(getEmits());
  const { t } = useI18n();
  const showClosed = reactive({ task: props.isShowClosed });
  const {
    subtaskData,
    columns,
    createTaskConfig,
    handleCreateTask,
    handleCancelCreate,
    topActions,
    aiTaskList,
    aiTaskListLoading,
    aiConfirmLoading,
    cancelAiSubtask,
    getAiSubtaskList,
    confirmAiSubtask,
  } = useSubtask();

  const taskComplete = computed(
    () =>
      unref(subtaskData).filter((item) => [TaskStatusEnum.Close, TaskStatusEnum.Cancelled].includes(item.status!))
        .length ?? 0,
    // () => props.detail.childCloseCount ?? 0,
  );
  // 任务进度
  const taskProgress = computed(() => (taskComplete.value / unref(subtaskData).length) * 100);

  const showTasks = computed(() => {
    return unref(subtaskData).filter((task, index) => {
      task.dataIndex = index;
      return showClosed.task || ![TaskStatusEnum.Cancelled, TaskStatusEnum.Close].includes(task.status!);
    });
  });

  const [register] = useTable({
    columns: columns.concat(props.tableColumn),
    scroll: { y: 400 },
    pagination: false,
    bordered: true,
    rowKey: 'id',
  });

  watch(
    () => unref(showClosed.task),
    (val) => {
      emits('getIsShowClosed', val);
    },
  );

  defineExpose({
    cancelAiSubtask,
  });
</script>
<style lang="less" scoped>
  .otd-subtask {
    &__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
      &-title {
        color: var(--otd-header-text);
        font-size: 14px;
      }
      &-action {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        column-gap: 10px;
        flex-wrap: wrap;
        padding-left: 60px;
      }
      &-progress {
        width: fit-content;
        display: flex;
        align-items: center;
        column-gap: 10px;
        font-size: 14px;
        .ant-progress {
          width: 100px;
          margin: -4px 0 0 0;
        }
      }
    }
    &__body {
      .otd-mask {
        position: fixed;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 2;
      }
      :deep(.is-add-task) {
        z-index: 3;
      }
      .otd-action-btn {
        width: 100%;
      }
      :deep(.otd-table) {
        .ant-table-wrapper {
          .ant-table-thead {
            tr .ant-table-cell {
              padding: 8px 16px;
            }
          }
          .ant-table-tbody {
            tr .ant-table-cell {
              padding: 10px 16px;
              .ant-form-item-control {
                flex-basis: 0;
              }
            }
          }
        }
      }
    }
  }
</style>
