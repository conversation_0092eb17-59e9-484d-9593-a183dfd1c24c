<template>
  <Popover overlay-class-name="otd-popover" placement="topLeft" @open-change="handleOpenPathTip(data)" v-bind="$attrs">
    <template #content>
      <span class="otd-remote-tip" v-if="data[field]">
        <span class="otd-remote-tip__item" v-for="item in data[field]" :key="item.id">
          <span @click="handleClickItem(item)">{{ item.title }}</span>
        </span>
      </span>
      <Spin size="small" v-else />
    </template>
    <slot></slot>
  </Popover>
</template>
<script lang="ts" setup>
  import { Popover, Spin } from 'ant-design-vue';
  import { PropType } from 'vue';
  import { Recordable } from '/#/global';
  const props = defineProps({
    data: { type: Object as PropType<Recordable>, default: () => ({}) },
    field: { type: String, default: '__RemoteTipContent' },
    remoteRequest: {
      type: Function as PropType<(data) => Promise<(Recordable & { title: string })[]>>,
    },
  });

  const emit = defineEmits(['item-click']);

  function handleOpenPathTip(record) {
    if (!record[props.field]) {
      props.remoteRequest?.(record).then((content) => {
        record[props.field] = content;
      });
    }
  }

  function handleClickItem(data) {
    emit('item-click', data);
  }
</script>
<style lang="less" scoped>
  .otd-remote-tip {
    &__item {
      > span {
        &:hover {
          cursor: pointer;
          text-decoration: underline;
        }
      }
      & + & {
        &::before {
          content: ' / ';
          margin: 0 2px;
          text-decoration: unset !important;
        }
      }
    }
  }
</style>
