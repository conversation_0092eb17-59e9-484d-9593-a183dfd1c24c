<template>
  <OtdImpExcel @success="loadDataSuccess">
    <Button class="mr-5" type="primary">导入</Button>
  </OtdImpExcel>
</template>

<script lang="ts" setup>
  import { Button, message } from 'ant-design-vue';
  import { OtdImpExcel, ExcelData, TableColumnPropsType } from '@otd/otd-ui';

  import { ref } from 'vue';
  const tableListRef = ref<any>([]);
  function loadDataSuccess(excelDataList: ExcelData[]) {
    tableListRef.value = [];
    for (const excelData of excelDataList) {
      const {
        header,
        results,
        meta: { sheetName },
      } = excelData;
      const columns: TableColumnPropsType[] = [];
      for (const title of header) {
        columns.push({ title, dataIndex: title });
      }
      tableListRef.value.push({ title: sheetName, dataSource: results, columns });
      message.success('操作成功, 查看控制台');
      console.log(tableListRef.value);
    }
  }
</script>

<style scoped lang="less"></style>
