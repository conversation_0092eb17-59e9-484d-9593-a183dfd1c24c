<template>
  <div class="otd-tab-filter" :class="{ 'otd-tab-filter__card': isCard }" v-if="isShowFilter">
    <div class="otd-tab-filter__tabs" v-if="tabOptions.length > 0">
      <Tabs v-model:activeKey="activeKey" @change="handleChangeTab">
        <TabPane v-for="tab in tabOptions" :key="tab.value">
          <template #tab>
            <i class="otdIconfont otd-tab-filter__tabs-icon" :class="[tab.icon]" v-if="tab.icon"></i>
            <span>{{ tab.label }}</span>
          </template>
        </TabPane>
        <template #rightExtra>
          <div class="otd-tab-filter__tabs-add">
            <span><slot name="add"></slot></span>
            <span><slot name="action"></slot></span>
          </div>
        </template>
      </Tabs>
    </div>
    <div class="otd-tab-filter__filter-form" v-show="!hideForm">
      <div class="otd-tab-filter__form">
        <BasicForm @register="register" :bordered="bordered" v-bind="$attrs">
          <template #formFooter>
            <FormItem
              v-if="filterActions.length > 0 || $slots.filter"
              class="otd-tab-filter__filter"
              :class="{ 'otd-tab-filter__filter-right': filterPlacement }"
            >
              <template v-for="action in getShowFilterActions" :key="action.id">
                <Tooltip :title="action.title">
                  <FilterActionComponent :action="action" />
                </Tooltip>
              </template>
              <slot name="filter"></slot>
            </FormItem>
          </template>
        </BasicForm>
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
  import { Tabs, TabPane, Tooltip, FormItem } from 'ant-design-vue';
  import { computed, PropType, ref, watchEffect } from 'vue';
  import { FilterActionType, TabItemType } from './types';
  import { BasicForm, BasicFormProps, FormSchema, useForm } from '/@/components/BasicForm';

  const props = defineProps({
    activeTab: {
      type: [String, Number] as PropType<string | number>,
    },
    tabOptions: {
      type: Array as PropType<TabItemType[]>,
      default: () => [],
    },
    formSchemas: {
      type: Array as PropType<FormSchema[]>,
      default: () => [],
    },
    formConfig: {
      type: Object as PropType<BasicFormProps>,
    },
    filterActions: {
      type: Array as PropType<FilterActionType[]>,
      default: () => [],
    },
    filterPlacement: {
      type: [String, Boolean] as PropType<false | 'right'>,
      default: false,
    },
    showFilterText: {
      type: Boolean,
      default: false,
    },
    isCard: {
      type: Boolean,
      default: false,
    },
    hideFilter: {
      type: Boolean,
      default: false,
    },
    hideForm: {
      type: Boolean,
      default: false,
    },
    bordered: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['tab-change']);

  const isShowFilter = computed(() => {
    return (
      props.filterActions.length > 0 ||
      props.tabOptions.length > 0 ||
      props.formSchemas.length > 0 ||
      (props.formConfig?.schemas?.length ?? 0) > 0 ||
      props.hideFilter
    );
  });

  const getFormConfig = computed<BasicFormProps>(() => {
    return {
      schemas: props.formSchemas,
      showActionButtonGroup: false,
      ...(props.formConfig ?? {}),
    };
  });
  const [register, formActions] = useForm(getFormConfig.value);
  const getShowFilterActions = computed(() => {
    return props.filterActions.filter((item) => (item.isHide ? !item.isHide(item) : true));
  });

  const FilterActionComponent = ({ action }) => {
    return (
      <div class="otd-tab-filter__filter-item otd-radius-border" onClick={(e) => handleActionClick(e, action)}>
        {action.render ? action.render() : <i class={['otdIconfont', action.icon]}></i>}
        {props.showFilterText ? <span class="otd-tab-filter__filter-item__text">{action.title}</span> : undefined}
      </div>
    );
  };

  function handleActionClick(_, action: FilterActionType) {
    action?.action?.(action);
  }

  const activeKey = ref<string | number | undefined>(undefined);
  watchEffect(() => {
    activeKey.value = props.activeTab ?? props.tabOptions?.[0]?.value;
  });
  function handleChangeTab(key) {
    emit('tab-change', key);
  }

  defineExpose({
    ...formActions,
  });
</script>
<style lang="less" scoped>
  @import './style.less';
</style>
