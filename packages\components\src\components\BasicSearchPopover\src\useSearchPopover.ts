import type { SearchQueryType, SearchPopoverPropsType, SearchPopoverEmitType, SearchOptionType } from './type';
import { ComponentInternalInstance, computed, getCurrentInstance, reactive, ref, unref } from 'vue';
import { debounce } from 'lodash-es';

export function useSearchPopover() {
  const { props, emit, proxy } = getCurrentInstance() as ComponentInternalInstance & {
    props: SearchPopoverPropsType;
    emit: SearchPopoverEmitType;
  };
  const getVisible = ref(false);

  const searchData = reactive<SearchQueryType>({
    keyword: '',
    data: [],
    fetching: false,
  });
  // 原始数据
  const sourceUserData = ref<SearchOptionType[]>([]);
  const searchSelectMap = ref(new Map<string, SearchOptionType>());

  const isSelect = computed(() => unref(searchSelectMap).size > 0);
  const SelectData = computed(() => Array.from(unref(searchSelectMap).values()));
  const isMultiple = computed(() => props.mode === 'multiple');

  // 搜索用户
  function handleSearch() {
    props.remoteMethod && remoteSearch();
  }

  // 远程搜索获取用户
  const remoteSearch = debounce(() => {
    sourceUserData.value = [];
    searchData.fetching = true;
    props.remoteMethod?.({ keyword: searchData.keyword, ...props.remoteQuery }).then((data) => {
      sourceUserData.value = data?.filter((item) => !unref(searchSelectMap).has(item.value!)) ?? [];
      searchData.data = sourceUserData.value;
      searchData.fetching = false;
    });
  }, 300);

  function getSearchList() {
    searchData.data = unref(sourceUserData).filter((item) => item.label?.includes(searchData.keyword ?? ''));
  }

  function closePopover() {
    getVisible.value = false;
  }

  // 选择项
  async function selectItem(data, index) {
    try {
      if (props.beforeChange) {
        const status = await props.beforeChange(unref(SelectData), data);
        if (!status) return;
      }
      sourceUserData.value.splice(index, 1);
      if (unref(isMultiple)) {
        unref(searchSelectMap).set(data.value, data);
      } else {
        const { value } = unref(searchSelectMap).values().next();
        if (value) {
          sourceUserData.value.unshift(value);
          unref(searchSelectMap).clear();
        }
        unref(searchSelectMap).set(data.value, data);
        searchData.keyword = '';
        closePopover();
      }
      getSearchList();
      updateChange(data);
    } catch (error) {}
  }

  // 取消项
  async function cancelItem(data) {
    try {
      if (props.beforeChange) {
        const status = await props.beforeChange(unref(SelectData), data);
        if (!status) return;
      }
      unref(searchSelectMap).delete(data.value);
      sourceUserData.value.unshift(data);
      getSearchList();
      updateChange(data);
    } catch (error) {}
  }

  // 清除内容
  function handleClear() {
    unref(searchSelectMap).clear();
    updateChange();
    emit('clear');
  }

  function updateChange(data?) {
    const selects = unref(SelectData);
    let value;
    if (unref(isMultiple)) {
      value = selects;
    } else {
      value = selects[0];
    }
    emit('update:value', value);
    emit('change', selects, data);
  }

  function handleOpenPopover(value) {
    if (!props.immediate) return;
    if (!props.disabled) getVisible.value = value;
    else return closePopover();
    setTimeout(() => {
      const { InputRef } = proxy?.$refs as any;
      InputRef?.focus();
    }, 100);
    handleSearch();
  }
  return {
    getVisible,
    searchData,
    searchSelectMap,
    isSelect,
    closePopover,
    handleSearch,
    selectItem,
    cancelItem,
    handleClear,
    handleOpenPopover,
  };
}
