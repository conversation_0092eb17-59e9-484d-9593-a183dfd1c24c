import { PropType } from 'vue';
import { TagDto } from './types';
import { mutable } from '/@/utils/props';

export const getProps = () => ({
  value: {
    type: Array as PropType<TagDto[]>,
    default: () => [],
  },
  placeholder: {
    type: String,
    default: '',
  },
  options: {
    type: Array as PropType<TagDto[]>,
    default: () => [],
  },
  type: {
    type: String,
    default: 'all',
  },
  maxTagCount: {
    type: Number,
    default: 2,
  },
  updateTags: {
    type: Function as PropType<() => Promise<any>>,
    default: undefined,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  notCreate: {
    type: Boolean,
    default: false,
  },
  bordered: {
    type: Boolean,
    default: false,
  },
  isSimple: {
    type: Boolean,
    default: false,
  },
  hideAction: {
    type: Boolean,
    default: false,
  },
  className: {
    type: String,
  },
});

const emit = [
  'update:value',
  'create',
  'change',
  'item-click',
  'item-remove',
  'delete',
  'rename',
  'change-color',
  'change-type',
  'save',
] as const;
export const getEmits = () => mutable(emit);
