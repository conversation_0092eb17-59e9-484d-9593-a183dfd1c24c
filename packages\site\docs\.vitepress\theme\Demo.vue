<template>
  <OtdConfigProvider
    :locale="getAntdLocale"
    :theme="{
      token: setThemeClor(getIsDarkMode),
    }"
    :layoutConfig="layoutConfig"
  >
    <AntdTheme v-bind="$attrs" />
  </OtdConfigProvider>
</template>
<script lang="ts" setup>
  import { OtdConfigProvider, OtdLayoutConfigType, setThemeClor, useLocale, useRootSetting } from '@otd/otd-ui';
  import { TaskLogTypeEnum } from '@otd/otd-ui/src/components/OtdHistory/src/components/useHistoryTimeline';
  import { AntdTheme } from 'vite-plugin-vitepress-demo/theme';
  import { watch } from 'vue';

  import { unref } from 'vue';

  const { getAntdLocale } = useLocale();

  const { getIsDarkMode, getDarkMode } = useRootSetting();

  watch(
    () => unref(getDarkMode),
    (value) => {
      localStorage.setItem('vitepress-theme-appearance', value);
    },
    { immediate: true },
  );
  const layoutConfig: Partial<OtdLayoutConfigType> = {
    userInfo: {
      userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
      username: 'admin',
      realName: 'limin',
      email: '<EMAIL>',
      roles: ['admin'],
      avatar: 'https://picsum.photos/200/300',
      isSts: false,
      idToken: '',
    },
    getAvatar: () => 'https://picsum.photos/200/300',
    getUserData: () =>
      new Promise((resolve) => {
        const data = [
          {
            tenantId: null,
            userName: '1022032',
            name: '陈永琴',
            surname: null,
            email: '<EMAIL>',
            emailConfirmed: false,
            phoneNumber: null,
            phoneNumberConfirmed: false,
            isActive: true,
            lockoutEnabled: false,
            lockoutEnd: null,
            concurrencyStamp: 'c5070e7110b4487a8aa6dd14e483ea09',
            entityVersion: 10,
            isDeleted: false,
            deleterId: null,
            deletionTime: null,
            lastModificationTime: '2023-12-15T16:59:03.323436',
            lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            creationTime: '2023-09-26T15:54:58.504459',
            creatorId: null,
            id: '245875ae-10f9-f520-cdca-3a0de2a8c9e8',
            extraProperties: {
              Avatar: null,
              AvatarFileId: null,
            },
          },
          {
            tenantId: null,
            userName: '1084632',
            name: '孙雪雯',
            surname: null,
            email: '<EMAIL>',
            emailConfirmed: false,
            phoneNumber: null,
            phoneNumberConfirmed: false,
            isActive: true,
            lockoutEnabled: false,
            lockoutEnd: null,
            concurrencyStamp: 'd71ba7ac160043b4a662568a833e378b',
            entityVersion: 4,
            isDeleted: false,
            deleterId: null,
            deletionTime: null,
            lastModificationTime: '2023-09-26T16:00:16.171813',
            lastModifierId: null,
            creationTime: '2023-09-26T15:54:58.776691',
            creatorId: null,
            id: '670775e0-0fee-f02c-da65-3a0de2a8caf9',
            extraProperties: {
              Avatar: null,
              AvatarFileId: null,
            },
          },
          {
            tenantId: null,
            userName: '1118277',
            name: '张正花',
            surname: null,
            email: '<EMAIL>',
            emailConfirmed: false,
            phoneNumber: null,
            phoneNumberConfirmed: false,
            isActive: true,
            lockoutEnabled: false,
            lockoutEnd: null,
            concurrencyStamp: '9c67422b273e4117936c78f878b0fa78',
            entityVersion: 12,
            isDeleted: false,
            deleterId: null,
            deletionTime: null,
            lastModificationTime: '2024-04-25T11:17:56.503369',
            lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            creationTime: '2023-09-26T15:54:59.622071',
            creatorId: null,
            id: '5873157c-943e-9b23-ed46-3a0de2a8ce36',
            extraProperties: {
              Avatar: null,
              AvatarFileId: null,
            },
          },
        ];
        const result = data.map((item) => ({
          label: item.name,
          value: item.id,
          img: item?.extraProperties?.Avatar,
          email: item.email,
        }));
        resolve(result);
      }),
    getFetchTask: ({ keyword }) =>
      new Promise((resolve) => {
        const data = [
          {
            taskCode: '0000000000000228.0001',
            collaborations: [],
            isShowWorkByDay: false,
            taskHours: [],
            isFavorite: false,
            relatedId: '3a11de09-afc9-4889-6391-fbd6131af368',
            relatedName: '哈--上海宝存信',
            relatedType: 5,
            subRelatedType: 'Opportunities',
            i18NRelatedType: '销售系统',
            i18NSubRelatedType: '商机',
            parentTaskId: '3a12fe9f-f406-9c41-7659-e08ec9aee65f',
            parentTaskItemTitle: '确定沙龙名单',
            preTaskItemId: null,
            preTaskItemTitle: '',
            title: '列出意向邀请嘉宾名单列出意向邀请嘉宾名单列出意向邀请嘉宾名单',
            description: null,
            priority: 0,
            taskProgress: 0,
            status: 30,
            groupSort: 0,
            statusSort: 10900,
            responsibleOrganizeId: null,
            checkerOrganizeId: null,
            responsibleUserId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            responsibleUserName: 'limin',
            responsibleUserAvatar: 'https://picsum.photos/200/300',
            responsibleGroupId: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
            responsibleGroup: {
              groupId: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
              name: '默认分组',
              color: null,
              order: 0,
              isDefault: true,
              lastModificationTime: '2024-07-05T10:04:15.661727',
              lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              creationTime: '2023-05-02T16:08:32.730807',
              creatorId: null,
              id: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
              extraProperties: {},
            },
            checkUsers: [
              {
                userId: '3a0f4fa9-cddb-f885-2ecc-2b0b466686e0',
                name: 'rikka',
                avatar: null,
                actualCheckDate: null,
              },
              {
                userId: '3a1326e5-31ec-7e7d-e79b-189362e6d54e',
                name: 'yyy3',
                avatar: null,
                actualCheckDate: null,
              },
              {
                userId: '3a133c7a-a8d3-ff20-0c65-f363e307d890',
                name: '天草二十六',
                avatar: null,
                actualCheckDate: null,
              },
            ],
            followUsers: [
              {
                userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                name: 'limin',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-06-06T15:28:38.358871',
              },
            ],
            planStartDate: '2024-06-25T00:00:00',
            planStart: '2024-06-25',
            planDoneDate: '2024-08-18T17:16:00',
            planDone: '2024-08-18 17:16',
            remind: null,
            actualStartDate: '2024-08-06T15:13:01.926443',
            actualDoneDate: null,
            actualCheckDate: null,
            actualCloseDate: null,
            childCount: 6,
            childCloseCount: 0,
            canCreateChild: true,
            creatorName: 'limin',
            taskDocLinks: [],
            taskItemTags: [
              {
                tagName: '信息',
                color: '#8E44AD',
                tagAuth: 1,
                tagType: 0,
                creationTime: '2023-10-26T09:54:33.938437',
                creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                id: '3a0e7bdd-9afe-1f9d-ad9f-84dd95dbb805',
              },
              {
                tagName: '标签1',
                color: '#16A085',
                tagAuth: 0,
                tagType: 0,
                creationTime: '2023-08-29T13:32:47.289828',
                creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                id: '3a0d51f4-8d35-d52f-7d4b-75e85ea202cd',
              },
            ],
            autoCompletion: true,
            planSpendDay: null,
            realSpendDay: null,
            planSpendHours: null,
            realSpendHours: null,
            lastModificationTime: '2024-08-12T15:05:18.882494',
            lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            creationTime: '2024-06-06T15:28:38.357995',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: '3a12fe9f-f5bc-a65e-4738-164ce49832bc',
          },
          {
            relatedId: null,
            relatedName: null,
            relatedType: 4,
            subRelatedType: null,
            i18NRelatedType: null,
            i18NSubRelatedType: null,
            parentTaskId: '3a1418c8-5a83-74b9-6de4-f142323964da',
            parentTaskItemTitle: '2',
            preTaskItemId: null,
            preTaskItemTitle: null,
            title: '6' + keyword,
            description: null,
            priority: null,
            taskProgress: 0,
            status: 10,
            groupSort: 2500,
            statusSort: 29700,
            responsibleOrganizeId: null,
            checkerOrganizeId: null,
            responsibleUserId: '3a0c30de-4720-871e-c121-852419671480',
            responsibleUserName: '邢黎敏',
            responsibleUserAvatar: 'https://picsum.photos/200/300',
            responsibleGroupId: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
            responsibleGroup: {
              groupId: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
              name: '默认分组',
              color: null,
              order: 0,
              isDefault: true,
              lastModificationTime: null,
              lastModifierId: null,
              creationTime: '2023-07-04T10:18:12.304233',
              creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
              id: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
              extraProperties: {},
            },
            checkUsers: [],
            followUsers: [
              {
                userId: '3a0c30de-4720-871e-c121-852419671480',
                name: '邢黎敏',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-07-31T10:25:48.024803',
              },
            ],
            planStartDate: null,
            planStart: null,
            planDoneDate: null,
            planDone: null,
            remind: null,
            actualStartDate: null,
            actualDoneDate: null,
            actualCheckDate: null,
            actualCloseDate: null,
            childCount: 0,
            childCloseCount: 0,
            canCreateChild: false,
            creatorName: '邢黎敏',
            taskDocLinks: null,
            taskItemTags: [],
            autoCompletion: false,
            planSpendDay: null,
            realSpendDay: null,
            planSpendHours: null,
            realSpendHours: null,
            lastModificationTime: '2024-07-31T10:27:11.470313',
            lastModifierId: '3a0c30de-4720-871e-c121-852419671480',
            creationTime: '2024-07-31T10:25:48.024613',
            creatorId: '3a0c30de-4720-871e-c121-852419671480',
            id: '3a1418c8-77d8-e1db-0c38-8c6e3f87d35b',
          },
          {
            relatedId: null,
            relatedName: null,
            relatedType: 4,
            subRelatedType: null,
            i18NRelatedType: null,
            i18NSubRelatedType: null,
            parentTaskId: '3a1418c8-4ca4-524a-61cc-5166d8ef5e35',
            parentTaskItemTitle: '1',
            preTaskItemId: null,
            preTaskItemTitle: null,
            title: '5' + keyword,
            description: null,
            priority: null,
            taskProgress: 0,
            status: 10,
            groupSort: 2600,
            statusSort: 29800,
            responsibleOrganizeId: null,
            checkerOrganizeId: null,
            responsibleUserId: '3a0c30de-4720-871e-c121-852419671480',
            responsibleUserName: '邢黎敏',
            responsibleUserAvatar: 'https://picsum.photos/200/300',
            responsibleGroupId: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
            responsibleGroup: {
              groupId: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
              name: '默认分组',
              color: null,
              order: 0,
              isDefault: true,
              lastModificationTime: null,
              lastModifierId: null,
              creationTime: '2023-07-04T10:18:12.304233',
              creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
              id: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
              extraProperties: {},
            },
            checkUsers: [],
            followUsers: [
              {
                userId: '3a0c30de-4720-871e-c121-852419671480',
                name: '邢黎敏',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-07-31T10:26:07.805142',
              },
            ],
            planStartDate: null,
            planStart: null,
            planDoneDate: null,
            planDone: null,
            remind: null,
            actualStartDate: null,
            actualDoneDate: null,
            actualCheckDate: null,
            actualCloseDate: null,
            childCount: 0,
            childCloseCount: 0,
            canCreateChild: false,
            creatorName: '邢黎敏',
            taskDocLinks: null,
            taskItemTags: [],
            autoCompletion: false,
            planSpendDay: null,
            realSpendDay: null,
            planSpendHours: null,
            realSpendHours: null,
            lastModificationTime: '2024-07-31T10:26:47.128462',
            lastModifierId: '3a0c30de-4720-871e-c121-852419671480',
            creationTime: '2024-07-31T10:26:07.804867',
            creatorId: '3a0c30de-4720-871e-c121-852419671480',
            id: '3a1418c8-c521-25a6-db47-fee64de0faee',
          },
          {
            relatedId: null,
            relatedName: null,
            relatedType: 4,
            subRelatedType: null,
            i18NRelatedType: null,
            i18NSubRelatedType: null,
            parentTaskId: '3a12ee1c-21bd-9c43-ee16-483727d3176a',
            parentTaskItemTitle: '二',
            preTaskItemId: null,
            preTaskItemTitle: null,
            title: '666777' + keyword,
            description: null,
            priority: null,
            taskProgress: 0,
            status: 10,
            groupSort: 2100,
            statusSort: 29300,
            responsibleOrganizeId: null,
            checkerOrganizeId: null,
            responsibleUserId: '3a0c30de-4720-871e-c121-852419671480',
            responsibleUserName: '邢黎敏',
            responsibleUserAvatar: 'https://picsum.photos/200/300',
            responsibleGroupId: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
            responsibleGroup: {
              groupId: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
              name: '默认分组',
              color: null,
              order: 0,
              isDefault: true,
              lastModificationTime: null,
              lastModifierId: null,
              creationTime: '2023-07-04T10:18:12.304233',
              creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
              id: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
              extraProperties: {},
            },
            checkUsers: [],
            followUsers: [
              {
                userId: '3a0c30de-4720-871e-c121-852419671480',
                name: '邢黎敏',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-07-31T10:24:46.60026',
              },
            ],
            planStartDate: null,
            planStart: null,
            planDoneDate: null,
            planDone: null,
            remind: null,
            actualStartDate: null,
            actualDoneDate: null,
            actualCheckDate: null,
            actualCloseDate: null,
            childCount: 0,
            childCloseCount: 0,
            canCreateChild: false,
            creatorName: '邢黎敏',
            taskDocLinks: null,
            taskItemTags: [],
            autoCompletion: false,
            planSpendDay: null,
            realSpendDay: null,
            planSpendHours: null,
            realSpendHours: null,
            lastModificationTime: '2024-07-31T10:25:03.448114',
            lastModifierId: '3a0c30de-4720-871e-c121-852419671480',
            creationTime: '2024-07-31T10:24:46.600065',
            creatorId: '3a0c30de-4720-871e-c121-852419671480',
            id: '3a1418c7-87e7-23ba-6922-51747e60e452',
          },
          {
            relatedId: null,
            relatedName: null,
            relatedType: 4,
            subRelatedType: null,
            i18NRelatedType: null,
            i18NSubRelatedType: null,
            parentTaskId: null,
            parentTaskItemTitle: '',
            preTaskItemId: null,
            preTaskItemTitle: null,
            title: '123' + keyword,
            description: null,
            priority: 3,
            taskProgress: 0,
            status: 30,
            groupSort: 100,
            statusSort: 2500,
            responsibleOrganizeId: null,
            checkerOrganizeId: null,
            responsibleUserId: '3a0c30de-4720-871e-c121-852419671480',
            responsibleUserName: '邢黎敏',
            responsibleUserAvatar: 'https://picsum.photos/200/300',
            responsibleGroupId: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
            responsibleGroup: {
              groupId: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
              name: '默认分组',
              color: null,
              order: 0,
              isDefault: true,
              lastModificationTime: null,
              lastModifierId: null,
              creationTime: '2023-07-04T10:18:12.304233',
              creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
              id: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
              extraProperties: {},
            },
            checkUsers: [
              {
                userId: '3a0c30e0-248f-0347-5e49-cf18b77705f5',
                name: '王蕾',
                avatar: 'https://picsum.photos/200/300',
                actualCheckDate: null,
              },
              {
                userId: '3a0c30de-4720-871e-c121-852419671480',
                name: '邢黎敏',
                avatar: 'https://picsum.photos/200/300',
                actualCheckDate: null,
              },
            ],
            followUsers: [
              {
                userId: '3a0c30de-4720-871e-c121-852419671480',
                name: '邢黎敏',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-01-30T14:08:22.092865',
              },
              {
                userId: '3a0c30e0-248f-0347-5e49-cf18b77705f5',
                name: '王蕾',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-07-31T10:03:19.297759',
              },
            ],
            planStartDate: '2024-06-27T00:00:00',
            planStart: '2024-06-27',
            planDoneDate: '2024-06-29T23:59:59',
            planDone: '2024-06-29',
            remind: null,
            actualStartDate: '2024-07-31T10:08:21.723801',
            actualDoneDate: null,
            actualCheckDate: null,
            actualCloseDate: null,
            childCount: 0,
            childCloseCount: 0,
            canCreateChild: false,
            creatorName: '邢黎敏',
            taskDocLinks: null,
            taskItemTags: [
              {
                tagName: '奥丁云平台',
                color: '#C0392B',
                tagAuth: 0,
                tagType: 0,
                creationTime: '2024-04-24T15:48:00.15509',
                creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
                id: '3a122140-3c11-a42f-d16f-4e47b4581f29',
              },
            ],
            autoCompletion: false,
            planSpendDay: 0.6,
            realSpendDay: 1.5,
            planSpendHours: 5,
            realSpendHours: 12,
            lastModificationTime: '2024-07-31T10:13:04.434006',
            lastModifierId: '3a0c30de-4720-871e-c121-852419671480',
            creationTime: '2024-01-30T14:08:22.092614',
            creatorId: '3a0c30de-4720-871e-c121-852419671480',
            id: '3a106b28-780f-8410-45e6-47c52a08ace3',
          },
          {
            relatedId: null,
            relatedName: null,
            relatedType: 4,
            subRelatedType: null,
            i18NRelatedType: null,
            i18NSubRelatedType: null,
            parentTaskId: null,
            parentTaskItemTitle: '',
            preTaskItemId: null,
            preTaskItemTitle: null,
            title: '测试责任人' + keyword,
            description: null,
            priority: 3,
            taskProgress: 0,
            status: 30,
            groupSort: 200,
            statusSort: 2200,
            responsibleOrganizeId: null,
            checkerOrganizeId: null,
            responsibleUserId: '3a0c30de-4720-871e-c121-852419671480',
            responsibleUserName: '邢黎敏',
            responsibleUserAvatar: 'https://picsum.photos/200/300',
            responsibleGroupId: null,
            responsibleGroup: null,
            checkUsers: [
              {
                userId: '3a0c30df-137e-811b-3853-b84ab63d7f8e',
                name: '张三',
                avatar: 'https://picsum.photos/200/300',
                actualCheckDate: null,
              },
              {
                userId: '3a0c30e0-248f-0347-5e49-cf18b77705f5',
                name: '王蕾',
                avatar: 'https://picsum.photos/200/300',
                actualCheckDate: null,
              },
              {
                userId: '3a0c30e1-fa27-201e-c910-89fb97529684',
                name: '宋佳莉',
                avatar: 'https://picsum.photos/200/300',
                actualCheckDate: null,
              },
            ],
            followUsers: [
              {
                userId: '3a0c30de-4720-871e-c121-852419671480',
                name: '邢黎敏',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-03-07T15:50:33.862362',
              },
              {
                userId: '3a0c30e1-fa27-201e-c910-89fb97529684',
                name: '宋佳莉',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-02-05T10:16:37.68736',
              },
              {
                userId: '3a0c30e0-248f-0347-5e49-cf18b77705f5',
                name: '王蕾',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-02-05T10:16:36.748809',
              },
              {
                userId: '3a0c30df-137e-811b-3853-b84ab63d7f8e',
                name: '张三',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-02-05T10:16:24.858614',
              },
            ],
            planStartDate: '2024-03-08T00:00:00',
            planStart: '2024-03-08',
            planDoneDate: '2024-03-08T23:59:59',
            planDone: '2024-03-08',
            remind: null,
            actualStartDate: '2024-07-19T17:19:39.237523',
            actualDoneDate: null,
            actualCheckDate: null,
            actualCloseDate: null,
            childCount: 1,
            childCloseCount: 0,
            canCreateChild: false,
            creatorName: '邢黎敏',
            taskDocLinks: null,
            taskItemTags: [],
            autoCompletion: false,
            planSpendDay: null,
            realSpendDay: 0.2,
            planSpendHours: null,
            realSpendHours: 2,
            lastModificationTime: '2024-07-19T17:20:39.046381',
            lastModifierId: '3a0c30de-4720-871e-c121-852419671480',
            creationTime: '2024-02-05T09:43:19.154056',
            creatorId: '3a0c30de-4720-871e-c121-852419671480',
            id: '3a10891b-f761-341f-d885-cb2f26b913ec',
          },
          {
            relatedId: null,
            relatedName: null,
            relatedType: 4,
            subRelatedType: null,
            i18NRelatedType: null,
            i18NSubRelatedType: null,
            parentTaskId: null,
            parentTaskItemTitle: '',
            preTaskItemId: null,
            preTaskItemTitle: null,
            title: '举办一场沙龙活动' + keyword,
            description: null,
            priority: 2,
            taskProgress: 0,
            status: 10,
            groupSort: 3100,
            statusSort: 29100,
            responsibleOrganizeId: null,
            checkerOrganizeId: null,
            responsibleUserId: '3a0c30e3-0a5a-dd13-2ae4-1215aa676b53',
            responsibleUserName: '周晓磊',
            responsibleUserAvatar: 'https://picsum.photos/200/300',
            responsibleGroupId: '3a0c30e3-0b07-f2dd-6b9d-f3c4a20f3ed1',
            responsibleGroup: {
              groupId: '3a0c30e3-0b07-f2dd-6b9d-f3c4a20f3ed1',
              name: '默认分组',
              color: null,
              order: 0,
              isDefault: true,
              lastModificationTime: null,
              lastModifierId: null,
              creationTime: '2023-07-04T10:23:24.424258',
              creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
              id: '3a0c30e3-0b07-f2dd-6b9d-f3c4a20f3ed1',
              extraProperties: {},
            },
            checkUsers: [],
            followUsers: [
              {
                userId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
                name: '丁留建',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-07-17T14:42:38.1814',
              },
              {
                userId: '3a0c30e4-e166-c7ef-cc5f-9684939556fe',
                name: '张苏',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-07-19T16:41:19.359715',
              },
              {
                userId: '3a0c30de-4720-871e-c121-852419671480',
                name: '邢黎敏',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-07-19T16:41:21.280401',
              },
            ],
            planStartDate: null,
            planStart: null,
            planDoneDate: '2024-08-12T23:59:59',
            planDone: '2024-08-12',
            remind: null,
            actualStartDate: null,
            actualDoneDate: null,
            actualCheckDate: null,
            actualCloseDate: null,
            childCount: 0,
            childCloseCount: 0,
            canCreateChild: false,
            creatorName: '丁留建',
            taskDocLinks: null,
            taskItemTags: [
              {
                tagName: '奥丁云平台',
                color: '#C0392B',
                tagAuth: 0,
                tagType: 0,
                creationTime: '2024-04-24T15:48:00.15509',
                creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
                id: '3a122140-3c11-a42f-d16f-4e47b4581f29',
              },
            ],
            autoCompletion: true,
            planSpendDay: null,
            realSpendDay: null,
            planSpendHours: null,
            realSpendHours: null,
            lastModificationTime: '2024-07-19T16:41:21.279924',
            lastModifierId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
            creationTime: '2024-07-17T14:42:38.18118',
            creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
            id: '3a13d19a-93d4-fb7a-d6c4-8508f7e39ab9',
          },
          {
            relatedId: null,
            relatedName: null,
            relatedType: 4,
            subRelatedType: null,
            i18NRelatedType: null,
            i18NSubRelatedType: null,
            parentTaskId: null,
            parentTaskItemTitle: '',
            preTaskItemId: null,
            preTaskItemTitle: null,
            title: '测试任务13号' + keyword,
            description: null,
            priority: 3,
            taskProgress: 0,
            status: 30,
            groupSort: 400,
            statusSort: 900,
            responsibleOrganizeId: null,
            checkerOrganizeId: null,
            responsibleUserId: '3a0c30e3-0a5a-dd13-2ae4-1215aa676b53',
            responsibleUserName: '周晓磊',
            responsibleUserAvatar: 'https://picsum.photos/200/300',
            responsibleGroupId: '3a0c30e3-0b07-f2dd-6b9d-f3c4a20f3ed1',
            responsibleGroup: {
              groupId: '3a0c30e3-0b07-f2dd-6b9d-f3c4a20f3ed1',
              name: '默认分组',
              color: null,
              order: 0,
              isDefault: true,
              lastModificationTime: null,
              lastModifierId: null,
              creationTime: '2023-07-04T10:23:24.424258',
              creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
              id: '3a0c30e3-0b07-f2dd-6b9d-f3c4a20f3ed1',
              extraProperties: {},
            },
            checkUsers: [
              {
                userId: '3a0c30e4-e166-c7ef-cc5f-9684939556fe',
                name: '张苏',
                avatar: 'https://picsum.photos/200/300',
                actualCheckDate: null,
              },
            ],
            followUsers: [
              {
                userId: '3a0c30de-4720-871e-c121-852419671480',
                name: '邢黎敏',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-03-14T15:57:24.473553',
              },
              {
                userId: '3a0c30e3-0a5a-dd13-2ae4-1215aa676b53',
                name: '周晓磊',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-03-14T15:59:43.707623',
              },
            ],
            planStartDate: '2024-03-11T09:00:00',
            planStart: '2024-03-11 09:00',
            planDoneDate: '2024-03-14T23:59:59',
            planDone: '2024-03-14',
            remind: null,
            actualStartDate: '2023-10-27T18:38:39.269593',
            actualDoneDate: null,
            actualCheckDate: null,
            actualCloseDate: null,
            childCount: 5,
            childCloseCount: 0,
            canCreateChild: false,
            creatorName: '丁留建',
            taskDocLinks: null,
            taskItemTags: [
              {
                tagName: '标签1',
                color: '#FF5733',
                tagAuth: 0,
                tagType: 0,
                creationTime: '2023-08-30T09:49:13.386113',
                creatorId: '3a0c30df-137e-811b-3853-b84ab63d7f8e',
                id: '3a0d564e-3b22-86f9-0b61-c71c7308c5c4',
              },
              {
                tagName: '标签',
                color: '#D4AC0D',
                tagAuth: 0,
                tagType: 0,
                creationTime: '2023-08-30T09:49:37.332499',
                creatorId: '3a0c30df-137e-811b-3853-b84ab63d7f8e',
                id: '3a0d564e-98af-285e-35f6-413c8056e249',
              },
            ],
            autoCompletion: false,
            planSpendDay: null,
            realSpendDay: null,
            planSpendHours: null,
            realSpendHours: null,
            lastModificationTime: '2024-03-14T16:30:25.346426',
            lastModifierId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
            creationTime: '2023-10-13T17:26:15.588905',
            creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
            id: '3a0e3a88-78db-9f78-6047-c70ff8e7a1b0',
          },
          {
            relatedId: null,
            relatedName: null,
            relatedType: 4,
            subRelatedType: null,
            i18NRelatedType: null,
            i18NSubRelatedType: null,
            parentTaskId: null,
            parentTaskItemTitle: '',
            preTaskItemId: null,
            preTaskItemTitle: null,
            title: '测试任务-01' + keyword,
            description: null,
            priority: null,
            taskProgress: 100,
            status: 60,
            groupSort: 100,
            statusSort: 10000,
            responsibleOrganizeId: '3a0c30ef-42b2-8d58-c20a-52391c70c7c9',
            checkerOrganizeId: null,
            responsibleUserId: '3a0c30de-4720-871e-c121-852419671480',
            responsibleUserName: '邢黎敏',
            responsibleUserAvatar: 'https://picsum.photos/200/300',
            responsibleGroupId: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
            responsibleGroup: {
              groupId: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
              name: '默认分组',
              color: null,
              order: 0,
              isDefault: true,
              lastModificationTime: null,
              lastModifierId: null,
              creationTime: '2023-07-04T10:18:12.304233',
              creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
              id: '3a0c30de-47cf-82ad-e15d-a11dc7b92d69',
              extraProperties: {},
            },
            checkUsers: [],
            followUsers: [
              {
                userId: '3a0c30de-4720-871e-c121-852419671480',
                name: '邢黎敏',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2023-07-04T16:19:49.382105',
              },
            ],
            planStartDate: '2024-03-05T00:00:00',
            planStart: '2024-03-05',
            planDoneDate: '2024-03-07T23:59:59',
            planDone: '2024-03-07',
            remind: null,
            actualStartDate: '2024-03-07T15:50:13.166309',
            actualDoneDate: '2024-03-07T15:50:49.990452',
            actualCheckDate: null,
            actualCloseDate: '2024-03-07T15:50:49.990456',
            childCount: 0,
            childCloseCount: 0,
            canCreateChild: false,
            creatorName: '邢黎敏',
            taskDocLinks: null,
            taskItemTags: [],
            autoCompletion: false,
            planSpendDay: null,
            realSpendDay: null,
            planSpendHours: null,
            realSpendHours: null,
            lastModificationTime: '2024-03-07T15:50:49.996147',
            lastModifierId: '3a0c30de-4720-871e-c121-852419671480',
            creationTime: '2023-07-04T16:19:49.381988',
            creatorId: '3a0c30de-4720-871e-c121-852419671480',
            id: '3a0c3229-59fe-978c-33e5-cc47515f3e08',
          },
          {
            relatedId: null,
            relatedName: null,
            relatedType: 4,
            subRelatedType: null,
            i18NRelatedType: null,
            i18NSubRelatedType: null,
            parentTaskId: null,
            parentTaskItemTitle: '',
            preTaskItemId: null,
            preTaskItemTitle: null,
            title: '123123123' + keyword,
            description: null,
            priority: null,
            taskProgress: null,
            status: 10,
            groupSort: 100,
            statusSort: 7500,
            responsibleOrganizeId: '3a0c30ef-7bfb-8a95-4515-76bfb56f1f18',
            checkerOrganizeId: null,
            responsibleUserId: null,
            responsibleUserName: '',
            responsibleUserAvatar: '',
            responsibleGroupId: '3a0c30df-141c-1d48-c869-d0d79de4cbc7',
            responsibleGroup: {
              groupId: '3a0c30df-141c-1d48-c869-d0d79de4cbc7',
              name: '默认分组',
              color: null,
              order: 0,
              isDefault: true,
              lastModificationTime: '2024-06-27T14:14:18.311121',
              lastModifierId: '3a0c30df-137e-811b-3853-b84ab63d7f8e',
              creationTime: '2023-07-04T10:19:04.605087',
              creatorId: '3a0c30d8-71a7-061f-5da2-4a37bda027e3',
              id: '3a0c30df-141c-1d48-c869-d0d79de4cbc7',
              extraProperties: {},
            },
            checkUsers: [],
            followUsers: [
              {
                userId: '3a0c30df-137e-811b-3853-b84ab63d7f8e',
                name: '张三',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2023-09-15T15:37:50.868416',
              },
              {
                userId: '3a0c30de-4720-871e-c121-852419671480',
                name: '邢黎敏',
                avatar: 'https://picsum.photos/200/300',
                creationTime: '2024-01-30T14:08:02.263956',
              },
            ],
            planStartDate: '2024-02-07T09:00:00',
            planStart: '2024-02-07 09:00',
            planDoneDate: '2024-02-07T23:59:59',
            planDone: '2024-02-07',
            remind: null,
            actualStartDate: null,
            actualDoneDate: null,
            actualCheckDate: null,
            actualCloseDate: null,
            childCount: 0,
            childCloseCount: 0,
            canCreateChild: false,
            creatorName: '苗玉洋',
            taskDocLinks: null,
            taskItemTags: [],
            autoCompletion: false,
            planSpendDay: null,
            realSpendDay: null,
            planSpendHours: null,
            realSpendHours: null,
            lastModificationTime: '2024-03-06T10:07:32.791858',
            lastModifierId: '3a0c30df-137e-811b-3853-b84ab63d7f8e',
            creationTime: '2023-09-15T15:37:35.209884',
            creatorId: '3a0c30df-aeb8-4ac2-3b48-2375227278bb',
            id: '3a0da9f2-ead3-69bc-5dbc-915fff2d8b80',
          },
        ];

        const result = data.map((item) => ({
          value: item.id,
          label: item.title,
          status: item.status,
          user: {
            id: item.responsibleUserId,
            avatar: item.responsibleUserAvatar,
            name: item.responsibleUserName,
          },
          source: {
            relatedName: item.relatedName,
            relatedType: item.i18NRelatedType,
            subRelatedType: item.i18NSubRelatedType,
          },
        }));
        setTimeout(
          // () => resolve([]),
          () => resolve(result),
          500,
        );
      }),
    helpDocument: 'http://www.baidu.com',
    HistoryRecordHandler: {
      [TaskLogTypeEnum.WorkSpend]: (data) => {
        data.children[1] = data.children[1]?.replace(/\[ ([a-zA-Z]+) \]/g, '[ 默认 ]');
        data.children[3] = data.children[3]?.replace(/\[ ([a-zA-Z]+) \]/g, '[ 默认 ]');
        return data;
      },
    },
    logoClick: () => {},
  };
</script>
<style lang="less" scoped></style>
