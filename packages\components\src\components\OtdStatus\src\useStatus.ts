import { getCurrentInstance } from 'vue';
import { OtdStatusActionType, OtdStatusOptionType } from './type';
import { useI18n } from '/@/hooks/web/useI18n';

export enum TaskStatusEnum {
  UnStarted = 10,
  Stopped = 20,
  OnGoing = 30,
  Reject = 40,
  Done = 50,
  Close = 60,
  Cancelled = 70,
}

export enum StatusColorEnum {
  Grey = 'grey',
  Orange = 'orange',
  Blue = 'blue',
  Green = 'green',
  Pink = 'pink',
  Red = 'red',
}

const { UnStarted, OnGoing, Stopped, Done, Reject, Close, Cancelled } = TaskStatusEnum;
export const StatusColorMap = {
  text: {
    [UnStarted]: '#676767',
    [OnGoing]: '#0038ff',
    [Stopped]: '#ff7a00',
    [Done]: '#c000ad',
    [Reject]: '#ec1010',
    [Close]: '#008872',
    [Cancelled]: '#676767',
  },
  border: {
    [UnStarted]: '#B9B9B9',
    [OnGoing]: '#91D5FF',
    [Stopped]: '#FFD591',
    [Done]: '#87E8DE',
    [Reject]: '#FFA39E',
    [Close]: '#B7EB8F',
    [Cancelled]: '#D3ADF7',
  },
  background: {
    [UnStarted]: '#9E9E9E',
    [OnGoing]: '#0085FF',
    [Stopped]: '#FFA800',
    [Done]: '#CA00B5',
    [Reject]: '#FF4646',
    [Close]: '#00A389',
    [Cancelled]: '#9E9E9E',
  },
};

export function useStatus() {
  const { t } = useI18n();
  const { props, emit } = getCurrentInstance()!;
  const { Grey, Orange, Blue, Green, Pink, Red } = StatusColorEnum;

  const actionMap: Record<TaskStatusEnum, OtdStatusActionType> = {
    [UnStarted]: { label: '', id: UnStarted, action: () => {} },
    // 开始
    [OnGoing]: {
      label: t('common.action.start'),
      id: OnGoing,
      action: ({ item }) => {
        updateValue(item, item.id);
      },
      type: 'primary',
    },
    // 暂停
    [Stopped]: {
      label: t('common.action.stop'),
      id: Stopped,
      action: ({ item }) => {
        updateValue(item, item.id);
      },
      type: 'default',
    },
    // 完成
    [Close]: {
      label: t('common.action.complete'),
      id: Close,
      action: ({ item }) => {
        updateValue(item, item.id);
      },
      type: 'primary',
    },
    // 退回
    [Reject]: {
      label: t('common.action.reject'),
      id: Reject,
      action: ({ item }) => {
        updateValue(item, item.id);
      },
      type: 'default',
    },
    // 确认
    [Done]: {
      label: t('common.action.confirm'),
      id: Done,
      action: ({ item }) => {
        updateValue(item, item.id);
      },
      type: 'primary',
    },
    // 取消
    [Cancelled]: {
      label: t('common.action.cancel'),
      id: Cancelled,
      action: ({ item }) => {
        updateValue(item, item.id);
      },
      type: 'default',
    },
  };
  const defaultOptions: OtdStatusOptionType<TaskStatusEnum>[] = [
    // 未开始
    {
      title: t('common.status.notStarted'),
      id: UnStarted,
      isProgress: ({ current }) => {
        return [Reject, Stopped, Done].includes(current) ? 0 : 1;
      },
      color: Grey,
      actions: [actionMap[Stopped], actionMap[OnGoing]],
    },
    // 待确认
    {
      title: t('common.status.verifying'),
      id: Done,
      isProgress: ({ current }) => {
        return current === Done ? 2 : 0;
      },
      color: Pink,
      actions: [actionMap[Reject], actionMap[Done]],
    },
    // 已暂停
    {
      title: t('common.status.onHold'),
      id: Stopped,
      isProgress: ({ current }) => {
        return current === Stopped ? 1 : 0;
      },
      color: Orange,
      actions: [actionMap[OnGoing]],
    },
    // 进行中
    {
      title: t('common.status.ongoing'),
      id: OnGoing,
      isProgress: ({ current }) => {
        return current === Done ? 1 : 2;
      },
      color: Blue,
      actions: [actionMap[Stopped], actionMap[Close]],
    },
    // 已完成
    {
      title: t('common.status.completed'),
      id: Close,
      isProgress: ({ current }) => {
        return [Cancelled].includes(current) ? 0 : 3;
      },
      color: Green,
    },
    // 已退回
    {
      title: t('common.status.rejected'),
      id: Reject,
      isProgress: ({ current }) => {
        return current === Reject ? 1 : 0;
      },
      actions: [actionMap[Stopped], actionMap[OnGoing]],
      color: Red,
    },
    // 已取消
    {
      title: t('common.status.cancelled'),
      id: Cancelled,
      isProgress: ({ current }) => {
        return current === Cancelled ? 3 : 0;
      },
      color: Grey,
    },
  ];

  async function updateValue(data, status) {
    const result = (await (props as any)?.beforeSelect?.(status)) ?? true;
    if (!result) return;
    status = result?.taskStatus || status;
    emit('update:value', status);
    emit('change', data, status);
  }

  return {
    actionMap,
    defaultOptions,
  };
}
