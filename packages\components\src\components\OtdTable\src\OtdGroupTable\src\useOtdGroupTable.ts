import { ComponentInternalInstance, getCurrentInstance } from 'vue';
export function useOtdGroupTable() {
  const { ctx } = getCurrentInstance() as ComponentInternalInstance & { ctx: any };
  function setHeaderSticky() {
    const parent = ctx.$el as HTMLElement;
    if (!parent) return;
    const collapseHead = parent.querySelector('.ant-collapse-header') as HTMLElement;
    const theads = parent.querySelectorAll('.ant-table-thead') as NodeListOf<HTMLElement>;
    const top = collapseHead?.offsetHeight ?? 0;
    theads.forEach((thead) => {
      thead.style.setProperty('top', `${top}px`);
    });
  }

  return {
    setHeaderSticky,
  };
}
