<template>
  <BasicMenuItem v-if="!menuHasChildren(item) || showMenus.length <= 0" v-bind="$props" />
  <SubMenu v-else :key="item.path" popupClassName="app-top-menu-popup" v-bind="$attrs">
    <template #title>
      <MenuItemContent v-bind="$props" :item="item" />
    </template>

    <template v-for="childrenItem in showMenus" :key="childrenItem.path">
      <BasicSubMenuItem v-bind="$props" :item="childrenItem" show-title />
    </template>
  </SubMenu>
</template>
<script lang="ts" setup>
  import { computed } from 'vue';
  import { Menu } from 'ant-design-vue';
  import BasicMenuItem from './BasicMenuItem.vue';
  import MenuItemContent from './MenuItemContent.vue';
  import { menuHasChildren } from '../tool';
  import { itemProps } from '../props';

  const SubMenu = Menu.SubMenu;
  const props = defineProps(itemProps());

  const showMenus = computed(() => {
    return props.item.children?.filter((item) => !item.meta?.hideMenu) ?? [];
  });
</script>
