// 普通表格样式
@table: ~'@{namespace}-table';
.@{table} {
  .@{table}-filter {
    margin-bottom: 12px;
  }
  &.@{table}-bordered {
    :deep(.ant-table-wrapper) {
      .ant-table {
        &:not(.ant-table-bordered) {
          .ant-table-thead > tr > th,
          .ant-table-tbody > tr > td {
            border-bottom-color: var(--otd-border-gray);
          }
          .ant-table-tbody:last-of-type > tr > td {
            border-bottom-color: transparent;
          }
        }
        .ant-table-thead > tr > th {
          background-color: var(--otd-header-bg);
        }
        .ant-table-tbody > tr > td,
        .ant-table-summary > tr > td {
          background-color: var(--otd-body-text);
        }
        .ant-table-container {
          > .ant-table-content,
          > .ant-table-body,
          > .ant-table-header,
          > .ant-table-summary {
            border: 1px solid var(--otd-border-gray);
            border-radius: var(--otd-small-radius);
          }
          .ant-table-summary {
            border-radius: var(--otd-border-radius);
          }
          .ant-table-header {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
            border-bottom-width: 0;
          }
          .ant-table-body {
            border-top-left-radius: 0;
            border-top-right-radius: 0;
            border-top-width: 0;
          }
        }
      }
    }
  }
  :deep(.ant-table-wrapper) {
    .ant-table {
      background-color: inherit;
      border-radius: var(--otd-small-radius);
      div.ant-table-summary {
        margin-top: 10px;
        box-shadow: unset;
        border-radius: var(--otd-border-radius);
        .ant-table-cell {
          border-bottom-width: 0;
          padding: 12px 16px;
          font-size: var(--otd-basic-size);
        }
      }
      .ant-table-selection-col {
        width: 32px;
      }
      &.ant-table-empty {
        .ant-table-body {
          > table {
            height: 100%;
          }
        }
        .ant-table-cell {
          border-bottom-width: 0;
        }
      }
      .otd-arrow {
        margin-right: 4px;
        margin-left: -4px;
        & + .ant-form-item {
          align-items: flex-start;
          .ant-form-row {
            width: calc(100% + 16px);
          }
        }
      }
      .ant-table-column-sorters {
        justify-content: flex-start;
        .ant-table-column-title {
          flex: unset;
        }
        .ant-table-column-sorter {
          margin-inline-start: 8px;
          color: var(--otd-basic-text);
          .ant-table-column-sorter-up {
            transform: translateY(1px);
          }
        }
      }
      .ant-table-thead,
      .ant-table-row {
        & > tr > th {
          color: var(--otd-header-text);
          font-weight: 500;
        }
        & > tr > td {
          color: var(--otd-basic-text);
        }
      }
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td,
      tfoot > tr > th,
      tfoot > tr > td {
        padding: var(--x) var(--y);
        background-color: var(--otd-basic-bg);
        line-height: 22px;
      }

      .ant-table-row,
      .ant-table-thead {
        .ant-table-selection {
          position: unset;
        }
        &:hover {
          .ant-checkbox,
          .ant-radio {
            display: inline-flex;
          }
        }
        .ant-checkbox-wrapper-checked .ant-checkbox,
        .ant-radio-wrapper-checked .ant-radio,
        .ant-checkbox.ant-checkbox-indeterminate {
          display: inline-flex;
        }
        .ant-checkbox,
        .ant-radio {
          display: none;
          position: absolute;
          left: 16px;
        }
      }
      .ant-table-container {
        .ant-table-expanded-row {
          > .ant-table-cell {
            background-color: var(--otd-expand-bg);
          }
        }
        .ant-table-header,
        table,
        table > thead > tr:first-child > *:first-child {
          border-radius: unset;
        }
      }

      .ant-table-thead {
        .ant-table-cell-scrollbar {
          background-color: var(--otd-basic-bg);
        }
      }
      .ant-table-tbody {
        > tr {
          &.ant-table-row:hover > td,
          > td.ant-table-cell-row-hover {
            background-color: var(--otd-gray-hover);
          }
        }
      }
    }
  }

  &.is-show-select {
    :deep(.ant-table-wrapper) {
      .ant-table-row,
      .ant-table-thead {
        .ant-checkbox,
        .ant-radio {
          display: inline-flex;
        }
      }
    }
  }

  &.@{table}-resize {
    height: 100%;
    display: flex;
    flex-direction: column;
    > .ant-table-wrapper {
      flex: 1;
      overflow: hidden;
      :deep(> .ant-spin-nested-loading) {
        height: 100%;
        > .ant-spin-container {
          height: 100%;
          display: flex;
          flex-direction: column;
          > .ant-table {
            flex: 1;
            overflow: hidden;
            > .ant-table-container {
              height: 100%;
              display: flex;
              flex-direction: column;
              > .ant-table-body {
                flex: 1;
                overflow-x: auto;
                overflow-y: auto !important;
                max-height: unset !important;
              }
              // > table {
              //   height: 100%;
              // }
            }
          }
        }
      }
    }
  }
}
