<template>
  <div class="otd-ai-subtask otd-ai-flash" v-if="loading || subtaskList.length">
    <OtdTable :data-source="loading ? SkeletonList : subtaskList" @register="register" />
    <div class="otd-ai-subtask__content">
      <Button class="otd-ai-btn" @click="cancelAiSubtask!">
        {{ t('common.cancelText') }}
      </Button>
      <Button class="otd-ai-btn" @click="getAiSubtaskList!" :loading="confirmLoading">
        <template #icon> <BasicIcon icon="otd-ai|svg" /> </template>
        {{ t('common.subtask.aiRebuild') }}
      </Button>

      <Button class="otd-ai-btn" @click="confirmAiSubtask!" type="primary" :loading="confirmLoading">
        {{ t('common.okText') }}
      </Button>
    </div>
  </div>
</template>
<script lang="tsx" setup>
  import { Button, Input, SkeletonButton } from 'ant-design-vue';
  import { OtdEditCellItem } from '/@/components/OtdEditCell';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { BasicIcon } from '/@/components/BasicIcon';
  import { OtdTable, useTable } from '/@/components/OtdTable';
  import { PropType, ref } from 'vue';
  import { ISetSubtaskInputDetail } from '../type';
  import { OtdMoreAction } from '/@/components/OtdMoreAction';
  import { ERROR_COLOR } from '/@/setting';
  const props = defineProps({
    subtaskList: {
      type: Array as PropType<ISetSubtaskInputDetail[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    parentTaskId: {
      type: String,
      default: '',
    },
    responsibleUserId: {
      type: String,
      default: '',
    },
    confirmLoading: {
      type: Boolean,
      default: false,
    },
    cancelAiSubtask: {
      type: Function,
      required: true,
    },
    getAiSubtaskList: {
      type: Function,
      required: true,
    },
    confirmAiSubtask: {
      type: Function,
      required: true,
    },
  });
  const emit = defineEmits(['handleAi']);

  const { t } = useI18n();
  const SkeletonList = ref(new Array(4).fill(null));

  function deleteSubtask(_, index) {
    props.subtaskList.splice(index, 1);
  }

  const [register, {}] = useTable({
    columns: [
      {
        title: t('common.subtask.taskName'),
        dataIndex: 'title',
        customRender: ({ record, index }) => {
          return (
            <OtdEditCellItem class="otd-table-action" offset={[-10, -16]} isFull>
              {props.loading ? (
                <SkeletonButton class="otd-ai-subtask__skeleton" block active size="small" />
              ) : (
                <Input class="otd-text-input focus-border otd-input" v-model:value={record.title} bordered={false} />
              )}
              <OtdMoreAction
                actions={[
                  {
                    icon: 'otd-icon-a-catdeletesize24',
                    color: ERROR_COLOR,
                    action: () => deleteSubtask(record, index),
                  },
                ]}
                expand-number={1}
              />
            </OtdEditCellItem>
          );
        },
      },
    ],
    bordered: true,
    pagination: false,
  });
</script>
<style lang="less" scoped>
  .otd-ai-subtask {
    border-radius: 6px;
    padding-bottom: 10px;
    margin-top: 10px;
    :deep(&__skeleton) {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 4px;
    }

    &__content {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 10px;
      .otd-ai-btn {
        margin-left: 10px;
      }
    }
  }
</style>
