import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useI18n } from '/@/hooks/web/useI18n';
import { ref } from 'vue';

export function useOtdDashBoard() {
  const { t } = useI18n();
  const dateRangeEnum = {
    ThisWeek: t('common.datePicker.ThisWeek'),
    LastWeek: t('common.datePicker.LastWeek'),
    ThisMonth: t('common.datePicker.ThisMonth'),
    LastMonth: t('common.datePicker.LastMonth'),
    ThisYear: t('common.datePicker.ThisYear'),
    LastYeat: t('common.datePicker.LastYeat'),
  };
  // 日期范围预设
  const dateRangeMap: { label: string; value: [Dayjs, Dayjs] }[] = [
    // 本周
    { label: dateRangeEnum.ThisWeek, value: [dayjs().startOf('week'), dayjs().endOf('week')] },
    // 上周
    {
      label: dateRangeEnum.LastWeek,
      value: [dayjs().add(-1, 'week').startOf('week'), dayjs().add(-1, 'week').endOf('week')],
    },
    // 本月
    { label: dateRangeEnum.ThisMonth, value: [dayjs().startOf('month'), dayjs().endOf('month')] },
    // 上月
    {
      label: dateRangeEnum.LastMonth,
      value: [dayjs().add(-1, 'month').startOf('month'), dayjs().add(-1, 'month').endOf('month')],
    },
    // 今年
    { label: dateRangeEnum.ThisYear, value: [dayjs().startOf('year'), dayjs().endOf('year')] },
    // 去年
    {
      label: dateRangeEnum.LastYeat,
      value: [dayjs().add(-1, 'year').startOf('year'), dayjs().add(-1, 'year').endOf('year')],
    },
  ];

  const collectDashboard = ref<Record<string, any>>({});
  // 收集器
  function registerDashboard(key: string | number, dashboard) {
    collectDashboard.value[key] = dashboard;
  }

  return {
    dateRangeEnum,
    dateRangeMap,
    registerDashboard,
    collectDashboard,
  };
}
