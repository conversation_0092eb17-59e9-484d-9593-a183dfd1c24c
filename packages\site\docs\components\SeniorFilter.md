# 高级筛选

<p style="font-size:26px">代码演示</p>

## 基础

<demo src="../demo/SeniorFilter/basic.vue" title="基础"></demo>

### 属性

| 参数             | 说明                                | 类型     | 可选值 | 默认值                      | 版本 |
| ---------------- | ----------------------------------- | -------- | ------ | --------------------------- | ---- |
| options          | 参数字段，参下表[options](#options) | []       | --     | --                          | 1.0  |
| placeholder      | 标题                                | String   | --     | 筛选                        | 1.0  |
| change           | 改变事件                            | Function | --     | ({value, type}) => {}       | 1.0  |
| getFilterList    | 获取保存的筛选列表                  | Function | --     | () => {}                    | 1.0  |
| saveFilter       | 保存筛选条件                        | Function | --     | (data) => {}                | 1.0  |
| updateFilterItem | 修改已保存筛选条件                  | Function | --     | ({ saveData, index }) => {} | 1.0  |
| removeFilterItem | 删除已保存筛选条件                  | Function | --     | ( { item, index }) => {}    | 1.0  |
| isShowSaveFilter | 是否显示保存当前筛选条件            | Boolean  | --     | true                        | 1.0  |
| ref              | 参下表[Ref](#Ref)                   | --       | --     | --                          | 1.0  |

### <span id='options'>options</span>

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| id | 唯一值 | String | -- | -- | 1.0 |
| type | 不使用自定义组件时的控件类型.为空表示下拉框 | String | input,undefined | -- | 1.0 |
| label | 筛选条件下拉框的 label | String | -- | -- | 1.0 |
| value | 向后端传递的字段名 | String | -- | -- | 1.0 |
| rangeKey | 适用于日期等其它有两个字段名的组件 | [String, String] | -- | -- | 1.0 |
| mergeCondition | -- | -- | -- | -- | 1.0 |
| conditions | -- | -- | -- | -- | 1.0 |
| valueOptions | 如不传 type,且不使用自定义组件,为默认下拉框的数据源 | [] | -- | -- | 1.0 |
| valueComponent | 自定义组件 | JSX | -- | -- | 1.0 |
| valueProps | 组件 props 接收的属性值 | {} | -- | -- | 1.0 |
| valueApi | 加载数据源 | Function | -- | -- | 1.0 |
| valueHandler | 处理向服务端传递的选中数据 | Function | -- | (data, index?: number) => any | 1.0 |

### <span id='Ref'>Ref</span>

| 参数            | 说明           | 类型     | 可选值 | 默认值             | 版本 |
| --------------- | -------------- | -------- | ------ | ------------------ | ---- |
| getDefaultValue | 获取筛选列表   | Function | --     | () => filterResult | 1.0  |
| resetFilter     | 清除已选择筛选 | Function | --     | () =>　｛｝        | 1.0  |
