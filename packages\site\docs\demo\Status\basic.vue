<template>
  <OtdStatus v-model:value="currentId" :icon-type="TaskType" :arrow="visible" :disabled="disabled">
    <template #sub-title="{ option }">
      <span>目前该任务{{ option?.title }}</span>
    </template>
  </OtdStatus>
  <h5 style="margin-top: 20px; margin-bottom: 20px">点击下方按钮切换交互</h5>
  <div style="margin-bottom: 20px">
    <RadioGroup v-model:value="currentId" name="radioGroup">
      <Radio :value="UnStarted">未开始</Radio>
      <Radio :value="OnGoing">进行中</Radio>
      <Radio :value="Reject">已退回</Radio>
      <Radio :value="Done">待确认</Radio>
      <Radio :value="Close">已完成</Radio>
      <Radio :value="Stopped">已暂停</Radio>
      <Radio :value="Cancelled">已取消</Radio>
    </RadioGroup>
  </div>
  <div style="margin-bottom: 20px">
    <RadioGroup v-model:value="visible" name="radioGroup">
      <Radio :value="true">展示箭头</Radio>
      <Radio :value="false">隐藏箭头</Radio>
    </RadioGroup>
  </div>
  <Checkbox v-model:checked="disabled">禁用</Checkbox>
  <!-- <RadioGroup v-model:value="disabled" name="radioGroup">
    <Radio :value="true">禁用</Radio>
    <Radio :value="false">不禁用</Radio>
  </RadioGroup> -->
</template>
<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import { OtdStatus, RadioGroup, Radio, TaskStatusEnum, Checkbox } from '@otd/otd-ui';

  const visible = ref<boolean>(true);
  const disabled = ref<boolean>(false);
  const { UnStarted, Stopped, OnGoing, Close, Reject, Done, Cancelled } = TaskStatusEnum;
  const currentId = ref<string | number>(OnGoing);
  const TaskType = ref<string>();

  onMounted(() => {
    TaskType.value = 'Bug'
  })
</script>
<style lang="less" scoped></style>
