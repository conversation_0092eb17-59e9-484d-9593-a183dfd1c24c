import { getCurrentInstance, ref, unref } from 'vue';
import { nextTick } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { UserOptionItemType } from '/@/components/OtdUserSearch/src/type';

export enum AtMentionEnum {
  // 用户
  User = 'User',
  // 任务
  Task = 'Task',
}

export function useMentionPopover() {
  const { t } = useI18n();
  const { proxy, emit } = getCurrentInstance()!;
  const currentIndex = ref(0);
  const showDropdown = ref(false);
  const loading = ref(true);
  const dropdownData = ref<UserOptionItemType[]>([]);
  const popoverSeat = ref([0, 0]);

  const searchValue = ref();
  const currentTab = ref(AtMentionEnum.User);
  const atMentionType = [
    // 用户
    {
      label: t('common.user'),
      value: AtMentionEnum.User,
    },
    // 任务
    {
      label: t('common.task'),
      value: AtMentionEnum.Task,
    },
  ];

  // 滚动到中间
  function scrollToCenter() {
    const { popoverRef } = proxy?.$refs as any;
    const container = unref(popoverRef).querySelector('.scrollbar__wrap');
    const target = unref(popoverRef).querySelector('.is-active');
    if (!container || !target) return;
    const containerRect = container.getBoundingClientRect();
    const targetRect = target.getBoundingClientRect();
    const targetTopInContainer = targetRect.top - containerRect.top;
    const scrollTop = targetTopInContainer - containerRect.height / 2 + targetRect.height / 2;
    container.scrollBy({ top: scrollTop, left: 0, behavior: 'smooth' });
  }
  function setSearchValue(value) {
    searchValue.value = value;
  }
  // 设置高亮
  function setActive(step: 1 | -1) {
    let index = currentIndex.value + step;
    const length = unref(dropdownData).length;
    if (index >= length) {
      index = 0;
    } else if (index < 0) {
      index = length - 1;
    }
    currentIndex.value = index;
    nextTick(() => {
      scrollToCenter();
    });
  }
  // 设置弹出位置
  function setDropdownSeat(seat: [number, number]) {
    popoverSeat.value = seat;
  }
  // 获取高亮数据
  function getActiveData() {
    return {
      data: unref(dropdownData)?.[unref(currentIndex)],
      tab: unref(currentTab),
    };
  }
  // 打开弹出层
  function setShow(status: boolean = true) {
    showDropdown.value = status;
  }
  // 设置弹出层数据
  function setLoading(status: boolean = true) {
    loading.value = status;
  }
  // 设置弹出层数据
  function setData(data: UserOptionItemType[]) {
    dropdownData.value = data;
    currentIndex.value = 0;
    loading.value = false;
  }
  // 点击选择事件
  function handleSelect(record, index) {
    currentIndex.value = index;
    showDropdown.value = false;
    emit('select', record, unref(currentTab), unref(currentIndex));
  }
  // 搜索事件
  function handleSearch(isChangeTab: boolean = false) {
    emit('search', unref(searchValue), unref(currentTab), isChangeTab);
  }
  // 切换Tab事件
  function handelChangeTab() {
    handleSearch(true);
  }

  return {
    showDropdown,
    loading,
    popoverSeat,
    currentTab,
    searchValue,
    currentIndex,
    dropdownData,
    atMentionType,
    setSearchValue,
    setActive,
    setDropdownSeat,
    getActiveData,
    setShow,
    setData,
    setLoading,
    handleSelect,
    handleSearch,
    handelChangeTab,
  };
}
