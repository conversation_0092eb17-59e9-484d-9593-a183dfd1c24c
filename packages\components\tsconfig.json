{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "lib": ["dom", "esnext"],
    "skipLibCheck": true,

    /* Bundler mode */
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "noLib": false,
    "jsx": "preserve",
    "forceConsistentCasingInFileNames": true,
    "allowSyntheticDefaultImports": true,
    "strictFunctionTypes": false, // ↓禁用函数参数双向协变检查。
    "baseUrl": ".", // ↓解析非相对模块名的基准目录。查看 模块解析文档了解详情。
    "allowJs": true, // ↓允许编译javascript文件。
    "experimentalDecorators": true, // ↓启用实验性的ES装饰器。

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": false,
    "removeComments": true,
    "types": ["vite/client"],
    "paths": {
      "/@/*": ["src/*"],
      "/#/*": ["types/*"],
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "vite/**/*.ts",
    "types/**/*.d.ts",
    "types/**/*.ts",
    "vite.config.ts"
  ],
  "exclude": ["node_modules", "dist", "**/*.js"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
