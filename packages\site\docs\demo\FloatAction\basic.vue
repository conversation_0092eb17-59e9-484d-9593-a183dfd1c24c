<template>
  <OtdFloatAction :actions="floatAction" />
</template>
<script lang="ts" setup>
  import { message, MoreActionItem, OtdFloatAction } from '@otd/otd-ui';
  import { reactive } from 'vue';

  const floatAction: MoreActionItem[] = reactive([
    {
      id: 1,
      icon: 'otd-icon-dianzan1',
      name: '点击测试',
      action: () => {
        message.success('点击测试');
      },
    },
    {
      id: 2,
      color: 'red',
      icon: 'otd-icon-lingdang-xianxing',
      name: '悬浮测试',
      pin: true,
      action: () => {
        message.success('悬浮测试');
      },
    },
    {
      id: 3,
      color: 'lightblue',
      icon: 'otd-icon-qianzhirenwu',
      name: '移动测试',
      action: () => {
        message.success('移动测试');
      },
    },
  ]);
</script>
<style lang="less" scoped></style>
