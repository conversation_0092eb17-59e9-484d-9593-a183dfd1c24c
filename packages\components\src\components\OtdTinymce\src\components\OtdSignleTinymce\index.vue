<template>
  <div>
    <Tinymce
      ref="editor"
      :toolbar="signleToolbar"
      :plugins="signlePlugin"
      :options="tinyOptions"
      :placeholder="placeholder"
      v-bind="getComProps"
      v-model="tinyValue"
      is-simple
      @change="handleChange"
      @blur="handleBlur"
    />
  </div>
</template>
<script lang="ts" setup>
  import { ref, watchEffect, unref, useAttrs } from 'vue';
  import Tinymce from '../../Editor.vue';
  import { plugins, toolbar } from './simple-tiny';
  import { useTinyOption } from './useTinyOption';
  import { computed } from 'vue';
  import { getEmits, getProps } from './props';
  import { UserOptionItemType } from '/@/components/OtdUserSearch';
  import { omit } from 'lodash-es';

  const props = defineProps(getProps());

  const emits = defineEmits(getEmits());

  const attrs = useAttrs();
  const getComProps = computed(() => ({
    ...attrs,
    ...omit(props, [
      'toolbar',
      'plugins',
      'options',
      'getMentionsRequest',
      'detail',
      'defaultValue',
      'leftToolbar',
      'rightToolbar',
    ]),
  }));

  const signleToolbar = computed<string[]>(() => {
    const data = new Array(2);
    data[0] = `${toolbar[0]} ${props.leftToolbar}`;
    data[1] = `${props.rightToolbar} ${toolbar[1]}`;
    return data;
  });
  const signlePlugin = computed(() => {
    return [...plugins, ...props.plugins];
  });

  const tinyValue = ref<string | undefined>('');
  let lastValue: string | undefined = '';
  watchEffect(() => {
    lastValue = props.modelValue ?? props.value ?? props.defaultValue;
    tinyValue.value = lastValue;
  });
  const { tinyOptions } = useTinyOption(props.options);

  function handleChange(content) {
    emits('change', content);
  }

  function handleBlur(e) {
    const value = e.value;
    const notChange = lastValue === value;
    emits('update:modelValue', value);
    emits('update:value', value);
    emits('blur', { ...e, value }, notChange);
  }

  const editor = ref();
  defineExpose({
    getEditorId: () => editor.value.getEditorId(),
    getMentionUser() {
      const { plugins } = editor.value.editorRef;
      return plugins.mention?.getMentionUser?.() ?? [];
    },
    setMentionUser(data: UserOptionItemType) {
      const { plugins } = editor.value.editorRef;
      if (plugins.mention) {
        plugins.mention.setMentionUser(data);
      }
    },
    getText: () => unref(editor).getText(),
  });
</script>
<style lang="less" scoped></style>
