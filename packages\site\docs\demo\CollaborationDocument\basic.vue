<template>
  <OtdCollaborationDocument v-model:value="text" />
  <OtdCollaborationDocument v-model:value="dataList" @change="handleChange" @remove="handleRemove" />
</template>
<script lang="tsx" setup>
  import { OtdCollaborationDocument, ISetTaskDocLinkInputDetail } from '@otd/otd-ui';
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';
  const text = ref();
  const dataList = ref<ISetTaskDocLinkInputDetail[]>([
    {
      id: '1',
      name: '文档名称1',
      docLink: 'https://www.baidu.com',
    },
    {
      id: '2',
      name: '文档名称2',
      docLink: 'https://www.baidu.com',
    },
  ]);

  const handleChange = (value: ISetTaskDocLinkInputDetail[], record: ISetTaskDocLinkInputDetail) => {
    record.id = Date.now().toString();
    console.log(record);
    dataList.value = value;
    message.success('操作成功');
  };

  const handleRemove = (row) => {
    const { index, record } = row;
    console.log(index);
    console.log(record);
    console.log(dataList.value);
    dataList.value.splice(index, 1);
    message.success('操作成功');
  };
</script>
<style lang="less" scoped></style>
