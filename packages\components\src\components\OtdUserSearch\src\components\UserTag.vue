<template>
  <div class="otd-user-option">
    <OtdAvatar :url="user.img" />
    <span>{{ user.label }}</span>
  </div>
</template>
<script lang="ts" setup>
  import { PropType } from 'vue';
  import { UserOptionItemType } from '../type';
  import { OtdAvatar } from '/@/components/OtdAvatar';

  defineProps({
    user: {
      type: Object as PropType<UserOptionItemType>,
      default: () => ({}),
    },
  });
</script>
<style lang="less" scoped>
  .otd-user-option {
    display: flex;
    align-items: center;
    column-gap: 4px;
    min-width: fit-content;
  }
</style>
