import { Frequency, WeekdayStr } from 'rrule';
import { ByType } from '../../../type';
import { useI18n } from '/@/hooks/web/useI18n';

export const WeekdayStrs: WeekdayStr[] = ['MO', 'TU', 'WE', 'TH', 'FR', 'SA', 'SU'];
export function useRepeatOption() {
  const { t } = useI18n();
  const { DAILY, WEEKLY, MONTHLY, YEARLY } = Frequency;
  const freqTextMap: Partial<Record<Frequency, string>> = {
    [DAILY]: t('common.repeatPicker.days'),
    [WEEKLY]: t('common.repeatPicker.weeks'),
    [MONTHLY]: t('common.repeatPicker.months'),
    [YEARLY]: t('common.repeatPicker.years'),
  };
  const byTypeMap: Record<ByType, string> = {
    day: t('common.repeatPicker.byDay'),
    week: t('common.repeatPicker.byWeek'),
  };
  const weekTextMap: Record<WeekdayStr, string> = {
    SU: t('common.repeatPicker.Sunday'),
    MO: t('common.repeatPicker.Monday'),
    TU: t('common.repeatPicker.Tuesday'),
    WE: t('common.repeatPicker.Wednesday'),
    TH: t('common.repeatPicker.Thursday'),
    FR: t('common.repeatPicker.Friday'),
    SA: t('common.repeatPicker.Saturday'),
  };
  const numTextMap = {
    1: t('common.repeatPicker.1st'),
    2: t('common.repeatPicker.2nd'),
    3: t('common.repeatPicker.3rd'),
    other: (num) => t('common.repeatPicker.numth', { n: num }),
  };

  // 重复周期数量选项
  const intervalOptions = new Array(30)
    .fill(0)
    .map((_, index) => ({ label: `${t('common.repeatPicker.every')} ${index + 1}`, value: index + 1 }));
  // 重复周期类型选项
  const freqOptions = [DAILY, WEEKLY, MONTHLY, YEARLY].map((key) => ({ label: freqTextMap[key], value: key }));
  // 月类型选项
  const byTypeOptions = (Object.keys(byTypeMap) as ByType[]).map((key) => ({
    label: byTypeMap[key],
    value: key,
  }));
  // 周次选项
  const typeIntervalOptions = new Array(5).fill(0).map((_, index) => {
    const count = index + 1;
    return {
      label: `${count > 3 ? numTextMap['other'](count) : numTextMap[count]}`,
      value: count,
    };
  });
  // 星期选项
  const weekOptions = (Object.keys(weekTextMap) as WeekdayStr[]).map((key) => ({
    label: weekTextMap[key],
    value: key,
  }));
  return {
    intervalOptions,
    freqOptions,
    byTypeOptions,
    typeIntervalOptions,
    weekOptions,
  };
}
