## 漫游式引导

<demo src="../demo/Tour/basic.vue" title="漫游式引导"></demo>

## 方法 useTourDriver 返回值

| 参数         | 说明         | 版本 |
| ------------ | ------------ | ---- |
| createDriver | 创建引导对象 | 1.0  |

## 方法 createDriver

参数参考 [driver](https://driverjs.com/docs/configuration)，title 和 description 支持 VNode 写法

## steps 新增参数

| 参数    | 说明                       | 类型   | 可选值           | 默认值 | 版本 |
| ------- | -------------------------- | ------ | ---------------- | ------ | ---- |
| trigger | 触发形式                   | String | `click`, `hover` |        | 1.0  |
| delay   | 触发后进入下一步延迟(毫秒) | Number | --               | 200    | 1.0  |
