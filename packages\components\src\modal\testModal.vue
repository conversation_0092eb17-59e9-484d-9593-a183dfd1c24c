<template>
  <BasicModal @register="register">111</BasicModal>
</template>
<script lang="ts" setup>
  import { BasicModal, useModal } from '../components';
  import { useGlobalConfig } from '../hooks/web/useGlobalConfig';

  const [register, { openModal }] = useModal();

  const { getGlobalProvide } = useGlobalConfig();
  console.log(getGlobalProvide());

  defineExpose({
    openModal,
  });
</script>
<style lang="less" scoped></style>
