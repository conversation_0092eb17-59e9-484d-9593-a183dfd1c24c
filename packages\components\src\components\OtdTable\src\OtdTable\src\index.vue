<template>
  <div
    ref="tableWrapperRef"
    class="otd-table"
    :class="{
      'otd-table-bordered': getProps.bordered,
      'otd-table-resize': getProps.canResize,
      'is-show-select': getProps.defaultShowSelect,
    }"
  >
    <OtdTabFilter
      ref="tabFilterRef"
      class="otd-table-filter"
      filter-placement="right"
      :filter-actions="getTableSetting"
      :form-config="formSetting"
      :hide-filter="!!$slots.filter"
      :bordered="formBorder"
      @submit="handleSearchInfoChange"
    >
      <template #filter>
        <slot name="filter"></slot>
      </template>
    </OtdTabFilter>
    <Table ref="tableElRef" v-bind="getBindValues" @change="handleTableChange">
      <template #[slot]="data" v-for="(_, slot) in $slots" :key="slot">
        <slot :name="slot" v-bind="data"></slot>
      </template>
      <template #expandIcon="{ expanded, onExpand, expandable, record }: any">
        <i
          class="otdIconfont otd-arrow"
          :class="{ 'otd-arrow-active': expanded, 'otd-icon-a-Statusdefault2': !!expandable }"
          @click.stop="onExpand(record)"
        />
      </template>
      <template #summary v-if="getProps.summary">
        <TableSummary fixed>
          <TableSummaryRow v-for="(item, index) in summaryData" :key="index">
            <template v-for="(column, i) in item" :key="i">
              <TableSummaryCell
                v-if="column !== undefined && tableShowColumns[i] !== undefined"
                :index="i"
                :col-span="column?.colSpan"
              >
                <CustomSummaryRender v-if="column" :summary="column" :column="tableShowColumns[i]" />
              </TableSummaryCell>
            </template>
          </TableSummaryRow>
        </TableSummary>
      </template>
    </Table>
  </div>
</template>
<script lang="tsx" setup>
  import type { TableActionType, TableInstancePropsType, TablePropsType } from './types';
  import type { Recordable } from '/#/global';
  import { computed, nextTick, onMounted, ref, unref, useAttrs } from 'vue';
  import { Table, TableSummary, TableSummaryCell, TableSummaryRow } from 'ant-design-vue';
  import { OtdTabFilter } from '/@/components/OtdTabFilter';
  import { getEmits, tableProps } from './props';

  import { useOtdTable } from './hooks/useOtdTable';
  import { usePagination } from './hooks/usePagination';
  import { useDataSource } from './hooks/useDataSource';
  import { useTableForm } from './hooks/useTableForm';
  import { useRowSelection } from './hooks/useRowSelection';
  import { BasicFormProps } from '/@/components/BasicForm';
  import { useTableExpand } from './hooks/useTableExpand';
  import { useTableScrollTo } from './hooks/useScrollTo';
  import { useCustomRow } from './hooks/useCustomRow';
  import { useDraggableRow } from './hooks/useDraggableRow';

  const props = defineProps(tableProps());
  const emit = defineEmits(getEmits());
  const attrs = useAttrs();
  const tableElRef = ref(null);
  const innerPropsRef = ref<Partial<TablePropsType>>();
  const getProps = computed<TableInstancePropsType>(() => {
    return { ...props, ...unref(innerPropsRef) } as TablePropsType;
  });
  const { getPaginationInfo, getPagination, setPagination, setShowPagination, getShowPagination } =
    usePagination(getProps);

  const {
    tableLoading,
    tableDataSource,
    getDataSourceRef,
    makeRemoteRequest,
    handleTableChange,
    reload,
    getLoading,
    setLoading,
    getDataSource,
    setTableData,
    updateTableDataRecord,
    deleteTableDataRecord,
    insertTableDataRecord,
    findTableDataRecord,
  } = useDataSource(getProps, {
    getPaginationInfo,
    setPagination,
  });

  const tableWrapperRef = ref();
  const {
    getRowSelectionRef,
    getRowSelection,
    getSelectRows,
    getSelectRowKeys,
    setSelectedRowKeys,
    clearSelectedRowKeys,
    deleteSelectRowByKey,
    setSelectedRows,
  } = useRowSelection(getProps, tableDataSource, emit);
  const { TableSetting, tableShowColumns, summaryData, CustomSummaryRender } = useOtdTable(getProps, {
    getPaginationInfo,
    reload,
  });
  const getTableSetting = computed(() =>
    unref(getProps).showTableSetting ? [...unref(getProps).settingConfig, ...TableSetting] : undefined,
  );
  const { handleSearchInfoChange } = useTableForm({ fetch: makeRemoteRequest });
  const { getExpandOption, expandAll, expandRows, collapseAll } = useTableExpand(getProps, tableDataSource, emit);
  const { initSortable } = useDraggableRow(getProps, tableDataSource!, getExpandOption);
  const { scrollTo } = useTableScrollTo(tableElRef, getDataSourceRef);

  const formSetting = computed<BasicFormProps>(() => ({
    autoSubmitOnEnter: true,
    ...(unref(getProps).formConfig ?? {}),
  }));

  const { customRow } = useCustomRow(getProps, {
    setSelectedRowKeys,
    getSelectRowKeys,
    clearSelectedRowKeys,
    emit,
  });

  const getBindValues = computed(() => {
    let propsData: Recordable = {
      ...{ scroll: unref(getProps).canResize ? { y: 100 } : undefined },
      ...attrs,
      customRow,
      ...unref(getProps),
      columns: unref(tableShowColumns),
      rowSelection: unref(getRowSelectionRef),
      dataSource: unref(tableDataSource),
      loading: unref(tableLoading),
      bordered: false,
      pagination: unref(getPaginationInfo),
      ...unref(getExpandOption),
    };
    return propsData;
  });

  function setProps(props: Partial<TablePropsType>) {
    innerPropsRef.value = { ...unref(innerPropsRef), ...props };
  }

  function getTableProps() {
    return unref(getProps) as TablePropsType;
  }

  function getShowColumns() {
    return unref(tableShowColumns);
  }

  const tabFilterRef = ref();

  const tableAction: TableActionType = {
    reload,
    getPagination,
    setPagination,
    getShowPagination,
    setShowPagination,
    getShowColumns,
    setProps,
    getTableProps,
    getRowSelection,
    getSelectRows,
    getSelectRowKeys,
    setSelectedRowKeys,
    clearSelectedRowKeys,
    deleteSelectRowByKey,
    setSelectedRows,
    getLoading,
    setLoading,
    getDataSource,
    setTableData,
    updateTableDataRecord,
    deleteTableDataRecord,
    insertTableDataRecord,
    findTableDataRecord,
    expandAll,
    expandRows,
    collapseAll,
    scrollTo,
  };
  emit('register', tableAction, () => unref(tabFilterRef));

  onMounted(() => {
    nextTick(() => {
      if (unref(getProps).remoteRequest && unref(getProps).immediateRequest) {
        makeRemoteRequest();
      }
      if (unref(getProps).draggable) {
        initSortable();
      }
    });
  });

  defineExpose({
    ...tableAction,
  });
</script>
<style lang="less" scoped>
  @import './style.less';
  .otd-table {
    --x: v-bind(padding[0] + 'px');
    --y: v-bind(padding[1] + 'px');
  }
</style>
