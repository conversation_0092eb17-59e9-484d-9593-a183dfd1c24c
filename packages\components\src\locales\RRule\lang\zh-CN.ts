const lang = {
  dayNames: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
  monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
  getterText: {
    day: '天',
    week: '周',
    month: '月',
    year: '年',
    daily: '每日',
    weekly: '每周',
    monthly: '每月',
    yearly: '每年',
    until: '直到',
    and: '和',
    on: '的',
    at: '在',
    in: '在',
    every: '每',
    days: '天',
    weeks: '周',
    months: '个月',
    years: '年',
    first: '第一',
    second: '第二',
    third: '第三',
    fourth: '第四',
    last: '最后',
    weekday: '工作日',
    weekend: '周末',
    for: '重复',
    times: '次',
    'on the': '的第',
    st: '个',
    nd: '个',
    rd: '个',
    th: '个',
  },
  getter(text) {
    return this.getterText[text] ?? text;
  },
};

export default lang;
