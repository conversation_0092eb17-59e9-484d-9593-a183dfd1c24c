import { ComponentInternalInstance, Component, PropType, VNode, createVNode, render } from 'vue';
import { Recordable } from '/#/global';

type ModalProps = {
  component: (Component & { __name?: string }) | undefined;
  props?: Record<string, any>;
  instance: ComponentInternalInstance;
};

export class ModalSignle {
  private modal: VNode | undefined = undefined;
  private static instance: Recordable<ModalSignle> = {};
  private component: ModalProps['component'] = undefined;
  private instance: ModalProps['instance'];
  private props: ModalProps['props'] = undefined;
  constructor(name: string, { component, props, instance }: ModalProps) {
    this.instance = instance;
    const modal = ModalSignle.instance[name];
    if (!modal || modal.instance !== instance) {
      ModalSignle.instance[name] = this;
    }
    this.component = component;
    this.props = props;
    ModalSignle.instance[name]?.setProps(props);
    return ModalSignle.instance[name];
  }

  private createContainer = () => {
    if (!this.component) return undefined;
    const container = document.createElement('div');
    document.body.append(container);
    const vnode = createVNode(this.component, { ...this.props });
    const { provides, appContext } = this.instance as any;
    vnode.appContext = { ...appContext, provides };

    render(vnode, container);
    vnode.component && (vnode.component.parent = this.instance);
    return vnode;
  };

  setProps(props) {
    if (this.modal) {
      this.modal.props = props;
    }
  }

  private createModal = () => {
    return this.createContainer();
  };

  getModal() {
    if (!this.modal) {
      this.modal = this.createModal();
    }
    return this.modal?.component?.exposed;
  }
}

export const ModelProps = {
  container: {
    type: Function as PropType<() => any>,
    default: undefined,
  },
};
