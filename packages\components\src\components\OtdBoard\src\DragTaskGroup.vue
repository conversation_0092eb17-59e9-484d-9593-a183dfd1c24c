<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div class="otd-board-group relative">
    <!-- 分组标题 -->
    <div class="board-group-title">
      <div class="group-title-text" v-if="group.isAdd || group.isEdit">
        <Input
          class="otd-input drap-title-input"
          :default-value="group.name"
          :placeholder="t('common.inputText') + t('common.groupTitle')"
          @blur="({ target }) => handleGroupSave(target, group)"
          @press-enter="($event.target as HTMLInputElement).blur()"
        />
      </div>
      <div class="group-title-text otd-table-action" v-else>
        <div class="group-title-text__header">
          <slot name="group-header" :group="group">
            <span class="otd-truncate" :title="group.name">{{ group.name }}</span>
            <span class="number">{{ group[listLabel]?.length }}</span>
          </slot>
        </div>
        <!-- 分组操作 -->
        <OtdMoreAction
          v-if="showAction"
          :index="index"
          :list="group[listLabel]"
          :data="group"
          :actions="action"
          :expand-number="1"
          action-type="icon"
        />
      </div>
    </div>
    <div class="board-group-body">
      <OtdScrollbar class="h-full" view-class="h-full">
        <div class="text-center my-10px" v-if="group.loading">
          <Spin size="small" class="mr-10px" />
          <span class="placeholder-text"> {{ t('common.loadingText') }} </span>
        </div>
        <!-- 任务拖拽 -->
        <OtdDraggable
          min-width="240px"
          :group="dragRule"
          :index="index"
          v-model:list="group[listLabel]"
          @end="dragOnEnd"
        >
          <template #item="{ element, index: elIndex }">
            <slot name="card" v-bind="{ element, index: elIndex }"></slot>
          </template>
          <template #footer v-if="showFooter">
            <!-- 创建任务 -->
            <div class="create-btn" @click="handleCreateTask(group)">
              <span class="placeholder-text placeholder-hover"> <i class="otdIconfont otd-icon-add-2"></i> </span>
            </div>
          </template>
        </OtdDraggable>
      </OtdScrollbar>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { Input } from 'ant-design-vue';
  import Spin from '/@/components/OtdLoading/src/Spin.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { OtdMoreAction } from '/@/components/OtdMoreAction';
  import { OtdDraggable } from '/@/components/OtdDraggable';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { ActionItem } from '/@/utils/types';
  import { computed, PropType } from 'vue';

  const { t } = useI18n();

  const props = defineProps({
    group: {
      type: Object,
      default: () => ({}),
    },
    dragRule: {
      type: Object,
      default: () => ({}),
    },
    action: {
      type: Array as PropType<ActionItem[]>,
      default: () => [],
    },
    index: {
      type: Number,
      default: undefined,
    },
    showAction: {
      type: Boolean,
      default: false,
    },
    hideTaskCreate: {
      type: Function as PropType<(data) => boolean>,
    },
    groupSave: {
      type: Function,
      default: () => {},
    },
    dragOnEnd: {
      type: Function as PropType<(...arg: any) => any>,
      default: () => {},
    },
    listLabel: {
      type: String,
      default: 'items',
    },
  });

  const showFooter = computed(() => {
    return !props.hideTaskCreate?.(props.group);
  });

  function handleGroupSave(target, group) {
    if (!target.value || target.value === group.name) {
      group.isEdit = false;
      target.value = group.name;
      return;
    }
    group.name = target.value;
    props.groupSave(group);
  }

  // 处理创建任务
  function handleCreateTask(data, action: 'unshift' | 'push' = 'push') {
    const { listLabel } = props;
    if (!data[listLabel]) {
      data[listLabel] = [];
    }
    data[listLabel][action]({ title: '', isAdd: true, responsibleGroup: data });
  }
</script>
<style lang="less" scoped>
  .otd-board-group {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    background-color: var(--otd-content-bg);
    border-radius: var(--otd-border-radius);
    .board-group-title {
      display: flex;
      align-items: center;
      border-radius: 8px;
      margin-bottom: 16px;
      .group-title-text {
        width: 100%;
        cursor: pointer;
        display: flex;
        font-size: 14px;
        justify-content: space-between;
        margin-bottom: 0;
        color: var(--otd-basic-text);
        height: 46px;
        align-items: center;
        line-height: 46px;
        background-color: var(--otd-basic-bg);
        padding: 4px;
        border-radius: 8px;
        &__header {
          width: 100%;
          display: flex;
          align-items: center;
          padding-right: 90px;
        }
        .otd-truncate {
          padding: 0 10px;
          font-size: 16px;
          color: var(--otd-basic-text);
        }
        .number {
          margin-left: 10px;
          color: var(--otd-basic-text);
        }
      }
    }
    .board-group-body {
      overflow: hidden;
      height: 100%;
    }
    .create-btn {
      .otdIconfont {
        font-size: 18px;
        background-color: var(--otd-gray4-color);
        color: var(--otd-body-text);
        border-radius: var(--otd-circle-radius);
        padding: 2px;
        line-height: 18px;
        display: flex;
        text-align: center;
      }
    }
  }
</style>
