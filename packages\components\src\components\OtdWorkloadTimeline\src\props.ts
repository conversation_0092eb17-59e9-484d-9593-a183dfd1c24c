import { PropType } from 'vue';
import { Recordable } from '/#/global';
import { mutable } from '/@/tool';

export const getProps = () => ({
  dateRange: {
    type: Array as PropType<string[]>,
    default: undefined,
  },
  request: {
    type: Function as PropType<(data?) => Promise<Recordable[]>>,
    default: undefined,
  },
  isCanEditer: {
    type: Function as PropType<(data?) => boolean>,
  },
  drapEnd: {
    type: Function as PropType<(data?) => Promise<Recordable>>,
    required: true,
  },
  resizeEnd: {
    type: Function as PropType<(data?) => Promise<Recordable>>,
    required: true,
  },
  moveEnd: {
    type: Function as PropType<(data?) => Promise<Recordable>>,
    required: true,
  },
});

const emit = ['click'] as const;
export const getEmits = () => mutable(emit);
