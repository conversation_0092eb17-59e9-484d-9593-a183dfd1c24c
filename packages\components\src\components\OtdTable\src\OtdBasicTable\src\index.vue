<template>
  <div ref="tableRef" class="otd-basic-table" v-intersection="dataInfo">
    <BasicTableHeader
      class="otd-basic-table__tr"
      :columns="columns"
      :data-source="dataSource"
      :row-key="getRowKey"
      @resizeColumn="handleResizeColumn"
    >
      <template #checkbox v-if="rowSelection">
        <Checkbox
          class="otd-basic-table__tr-checkbox"
          :indeterminate="indeterminate"
          :checked="checkedCurrentAll"
          :disabled="flattenData.length <= 0"
          @change="handleCheckAll"
        />
      </template>
    </BasicTableHeader>
    <div class="otd-basic-table__body" :style="{ height: `${bodyHeight}px` }">
      <template
        v-for="({ record, indent, isFooter }, index) in realData"
        :key="[getRowKey(record), isFooter ? 'add' : ''].filter(Boolean).join('-')"
      >
        <template v-if="isFooter">
          <div
            class="otd-basic-table__body-expend-footer otd-box-left"
            :style="{ top: `${(dataInfo.startRow + index) * rowHeight}px` }"
          >
            <div class="otd-basic-table__tr-indent" :style="{ '--indent': indent }"></div>
            <slot name="expend-footer" :record="record"></slot>
          </div>
        </template>
        <template v-else>
          <BasicTableRow
            v-if="defer()"
            :group-data="groupData"
            :record="record"
            :columns="columns"
            :indent="indent"
            :index="dataInfo.startRow + index"
            :selected-key-map="selectedKeyMap"
            :row-key="getRowKey"
            :row-height="rowHeight"
            :row-selection="rowSelection"
            :children-column-name="childrenColumnName"
            :expanded-keys="mergedExpandedKeys"
            @change="handleCheckRecord"
          />
        </template>
      </template>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import type { GetRowKey, TriggerEventHandler } from 'ant-design-vue/es/vc-table/interface';
  import type { Recordable } from '/#/global';
  import { computed, inject, onMounted, PropType, reactive, ref, shallowRef, toRef, unref, watch } from 'vue';
  import BasicTableHeader from './components/BasicTableHeader/index.vue';
  import BasicTableRow from './components/BasicTableRow/index.vue';
  import { useProvideBody } from '../useBasicTable';
  import useFlattenRecords from './hooks/useFlattenRecords';
  import { Checkbox } from 'ant-design-vue';
  import { useSelectionRecord } from './hooks/useSelectionRecord';
  import { useDefer } from '/@/hooks/event/useDefer';
  import { getEmits, getProps } from './props';
  import { Key } from 'ant-design-vue/es/_util/type';

  const props = defineProps({
    ...getProps(),
    rowKey: {
      type: [String, Function] as PropType<string | GetRowKey<Recordable>>,
    },
    groupIndex: {
      type: Number,
      default: 0,
    },
    groupData: {
      type: Object as PropType<Recordable>,
    },
  });

  const emit = defineEmits(getEmits());
  // 这个函数返回一个方法
  const defer = useDefer();

  const dataInfo = reactive({ startRow: 0, endRow: 0 });
  const bodyHeight = computed(() => unref(flattenData).length * props.rowHeight);

  const realData = computed(() => {
    return unref(flattenData).slice(dataInfo.startRow, dataInfo.endRow);
  });

  const innerExpandedKeys = shallowRef<Key[]>([]);
  const tableData = ref<Recordable[]>([]);
  const mergedExpandedKeys = computed(() => new Set(props.expandedRowKeys || innerExpandedKeys.value || []));

  const getRowKey = computed(() => {
    const rowKey = props.rowKey;
    if (typeof rowKey === 'function') {
      return rowKey;
    }
    return (record) => {
      const key = record && record[rowKey!];
      return key;
    };
  });

  const tableRef = ref();
  const getObserver = inject('observer') as (dom, index) => IntersectionObserver;
  const resetObserverInit = inject('resetObserverInit') as (index: number) => void;

  onMounted(() => {
    getObserver?.(unref(tableRef), props.groupIndex);
  });

  watch(
    () => props.dataSource,
    (value) => {
      if (!props.expandedRowKeys) {
        mergedExpandedKeys.value.clear();
      }
      tableData.value = value;
    },
    { immediate: true },
  );

  const rowSelection = ref();
  watch(
    () => props.rowSelection,
    () => {
      rowSelection.value = props.rowSelection ? { ...props.rowSelection } : props.rowSelection;
    },
    { deep: true, immediate: true },
  );

  const flattenData = useFlattenRecords(
    tableData,
    toRef(props, 'childrenColumnName'),
    mergedExpandedKeys,
    getRowKey,
    toRef(props, 'filter'),
  );

  const { selectedKeyMap, indeterminate, checkedCurrentAll, handleCheckAll, handleCheckRecord } = useSelectionRecord({
    pageData: toRef(props, 'dataSource'),
    childrenColumnName: toRef(props, 'childrenColumnName'),
    rowSelection: toRef(props, 'rowSelection'),
    getRowKey,
    expandedKeysRef: mergedExpandedKeys,
  });

  function handleResizeColumn(data) {
    emit('resizeColumn', data);
  }

  const onTriggerExpand: TriggerEventHandler<any> = (record) => {
    const rowKey = getRowKey.value(record, flattenData.value.indexOf(record));
    let expandKeys: Key[];
    const hasKey = mergedExpandedKeys.value.has(rowKey);
    if (hasKey) {
      mergedExpandedKeys.value.delete(rowKey);
      expandKeys = [...mergedExpandedKeys.value];
    } else {
      expandKeys = [...mergedExpandedKeys.value, rowKey];
    }
    innerExpandedKeys.value = expandKeys;
    resetObserverInit?.(props.groupIndex);
    emit('expand', { record, rowKey, expanded: !hasKey, expandKeys });
    emit('update:expandedRowKeys', expandKeys);
    emit('expandedRowsChange', expandKeys);
  };
  useProvideBody({
    onTriggerExpand,
  });
</script>
<style lang="less" scoped>
  @prefix: ~'otd-basic-table';
  .@{prefix} {
    position: relative;
    min-width: 100%;
    &__body {
      position: relative;
      &-expend-footer {
        --height: v-bind(rowHeight + 'px');
        height: var(--height);
        padding-left: 92px;
      }
    }
    &__tr,
    &__body-expend-footer {
      position: absolute;
      display: flex;
      top: 0;
      width: 100%;
      border-bottom: 1px solid var(--otd-border-table);
    }
    &__tr {
      :deep(&-indent) {
        width: calc(16px * var(--indent));
        height: 1px;
        float: left;
      }
      &:last-of-type {
        &__td,
        :deep(&__th) {
          border-color: transparent;
        }
      }
      :deep(&-checkbox) {
        position: absolute;
        left: 12px;
        z-index: 3;
        top: 50%;
        transform: translateY(-50%);
        .ant-checkbox {
          display: none;
          &.ant-checkbox-checked,
          &.ant-checkbox-indeterminate {
            display: inline-flex;
          }
        }
      }
      &:hover {
        :deep(.@{prefix}__tr-checkbox) {
          .ant-checkbox {
            display: inline-flex;
          }
        }
      }
    }
    &__td,
    :deep(&__th) {
      background-color: var(--otd-basic-bg);
    }
    :deep(&-line) {
      position: absolute;
      border-left: 1px solid var(--otd-primary-text);
      height: calc(100% - 50px);
      top: 50px;
      z-index: 2;
      transform: translateX(-100%);
    }
  }
</style>
