<template>
  <div
    class="otd-more-action"
    :class="{ 'otd-more-action__open': visible || openPopover }"
    v-if="(showAction.length || hideAction.length) > 0"
  >
    <div class="show-action" :class="hideClass">
      <template class="txt" v-for="item in showAction" :key="item.id">
        <Tooltip :open="item.expand ? false : undefined" :title="item.tip || getName(item, data)">
          <OtdActionItem
            :item="item"
            :data="data"
            :list="list"
            :index="index"
            not-menu
            v-bind="$props"
            @open-change="getPopoverShow"
          />
        </Tooltip>
      </template>
      <Dropdown
        v-if="hideAction.length > 0"
        v-model:open="visible"
        :overlayStyle="{ minWidth: '120px' }"
        :trigger="trigger"
        :destroy-popup-on-hide="destroyPopupOnHide"
        :disabled="disabled"
        overlayClassName="otd-dropdown"
      >
        <span class="hide-action-drop" :class="hideClass" @click.stop>
          <OtdActionItem
            :item="{ id: 1, icon: ' otd-icon-more' }"
            :stopped="false"
            :index="index"
            not-menu
            v-bind="$props"
          />
        </span>
        <template #overlay>
          <Menu>
            <div class="box-bottom-border" v-if="search">
              <Input
                v-model:value="searchVale"
                :bordered="false"
                :placeholder="t('common.searchText')"
                allowClear
                @change="handleSearch"
              >
                <template #prefix> <i class="otdIconfont otd-icon-sousuo" /> </template>
              </Input>
            </div>
            <OtdScrollbar style="max-height: 200px">
              <div v-if="hideActionList.length > 0" class="otd-dropdown-item-box">
                <template v-for="item in hideActionList" :key="item.id">
                  <OtdActionItem
                    :item="item"
                    :data="data"
                    :list="list"
                    :index="index"
                    actionType="icon"
                    @select="handleItemClick"
                  />
                </template>
              </div>
              <Empty :image="simpleImage" v-else />
            </OtdScrollbar>
          </Menu>
        </template>
      </Dropdown>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { Dropdown, Menu, Tooltip, Input, Empty } from 'ant-design-vue';
  import { ref, computed, nextTick, unref } from 'vue';
  import { isFunction } from '/@/utils/is';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { MoreActionItem } from './type';
  import OtdActionItem from './OtdActionItem.vue';
  import { getName } from './actionContent';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getProps } from './props';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';

  const props = defineProps(getProps());
  const emit = defineEmits(['query']);

  const { t } = useI18n();
  const { getGlobalProvide } = useGlobalConfig();
  const { judgePremission } = getGlobalProvide();
  const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
  const visible = ref(false);
  const searchVale = ref<string | undefined>();
  const hideActionList = ref<MoreActionItem[]>([]);

  const filterHideAction = computed(() => props.actions.filter((item) => judeShowAction(item, props.data)));
  // 显示操作内容
  const showAction = computed(() => unref(filterHideAction).slice(0, props.expandNumber));

  function handleActionList(): MoreActionItem[] | any {
    return unref(filterHideAction).slice(props.expandNumber);
  }

  // 隐藏操作内容
  const hideAction = computed(() => {
    hideActionList.value = handleActionList();
    return handleActionList();
  });

  // 判断操作是否显示
  function judeShowAction(action: MoreActionItem, data) {
    if (action.auth && judgePremission) {
      if (!judgePremission(action.auth)) return false;
    }
    if (isFunction(action.isHide)) {
      return !action.isHide(data, action);
    } else {
      return !action.isHide;
    }
  }

  const openPopover = ref(false);
  function getPopoverShow(visible) {
    openPopover.value = visible;
  }

  // 操作点击事件
  function handleItemClick() {
    visible.value = false;
  }

  // 搜索
  function handleSearch() {
    if (props.emitSearch) {
      emit('query', searchVale.value);
    } else {
      hideActionList.value = [];
      nextTick(() => {
        hideActionList.value = handleActionList().filter((item: MoreActionItem) =>
          getName(item, props.data)?.includes(searchVale.value as string),
        );
      });
    }
  }

  // 关闭展开内容
  function handleExpandClose() {
    visible.value = false;
    openPopover.value = false;
  }

  defineExpose({
    handleExpandClose,
  });
</script>
<style lang="less" scoped>
  .otd-more-action {
    --gap: v-bind(interval + 'px');
    display: flex;
    //flex-direction: row-reverse;
    .ant-dropdown-open {
      display: flex !important;
      .add-icon,
      .add-icon.is-btn {
        background-color: var(--otd-gray-hover);
      }
      & + .hide-action-drop {
        display: flex !important;
      }
    }
    .ant-dropdown-trigger {
      display: flex;
    }
    .show-action {
      display: flex;
      align-items: center;
      column-gap: var(--gap);
    }
  }
</style>
