<template>
  {{ condition }}
  <OtdSeniorFilter
    v-model:value="condition"
    :options="conditionFilters"
    placeholder="筛选"
    @change="handleFilterChange"
    :updateFilterItem="handleUpdateFilterItem"
    :removeFilterItem="handleRemoveFilterItem"
    :saveFilter="handleSaveFilter"
    :getFilterList="GetFilterList"
  />
</template>
<script lang="tsx" setup>
  import {
    OtdSeniorFilter,
    OtdUserSearch,
    FilterItem,
    OtdTag,
    FilterListItemType,
    FilerListType,
    AddFilterItem,
  } from '@otd/otd-ui';
  import { ref, shallowRef } from 'vue';
  import { RangePicker } from 'ant-design-vue';
  import { TagDto } from '@otd/otd-ui/src/components/OtdTag/src/types';
  enum FilterEnum {
    Status = 'status',
    Filter = 'filter',
    Sort = 'sort',
    Group = 'group',
    Tags = 'tagIds',
    ResponsibleUserId = 'responsibleUserId',
    CheckUserId = 'checkUserId',
    CreatorId = 'creatorId',
    FollowUserId = 'followUserId',
    PlanStartDateStart = 'planStartDateStart',
    PlanStartDateEnd = 'planStartDateEnd',
    PlanDoneDateStart = 'planDoneDateStart',
    PlanDoneDateEnd = 'planDoneDateEnd',
    CreationTimeStart = 'creationTimeStart',
    CreationTimeEnd = 'creationTimeEnd',
    ActualDoneDateStart = 'actualDoneDateStart',
    ActualDoneDateEnd = 'actualDoneDateEnd',
  }

  // 标签
  const tagOptions = ref<TagDto[] | undefined>([
    {
      color: '#A9A9A9',
      id: '1',
      tagAuth: 1,
      tagName: '新太阳',
      tagType: 0,
    },
    {
      color: '#2980B9',
      id: '2',
      tagAuth: 0,
      tagName: '夏普',
      tagType: 0,
    },
    {
      color: '#9B59B6',
      id: '3',
      tagAuth: 0,
      tagName: '松下',
      tagType: 0,
    },
    {
      color: '#A9A9A9',
      id: '4',
      tagAuth: 1,
      tagName: '日世',
      tagType: 0,
    },
    {
      color: '#2980B9',
      id: '5',
      tagAuth: 0,
      tagName: '看板中心',
      tagType: 0,
    },
    {
      color: '#9B59B6',
      id: '6',
      tagAuth: 0,
      tagName: '系统优化',
      tagType: 0,
    },
  ]);
  const condition = ref([
    {
      field: 'planStartDateStart',
      value: '2024-09-18T00:00:00',
      id: 8.1,
      condition: 'is',
      defaultValue: ['2024-09-18', '2024-09-21'],
    },
    {
      field: 'planStartDateEnd',
      value: '2024-09-21T23:59:59',
      id: 8.1,
      condition: 'is',
      defaultValue: ['2024-09-18', '2024-09-21'],
    },
    { field: 'status', value: 10, id: 1, condition: 'is', defaultValue: 10 },
  ]);
  const saveFilterObj = ref<FilerListType>({ items: [] } as FilerListType);
  function getTagsOptions() {
    return Promise.resolve();
  }

  // 状态
  enum TaskStatusEnum {
    UnStarted = 10,
    Stopped = 20,
    OnGoing = 30,
    Reject = 40,
    Done = 50,
    Close = 60,
    Cancelled = 70,
  }
  const { UnStarted, Stopped, OnGoing, Close, Reject, Done, Cancelled } = TaskStatusEnum;
  const options = [
    {
      label: '未开始',
      value: UnStarted,
    },
    {
      label: '待确认',
      value: Done,
    },
    {
      label: '已暂停',
      value: Stopped,
    },
    {
      label: '进行中',
      value: OnGoing,
    },
    {
      label: '已完成',
      value: Close,
    },
    {
      label: '已退回',
      value: Reject,
    },
    {
      label: '已取消',
      value: Cancelled,
    },
  ];

  // 筛选改变
  function handleFilterChange({ value, type }) {
    console.log(value, type);
  }

  // 修改保存的筛选项
  function handleUpdateFilterItem(data: { saveData: AddFilterItem; index: number }) {
    const { saveData, index } = data;
    console.log(saveData);
    return new Promise((resolve) => {
      saveFilterObj.value.items[index] = saveData;
      resolve('');
      console.log(saveFilterObj.value);
    });
  }

  // 删除保存的筛选项
  function handleRemoveFilterItem(data: { item: FilterListItemType; index: number }) {
    const { item, index } = data;
    console.log(item);
    return new Promise((resolve) => {
      saveFilterObj.value.items.splice(index, 1);
      resolve('');
    });
  }

  // 保存筛选项
  function handleSaveFilter(data: AddFilterItem) {
    console.log(data, '保存');
    return new Promise((resolve) => {
      saveFilterObj.value.items.push(data);
      console.log(saveFilterObj.value, 'saveFilterObj.value');
      resolve('');
    });
  }

  // 获取保存的筛选项
  function GetFilterList() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(saveFilterObj.value);
      }, 300);
    });
  }

  // 条件筛选
  const conditionFilters: FilterItem[] = [
    // 状态
    {
      id: 1,
      label: '状态',
      value: FilterEnum.Status,
      valueOptions: options,
    },
    // 标签
    {
      id: 2,
      label: '标签',
      value: FilterEnum.Tags,
      valueComponent: shallowRef(OtdTag),
      valueProps: {
        className: 'w-240px',
        placeholder: '标签',
        options: tagOptions,
        updateTags: () => getTagsOptions(),
        maxTagCount: 2,
        hideAction: true,
        notCreate: true,
        notHover: true,
        bordered: true,
      },
      valueApi: getTagsOptions,
      valueHandler: (data) => data?.map((item) => item.id),
    },
    // 负责人
    {
      id: 3,
      label: '负责人',
      value: FilterEnum.ResponsibleUserId,
      valueComponent: <OtdUserSearch />,
      valueProps: {
        class: 'w-240px',
        size: 'medium',
        placeholder: '负责人',
        icon: '',
        showText: true,
        isSimple: true,
        bordered: true,
      },
      valueHandler: (data) => data?.value,
    },
    // 确认人
    {
      id: 4,
      label: '确认人',
      value: FilterEnum.CheckUserId,
      valueComponent: <OtdUserSearch />,
      valueProps: {
        class: 'w-240px',
        placeholder: '确认人',
        size: 'medium',
        icon: '',
        showText: true,
        bordered: true,
        isSimple: true,
      },
      valueHandler: (data) => data?.value,
    },
    // 创建人
    {
      id: 5,
      label: '创建人',
      value: FilterEnum.CreatorId,
      valueComponent: <OtdUserSearch />,
      valueProps: {
        class: 'w-240px',
        placeholder: '创建人',
        size: 'medium',
        icon: '',
        showText: true,
        bordered: true,
        isSimple: true,
      },
      valueHandler: (data) => data?.value,
    },
    // 关注人
    {
      id: 6,
      label: '关注人',
      value: FilterEnum.FollowUserId,
      valueComponent: <OtdUserSearch />,
      valueProps: {
        class: 'w-240px',
        placeholder: '关注人',
        size: 'medium',
        icon: '',
        showText: true,
        bordered: true,
        isSimple: true,
      },
      valueHandler: (data) => data?.value,
    },
    // 开始时间
    {
      id: 8.1,
      label: '开始时间',
      value: FilterEnum.PlanStartDateStart,
      rangeKey: [FilterEnum.PlanStartDateStart, FilterEnum.PlanStartDateEnd],
      valueComponent: shallowRef(RangePicker),
      valueProps: {
        class: 'otd-picker w-240px',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        allowClear: false,
      },
      valueHandler: (data, index) => {
        return index === 0 ? data + 'T00:00:00' : data + 'T23:59:59';
      },
    },
    // 截止时间
    {
      id: 8.2,
      label: '截止时间',
      value: FilterEnum.PlanDoneDateStart,
      rangeKey: [FilterEnum.PlanDoneDateStart, FilterEnum.PlanDoneDateEnd],
      valueComponent: shallowRef(RangePicker),
      valueProps: {
        class: 'otd-picker w-240px',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        allowClear: false,
      },
      valueHandler: (data, index) => {
        return index === 0 ? data + 'T00:00:00' : data + 'T23:59:59';
      },
    },
    // 创建时间
    {
      id: 9,
      label: '创建时间',
      value: FilterEnum.CreationTimeStart,
      rangeKey: [FilterEnum.CreationTimeStart, FilterEnum.CreationTimeEnd],
      valueComponent: shallowRef(RangePicker),
      valueProps: {
        class: 'otd-picker w-240px',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        allowClear: false,
      },
      valueHandler: (data, index) => {
        return index === 0 ? data + 'T00:00:00' : data + 'T23:59:59';
      },
    },
    // 完成时间
    {
      id: 10,
      label: '完成时间',
      value: FilterEnum.ActualDoneDateStart,
      rangeKey: [FilterEnum.ActualDoneDateStart, FilterEnum.ActualDoneDateEnd],
      valueComponent: shallowRef(RangePicker),
      valueProps: {
        class: 'otd-picker w-240px',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        allowClear: false,
      },
      valueHandler: (data, index) => {
        return index === 0 ? data + 'T00:00:00' : data + 'T23:59:59';
      },
    },
  ];
</script>
<style lang="less">
  .w-240px {
    width: 240px !important;
  }
</style>
