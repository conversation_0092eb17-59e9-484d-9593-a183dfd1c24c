import { darkMode } from "/@/utils/theme";
import { LocaleEnum } from "/@/enums/appEnum";
import { ThemeEnum, RouterTransitionEnum } from "/@/enums/appEnum";

import { CacheTypeEnum } from "/@/enums/cacheEnum";

export type LocaleType = LocaleEnum;
export type LangType = "zh-cn" | "en-us"; // | 'ru' | 'ja' | 'ko';

export interface LocaleSetting {
  showPicker: boolean;
  // Current language
  locale: LocaleType;
  // default language
  fallback: LocaleType;
  // available Locales
  availableLocales: LocaleType[];
}

export type ProjectConfigType = {
  // 灰色模式
  grayMode: boolean;
  // 色弱模式
  colorWeak: boolean;
  // 黑夜模式
  darkMode: ThemeEnum;
};
