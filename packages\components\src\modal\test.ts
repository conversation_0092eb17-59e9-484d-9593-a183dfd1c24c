import { nextTick, getCurrentInstance } from 'vue';
import testModal from './testModal.vue';
import { ModalSignle } from './ModalSignle';

export function useCreateModal() {
  const instance = getCurrentInstance()!;
  // const floatActionStore = useFloatActionStoreWithOut();
  return (name: string, { component, props = {} }, callback: Function) => {
    const modal = new ModalSignle(name, {
      component,
      props,
      instance,
    });

    const vnode = modal.getModal();
    // floatActionStore.setFloatModal(vnode);
    nextTick(callback(vnode));
    return vnode;
  };
}

export function useModelSignle() {
  const CreateModal = useCreateModal();

  // 任务详情弹窗
  function TestModal(props = {}, data?) {
    return CreateModal('testModal', { component: testModal }, (modal) => {
      const { openModal } = modal;
      openModal(props, ...(data ?? []));
    });
  }

  return {
    TestModal,
  };
}
