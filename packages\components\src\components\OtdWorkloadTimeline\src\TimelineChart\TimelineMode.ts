import { ImageRect } from './ToolLibrary/shape/ImageRect';
import { GridInfoType, TimelineConfigType } from './type';
import { Square, Text, DrawLayer } from './ToolLibrary';
import dayjs, { Dayjs } from 'dayjs';
import { merge, throttle, debounce } from 'lodash-es';
import { calculateIntersectionDays, darkenColor, getCellCenterSeat, lightenColor } from './ToolLibrary/tool';
import { ToolTipMode } from './Plugins/ToolTipMode';
import { ScrollbarMode } from './Plugins/ScrollbarMode';
import { useI18n } from '/@/hooks/web/useI18n';
import { DrawTimelineType, GraphType, PublicType, SquareInfoType, ThemeType, TimelineType } from './ToolLibrary/type';
import defaultHeader from '/@/assets/images/default-header.svg';
import isToday from 'dayjs/plugin/isToday';
import { Recordable } from '/#/global';

dayjs.extend(isToday);

const UserAvatarRecord: Recordable<HTMLImageElement> = {};

export function useFixedColumn() {
  const { t } = useI18n();

  const FixedColumn = {
    // 负责人
    response: {
      title: t('common.subtask.resPerson'),
      width: 140,
      drawCell(tool, info) {
        const { record } = tool;
        const { x, y } = info;
        const Avatar = record.avatar ?? defaultHeader;
        const imageInfo = { x: x + 12, y: y + 15, width: 28, height: 28, radius: 28 };
        const imageAvatar = UserAvatarRecord[Avatar];
        if (!imageAvatar) {
          const image = new ImageRect(tool, { ...imageInfo, url: Avatar, isDraw: false });
          image.draw().then((img) => {
            UserAvatarRecord[Avatar] = img;
          });
        } else {
          new ImageRect(tool, { ...imageInfo, image: imageAvatar });
        }
        new Text(tool, { text: record.userName, x: x + 50, y: y + 30 });
      },
      drawHeader(tool, info) {
        const title = `${this.title}  ( ${tool.data.length} )`;
        const [textX, textY] = getCellCenterSeat(tool.ctx, title, info);
        new Text(tool, { text: title, x: textX, y: textY });
      },
    },
  };
  return {
    t,
    FixedColumn,
  };
}
const timelineTheme = {
  light: {
    mainColor: '#003de8',
    headerBackgroundColor: '#FCFCFC',
    cellBackgroundColor: '#FFF',
    cellSpaceBackgroundColor: '#F7F7F7',
    cellBorderColor: '#e0e0e0',
    timelineColor: '#003de8',
    dragBackgroundColor: '#DBDEE2',
    activeTimelineBorderColor: 'orange',
  },
  dark: {
    headerBackgroundColor: '#212130',
    cellBackgroundColor: '#212130',
    cellBorderColor: '#2f2f44',
    cellSpaceBackgroundColor: '#474747',
    dragBackgroundColor: '#6b6b6b',
  },
};

export class TimelineMode {
  private canvas!: HTMLCanvasElement;
  private parentNode!: HTMLElement;
  private rootContainer!: HTMLElement;
  private ctx!: CanvasRenderingContext2D;
  private resizeObserver!: ResizeObserver;
  private queue!: DrawLayer;
  private theme: ThemeType = 'light';
  private itemsKey!: string;
  private config: TimelineConfigType = {
    view: '',
    dateRange: ['', ''],
    timelineData: [],
    cellMinWidth: 64,
    cellMinHeight: 56,
    themeMode: 'light',
    itemsKey: 'items',
    theme: {
      ...timelineTheme[this.theme],
    },
    headerMinHeight: 40,
    timelineHeight: 30,
    timelineSpace: 6,
    container: () => document.body,
    isCanEditer: () => true,
  };
  private columnDraw = ['response'];
  private pluginMode: {
    tooltip: ToolTipMode;
    scrollbar: ScrollbarMode;
  } & Recordable = {} as any;

  // 内容宽度
  private content = { width: 0, height: 0 };
  private cellInfo = { width: 0, height: 0 };
  private currentTimeline: Recordable | undefined = undefined;

  private dateRange!: [Dayjs, Dayjs];
  // 网格信息
  private gridInfo: GridInfoType = {
    padding: [0, 0, 0, 0],
    content: {},
  };
  // 表头信息
  private headerInfo: GridInfoType = {
    padding: [0, 0, 0, 0],
    content: {},
  };
  // 固定列信息
  private fixedColumnInfo: GridInfoType = {
    padding: [0, 0, 0, 0],
    content: {},
  };
  // 时间线信息
  private timelineInfo: GridInfoType = {
    padding: [0, 0, 0, 0],
    content: {},
  };
  private timelineInfoMap = new Map<string, Square>();
  private days: number = 0;
  private t;
  private FixedColumn;

  constructor(config: TimelineConfigType) {
    this.config = merge(this.config, config);
    const { dateRange, container } = this.config;
    const { t, FixedColumn } = useFixedColumn();
    this.t = t;
    this.FixedColumn = FixedColumn;
    this.rootContainer = (container as Function)();
    this.theme = this.config.themeMode ?? 'light';
    this.canvas = document.getElementById(this.config.view) as HTMLCanvasElement;
    this.ctx = this.canvas.getContext('2d') as CanvasRenderingContext2D;
    this.dateRange = this.getDateRange(dateRange);
    this.itemsKey = this.config.itemsKey || 'items';
    this.days = this.dateRange[1].diff(dateRange[0], 'day') + 1;
    this.queue = new DrawLayer();
    this.init();
  }

  // 初始化
  private init() {
    this.parentNode = this.canvas.parentElement as HTMLElement;
    this.setCanvasStyle({
      width: this.parentNode.clientWidth,
      height: this.parentNode.clientHeight,
    });
    this.canvas.style.setProperty('user-select', 'none');
    this.resize();
    this.setTheme(this.theme, false);
    this.registerPlugin();
    this.registerListener();
    this.updateCanvas();
  }
  private setCanvasStyle({ width, height }) {
    const ratio = window.devicePixelRatio || 1;
    this.canvas.setAttribute('width', (width * ratio).toString());
    this.canvas.setAttribute('height', (height * ratio).toString());
    this.canvas.style.setProperty('width', width + 'px');
    this.canvas.style.setProperty('height', height + 'px');
    this.ctx.scale(ratio, ratio);
  }
  // 注册组件
  private registerPlugin() {
    // 提示模块
    this.pluginMode.tooltip = new ToolTipMode({
      rootContainer: this.rootContainer,
      mousemoveDom: this.canvas,
      tooltipId: `timeline-tooltip_${Date.now()}`,
    });
    // 滚动条模块
    this.pluginMode.scrollbar = new ScrollbarMode(
      {
        borderColor: this.config.theme?.cellBorderColor,
      },
      this,
    );
  }
  // 注册事件
  private registerListener() {
    this.canvas.addEventListener('contextmenu', (e) => {
      e.preventDefault();
    });
    document.body.addEventListener('mousedown', () => {
      if (this.currentTimeline) {
        this.selectTimeline();
      }
    });
  }
  // 获取单元格高度
  private getCellHeight(num) {
    const { cellInfo } = this;
    const { timelineHeight = 0, timelineSpace = 0 } = this.config;
    return Math.max(cellInfo.height, (timelineHeight + timelineSpace) * (num ?? 0) + timelineSpace);
  }
  // 计算表格内容
  private computedTableInfo() {
    const { days, content, cellInfo, gridInfo } = this;
    const { padding } = gridInfo;
    const { timelineData, headerMinHeight = 0 } = this.config;
    const height = timelineData.reduce((count, record) => {
      return count + this.getCellHeight(record?.[this.itemsKey]?.length) + 1;
    }, headerMinHeight + 1);
    content.width = days * (cellInfo.width + 1) + padding[3] - 1;
    content.height = height;
  }
  // 自适应内容
  private resize() {
    this.resizeObserver = new ResizeObserver((entries) => {
      // entries 是一个包含目标元素变化信息的数组
      for (const entry of entries) {
        // entry.contentRect 包含了元素的新尺寸信息
        this.setCanvasStyle({ width: entry.contentRect.width, height: entry.contentRect.height });
        this.updateCanvas();
      }
    });
    // 开始观察目标div元素
    this.resizeObserver.observe(this.canvas.parentElement as Element);
  }
  // 获取时间范围
  private getDateRange(dateRange): [Dayjs, Dayjs] {
    return [dayjs(dateRange[0] + ' 00:00:00'), dayjs(dateRange?.[1] + ' 23:59:59')];
  }

  // #region 网格
  // 绘制网格
  private drawGrid() {
    this.destroyGrid();
    this.destroyTimeline();
    const { days, gridInfo, cellInfo, canvas } = this;
    const { width } = cellInfo;
    const { padding } = gridInfo;
    const { timelineData } = this.config;
    const { scroll } = this.pluginMode.scrollbar;
    this.computedTableInfo();
    let lastHeight = padding[0] - scroll.y;
    timelineData.map((record, row) => {
      let y = lastHeight + 1;
      for (let column = 0; column < days; column++) {
        const x = column * (width + 1) + gridInfo.padding[3] - scroll.x;
        const cellHeight = this.getCellHeight(record?.[this.itemsKey]?.length);
        lastHeight = y + cellHeight;
        if (x + width > 0 && x < canvas.offsetWidth && y + cellHeight > 0 && y < canvas.offsetHeight) {
          this.drawGridCell({ row, column, x, y }, cellHeight, record);
        }
      }
      this.mapTimelineContent(record, (data, index) => {
        const days = calculateIntersectionDays(data.date, this.dateRange);
        if (days > 0) {
          this.drawTimeline({ data, index, row, ySeat: y, showDays: days });
        }
      });
    });
  }
  // 绘制网格单个单元格
  private drawGridCell({ row = 0, column = 0, x, y }: PublicType, cellHeight: number, record) {
    const { gridInfo, cellInfo, dateRange } = this;
    const {
      cellSpaceBackgroundColor,
      cellBackgroundColor,
      cellBorderColor = '',
      dragBackgroundColor = '',
      mainColor = '',
    } = this.config.theme ?? {};
    const date = dateRange?.[0].add(column, 'days');
    const dayOfWeek = date?.day();

    const isWeekend = dayOfWeek === 6 || dayOfWeek === 0;
    const isToday = date.isToday();
    const reloadRowTimelin = () => {
      const data = this.timelineInfo.content[row] ?? {};
      Object.values(data)?.map((square) => square.draw());
    };
    const square = new Square(this as any, {
      row,
      column,
      x,
      y,
      width: cellInfo.width,
      height: cellHeight,
      borderWidth: 1,
      isCell: true,
      borderColor: cellBorderColor,
      bgColor: isToday
        ? this.theme === 'light'
          ? lightenColor(mainColor, 0.9)
          : darkenColor(mainColor, 0.3)
        : isWeekend
        ? cellSpaceBackgroundColor
        : cellBackgroundColor,
      data: { record },
      isDrap: true,
      drapBgColor: dragBackgroundColor,
      onSetArea: (handler) => this.setClipArea(handler),
      onDrapEnter: reloadRowTimelin,
      onDrapLeave: reloadRowTimelin,
      onDrop: debounce((data) => {
        const { data: drapRecord, index } = data.record;
        const square = this.headerInfo.content[0][column];
        const { time } = square.getInfo.data ?? {};
        this.config?.onDrop?.({ rowData: record, record: drapRecord, index, time }).then((res) => {
          const oldTimeline = this.timelineInfoMap.get(drapRecord.id);
          const info = oldTimeline?.getInfo;
          if (info?.row === row) {
            record[this.itemsKey][info?.column ?? 0] = res;
          } else {
            if (info) {
              this.config.timelineData[info?.row ?? 0][this.itemsKey].splice(info?.column, 1);
            }
            if (res) {
              record[this.itemsKey] ? record[this.itemsKey].push(res) : (record[this.itemsKey] = [res]);
            }
          }
          this.updateCanvas();
        });
      }, 100),
    });

    if (!gridInfo.content[row]) {
      gridInfo.content[row] = {};
    }
    gridInfo.content[row][column] = square;
  }
  // 销毁网格
  private destroyGrid() {
    Object.values(this.gridInfo.content)?.map((item) => Object.values(item).map((square) => square.destroy()));
    this.gridInfo.content = {};
  }
  // #endregion

  // #region 表头
  // 获取表头高度
  private getHeaderHeight() {
    return this.config.headerMinHeight ?? 0;
  }
  // 绘制表头
  private drawHeader() {
    this.destroyHeader();
    const { days } = this;
    for (let column = 0; column < days; column++) {
      const square = this.drawHeaderCell(column);
      if (!this.headerInfo.content[0]) {
        this.headerInfo.content[0] = {};
      }
      this.headerInfo.content[0][column] = square;
    }
  }
  // 绘制表头单个单元格
  private drawHeaderCell(column) {
    const { gridInfo, cellInfo, dateRange } = this;
    const { scroll } = this.pluginMode.scrollbar;
    const { headerMinHeight = 0 } = this.config;
    const { headerBackgroundColor = '', cellBorderColor = '', mainColor = '' } = this.config.theme ?? {};
    const x = column * (cellInfo.width + 1) + gridInfo.padding[3] - scroll.x;
    const date = dateRange?.[0].add(column, 'day');
    const strDate = date?.format('MM-DD') ?? '';
    const week = date?.format('ddd').toString() ?? '';
    const width = cellInfo.width;
    const height = headerMinHeight;
    const isToday = date.isToday();
    const square = new Square(this as any, {
      row: 0,
      column,
      x,
      y: 0,
      width,
      height,
      borderWidth: 1,
      bgColor: headerBackgroundColor,
      borderColor: cellBorderColor,
      isCell: true,
      data: {
        time: date.format('YYYY-MM-DD'),
      },
      onMouseEnter: ({ x, y, width, height }) => {
        const { tooltip } = this.pluginMode;
        const content = date.format('YYYY-MM-DD (dddd)');
        const { left, top } = this.canvas.getBoundingClientRect();

        tooltip.setToolTip(content, (dom) => {
          return {
            x: x + left + width / 2 - dom.clientWidth / 2,
            y: y + top - height / 2 - dom.clientHeight + 8,
          };
        });
      },
      onMouseLeave: () => {
        const { tooltip } = this.pluginMode;
        tooltip.hidden();
      },
    });
    const text = isToday ? this.t('common.datePicker.Today') : strDate;
    const { width: contentWidth, actualBoundingBoxDescent: contentHeight } = this.ctx.measureText(text);

    const textInfo = getCellCenterSeat(
      this.ctx,
      { contentWidth: isToday ? contentWidth : Math.max(contentWidth, 40), contentHeight },
      { x, y: 0, width, height },
    );
    const [textX, textY, textWidth] = textInfo;
    const color = isToday ? mainColor : undefined;
    if (width > 80) {
      square.add([
        new Text(this as any, {
          text: week,
          x,
          y: 0,
          offset: [5, textY - 0],
          isDraw: false,
          color,
        }),
        new Text(this as any, {
          text,
          x,
          y: 0,
          offset: [width - textWidth - 5, textY - 0],
          isDraw: false,
          color,
        }),
      ]);
    } else {
      square.add([
        new Text(this as any, {
          text,
          x,
          y: 0,
          offset: [textX - x, textY - 0],
          isDraw: false,
          color,
        }),
      ]);
    }
    return square;
  }
  // 销毁表头
  private destroyHeader() {
    Object.values(this.headerInfo.content)?.map((item) => Object.values(item).map((square) => square.destroy()));
    this.headerInfo.content = {};
  }
  // #endregion

  // #region 固定列
  // 获取固定列宽度
  private getFixedWidth() {
    const { gridInfo, columnDraw } = this;
    const { padding } = gridInfo;
    padding[3] = 0;
    columnDraw.map((key) => {
      const column = this.FixedColumn[key];
      padding[3] += column.width + 1;
    });
    return padding[3];
  }
  // 绘制固定列
  private drawFixedColumn() {
    this.destroyFixedColumn();
    const { columnDraw } = this;
    const { padding } = this.gridInfo;
    const { timelineData, headerMinHeight = 0 } = this.config;
    padding[3] = 0;
    let height = 0;
    columnDraw.map((key, column) => {
      height += padding[0] + 1;
      const columnInfo = this.FixedColumn[key];
      timelineData.map((record, row) => {
        const { cellHeight } = this.drawFixedColumnCell(columnInfo, { row, column, record, height });
        height += cellHeight + 1;
      });
      // 固定列表头
      const header: SquareInfoType = {
        x: padding[3],
        y: 0,
        isCell: true,
        width: columnInfo.width,
        height: headerMinHeight,
        bgColor: this.config.theme?.headerBackgroundColor,
        borderColor: this.config.theme?.cellBorderColor,
        borderWidth: 1,
      };
      new Square(this as any, header);
      columnInfo.drawHeader.bind(columnInfo)({ ...this, data: timelineData }, header);
      padding[3] += columnInfo.width + 1;
      height = 0;
    });
  }
  // 绘制固定列单个单元格
  private drawFixedColumnCell(columnInfo: (typeof this.FixedColumn)['response'], { row, column, record, height }) {
    const { fixedColumnInfo } = this;
    const { padding } = this.gridInfo;
    const { scroll } = this.pluginMode.scrollbar;
    const { timelineData } = this.config;
    const cellHeight = this.getCellHeight(record?.[this.itemsKey]?.length);
    const cell: SquareInfoType = {
      x: padding[3],
      y: height - scroll.y,
      width: columnInfo.width,
      height: cellHeight,
      isCell: true,
      bgColor: this.config.theme?.cellBackgroundColor,
      borderColor: this.config.theme?.cellBorderColor,
      borderWidth: 1,
    };
    const square = new Square(this as any, cell);
    if (!fixedColumnInfo.content[row]) {
      fixedColumnInfo.content[row] = {};
    }
    fixedColumnInfo.content[row][column] = square;
    columnInfo.drawCell.bind(columnInfo)({ ...(this as any), record, data: timelineData }, cell);
    return { cellHeight };
  }
  // 销毁固定列
  private destroyFixedColumn() {
    Object.values(this.fixedColumnInfo.content)?.map((item) => Object.values(item).map((square) => square.destroy()));
    this.fixedColumnInfo.content = {};
  }
  // #endregion

  // #region 时间线
  // 循环时间线内容
  private mapTimelineContent(rowRecord, handler: (data: TimelineType, index) => void) {
    rowRecord?.[this.itemsKey]?.map((item, index) => {
      if (item.planStart || item.planDone) {
        const startTime = (item.planStart || item.planDone).split(' ');
        const endTime = (item.planDone || item.planStart).split(' ');
        const date: [Dayjs, Dayjs] = [
          dayjs(startTime[0] + ' ' + (startTime[1] ?? '00:00')),
          dayjs(endTime[0] + ' ' + (endTime[1] ?? '23:59')),
        ];
        const startDate = date[0].isAfter(this.dateRange?.[0], 'd') ? date[0] : this.dateRange?.[0];
        const endDate = date[1].isBefore(this.dateRange?.[1], 'd') ? date[1] : this.dateRange?.[1];
        const days = (endDate?.diff(startDate, 'd') ?? 0) + 1;
        handler({ record: item, date: [startDate, endDate], days, planDate: date }, index);
      }
    });
  }
  // 绘制单条时间线
  private drawTimeline({ data, index, row, ySeat, showDays }: DrawTimelineType) {
    const { record, date, days, planDate } = data;
    const { ctx, cellInfo, timelineInfo } = this;
    const { padding } = this.gridInfo;
    const { scroll } = this.pluginMode.scrollbar;
    const { timelineHeight = 0, timelineSpace = 0, isCanEditer } = this.config;
    const {
      timelineColor = '',
      timelineColorHandler,
      priorityHandler,
      activeTimelineBorderColor = '',
      dragBackgroundColor = '',
    } = this.config.theme ?? {};

    const width = showDays * (cellInfo.width + 1);
    const lineHeight = timelineHeight + timelineSpace;
    const columm = date[0].diff(this.dateRange[0], 'd');
    const x = columm * (cellInfo.width + 1) + padding[3] - scroll.x;
    const y = ySeat + index * lineHeight + timelineSpace;
    const startDate = planDate[0];
    const endDate = planDate[1];
    let radius: number[] = [0, 0];
    let resizeDirection: GraphType['resizeDirection'] = [];
    if (date[0].isSame(startDate, 'd')) {
      radius[0] = 5;
      resizeDirection.push('left');
    }
    if (date[1].isSame(endDate, 'd')) {
      radius[1] = 5;
      resizeDirection.push('right');
    }
    const info = { x, y, width, height: timelineHeight, row, column: index };
    let drapCell: Square;
    const isCan = isCanEditer?.({ record }) ?? true;
    const bgColor = timelineColorHandler ? timelineColorHandler?.({ record }) : timelineColor;
    const isCurrentTimeline = this.currentTimeline?.id === record.id;
    const square = new Square(this as any, {
      ...info,
      minWidth: cellInfo.width,
      borderWidth: isCurrentTimeline ? 2 : undefined,
      borderColor: isCurrentTimeline ? activeTimelineBorderColor : undefined,
      bgColor,
      hoverBgColor: darkenColor(bgColor, 0.9),
      cursor: isCan ? 'move' : 'pointer',
      resizeDirection,
      radius,
      isResise: isCan,
      data,
      onDelete: (info) => {
        this.config.timelineData[info.row][this.itemsKey].splice(info.column, 1);
        this.timelineInfoMap.delete(record.id);
      },
      onClick: this.config?.onClick,
      onResize: () => {
        Object.values(this.gridInfo.content).map((item) => Object.values(item).map((square) => square.draw()));
        Object.values(this.timelineInfo.content).map((item) => Object.values(item).map((square) => square.draw()));
      },
      onResizeEnd: (_, info, type) => {
        const seatMap = {
          left: { offsetX: info.x, offsetY: info.y },
          right: { offsetX: info.x + info.width, offsetY: info.y },
        };
        const seat = seatMap[type];
        let index = Object.values(this.gridInfo.content[row]).findIndex((square) =>
          square.isPointInRegion(seat as MouseEvent),
        );
        if (index >= 0) {
          let header = this.headerInfo.content[0][index].getInfo;
          const average = Math.round((header.x * 2 + header.width) / 2);
          const indexMao = {
            left: seat.offsetX < average ? index : index + 1,
            right: seat.offsetX > average ? index : index - 1,
          };
          index = indexMao[type];
          header = this.headerInfo.content[0][index].getInfo;
          this.config
            ?.onResizeEnd?.({
              record,
              rowData: this.config.timelineData[row],
              time: header?.data?.time,
              type,
            })
            .then(() => {
              this.updateCanvas();
            });
        }
      },
      isMove: isCan,
      onMove: (e) => {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        Object.values(this.gridInfo.content).map((item) =>
          Object.values(item).map((square) => {
            if (square.isPointInRegion(e)) {
              drapCell = square;
              square?.draw({ hoverColor: dragBackgroundColor });
            } else {
              square.draw();
            }
          }),
        );
        Object.values(this.timelineInfo.content).map((item) => Object.values(item).map((square) => square.draw()));
      },
      onMoveEnd: (_, info) => {
        if (drapCell) {
          const cell = drapCell.getInfo;
          const header = this.headerInfo.content[0][cell.column ?? 0].getInfo;
          const [endDate, time] = (record.planDone || record.planStart)?.split(' ');
          const realDays =
            !record.planStart || !record.planDone
              ? 0
              : dayjs(`${endDate} ${time ?? '23:59'}`).diff(dayjs(record.planStart), 'day');
          this.config
            ?.onMoveEnd?.({
              record,
              rowData: cell?.data?.record,
              time: [header?.data?.time, dayjs(header?.data?.time).add(realDays, 'days').format('YYYY-MM-DD')],
            })
            .then((res) => {
              if (res) {
                const source = this.config.timelineData[info.row][this.itemsKey];
                const isSameRow = info.row === cell.row;
                const rowData = this.config.timelineData[cell.row ?? 0];
                if (isSameRow) {
                  source[info.column] = res;
                } else {
                  source.splice(info.column, 1);
                  rowData[this.itemsKey] ? rowData[this.itemsKey].push(res) : (rowData[this.itemsKey] = [res]);
                }
              }
              this.updateCanvas();
            });
        }
      },
      onSetArea: (handler) => this.setClipArea(handler),
    });
    this.timelineInfoMap.set(record.id, square);
    if (isCurrentTimeline && !this.currentTimeline?.timelineInfo) {
      (this.currentTimeline as any).timelineInfo = square.getInfo;
      this.scrollToTimeline(square.getInfo);
    }
    const workDay = dayjs(endDate).diff(dayjs(startDate), 'day') + 1;
    const text = `${days > 1 ? `( ${workDay} ${this.t('common.repeatPicker.days')} ) ` : ''}${record.title}`;
    const [, textY] = getCellCenterSeat(ctx, { contentWidth: 1, contentHeight: 2 }, info);
    square.add([
      new Text(this as any, {
        text: 'e673',
        x,
        y,
        offset: x <= this.gridInfo.padding[3] ? [7, textY - y - 1] : [-16, textY - y - 1],
        font: 'otdIconfont',
        color: priorityHandler ? priorityHandler?.({ record }) : '#fff',
        borderColor: '#fff',
        isIcon: true,
        isDraw: false,
      }),
      new Text(this as any, {
        text,
        x,
        y,
        offset: x <= this.gridInfo.padding[3] ? [22, textY - y] : [7, textY - y],
        maxWidth: width - 20,
        fontSize: 12,
        color: '#fff',
        isDraw: false,
      }),
    ]);

    if (!timelineInfo.content[row]) {
      timelineInfo.content[row] = {};
    }
    timelineInfo.content[row][index] = square;
  }
  // 销毁时间线
  private destroyTimeline() {
    Object.values(this.timelineInfo.content)?.map((item) => Object.values(item).map((square) => square.destroy()));
    this.timelineInfo.content = {};
    this.timelineInfoMap.clear();
  }
  // #endregion

  // 绘制滚动条
  private drawScrollBar() {
    const { scrollbar } = this.pluginMode;
    const { drawVerticalScrollBar, drawHorizontalScrollBar } = scrollbar;
    drawVerticalScrollBar.bind(scrollbar)();
    drawHorizontalScrollBar.bind(scrollbar)();
  }
  // 设置裁剪区域(可绘制区域)
  private setClipArea(handler: () => void) {
    const { ctx, canvas } = this;
    const { padding } = this.gridInfo;
    ctx.save();
    ctx.beginPath();
    ctx.rect(
      padding[3],
      padding[0] + 1,
      canvas.width - (padding[1] + padding[3]),
      canvas.height - (padding[0] + 1 + padding[2]),
    );
    ctx.clip();
    handler();
    ctx.restore();
    return padding;
  }
  // 滚动到时间线位置
  private scrollToTimeline(info) {
    const { scrollbar } = this.pluginMode;
    scrollbar.setScroll({
      x: Math.max(info.x + scrollbar.scroll.x - this.canvas.offsetWidth / 2, 0),
      y: Math.max(info.y + scrollbar.scroll.y - this.canvas.offsetHeight / 2, 0),
    });
  }

  // 选中时间线
  selectTimeline(record?: Recordable) {
    if (!record) {
      const info = this.currentTimeline?.timelineInfo;
      this.currentTimeline = undefined;
      if (info) {
        this.timelineInfo.content[info.row][info.column].setInfo({
          borderWidth: 0,
        });
        this.updateRow(info.row);
      }
    } else {
      const data = this.timelineInfoMap.get(record.id);
      if (data) {
        const { activeTimelineBorderColor = '' } = this.config.theme ?? {};
        data.setInfo({
          borderWidth: 2,
          borderColor: activeTimelineBorderColor,
        });
        data.draw();
        this.scrollToTimeline(data.getInfo);
      }
      this.currentTimeline = Object.assign({}, record, { timelineInfo: data?.getInfo });
    }
  }

  // 更新行
  updateRow(row) {
    Object.values(this.gridInfo.content[row] ?? {})?.map((square) => square.draw());
    Object.values(this.timelineInfo.content[row] ?? {}).map((square) => square.draw());
  }

  // 设置主题
  setTheme(theme: ThemeType, update = true) {
    this.theme = theme;
    this.config.theme = {
      ...this.config.theme,
      ...timelineTheme['light'],
      ...timelineTheme[this.theme],
    };
    if (update) {
      this.updateCanvas();
      this.pluginMode.scrollbar.update(this.config.theme.cellBorderColor);
    }
  }

  // 更新
  update(dateRange?, timelineData?) {
    if (dateRange) {
      this.dateRange = this.getDateRange(dateRange);
      this.days = this.dateRange[1].diff(dateRange[0], 'day') + 1;
    }
    if (timelineData) {
      this.config.timelineData = timelineData;
    }
    this.updateCanvas();
  }
  // 更新画布
  updateCanvas = throttle((updateScrollBar = true) => {
    const { canvas } = this;
    const { padding } = this.gridInfo;
    const { cellMinHeight = 0, cellMinWidth = 0 } = this.config;
    // 单元格宽度
    this.cellInfo.width = Math.max(cellMinWidth, (canvas.width + 1 - padding[3]) / this.days - 1);
    // 单元格高度
    this.cellInfo.height = cellMinHeight;
    this.ctx.clearRect(0, 0, canvas.width, canvas.height);
    this.gridInfo.padding[0] = this.getHeaderHeight();
    this.gridInfo.padding[3] = this.getFixedWidth();
    this.queue.addQueue(() => {
      this.drawHeader();
      this.drawFixedColumn();
    }, 1);
    this.queue.addQueue(() => {
      this.drawGrid();
    });
    this.queue.draw();
    if (updateScrollBar) {
      this.drawScrollBar();
    }
  }, 50);

  get getConfig() {
    return this.config;
  }
  get getTimelineMap() {
    return this.timelineInfoMap;
  }
}
