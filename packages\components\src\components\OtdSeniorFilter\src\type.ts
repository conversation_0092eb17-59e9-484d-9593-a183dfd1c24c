import { DefaultOptionType } from 'ant-design-vue/es/select';
import { Component, ExtractPropTypes } from 'vue';
import { getEmits, getProps } from './props';
import { EmitType } from '/#/global';

export type FilterItem = {
  id: string | number;
  type?: 'input' | undefined;
  label: string;
  value: string;
  bordered?: boolean;
  rangeKey?: [string, string];
  mergeCondition?: boolean;
  conditions?: DefaultOptionType[];
  valueOptions?: DefaultOptionType[];
  valueComponent?: Component;
  valueProps?: any;
  valueApi?: Function;
  valueHandler?: (data, index?: number) => any;
};

export type FilterListItemType = {
  id?: string;
  creationTime?: string;
  creatorId?: string | undefined;
  creatorName?: string | undefined;
  lastModificationTime?: string | undefined;
  lastModifierId?: string | undefined;
  lastModifierName?: string | undefined;
  isDeleted?: boolean;
  deleterId?: string | undefined;
  deletionTime?: string | undefined;
  deleterName?: string | undefined;
  moduleEnum?: number | string;
  /** 名称 */
  name?: string | undefined;
  /** 查询条件 */
  text?: string | undefined;
};

export type FilerListType = {
  totalCount?: number;
  items: FilterListItemType[];
};

export type AddFilterItem = {
  moduleEnum?: number | string;
  /** 名称 */
  name?: string | undefined;
  /** 查询条件 */
  text?: string | undefined;
};

export type SeniorFilterPropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
export type SeniorFilterEmitType = EmitType<ReturnType<typeof getEmits>[number]>;
