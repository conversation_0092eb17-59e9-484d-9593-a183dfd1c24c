<template>
  <Popover
    v-model:open="visible"
    :arrow="false"
    :z-index="20"
    :align="PublicAlignConfig"
    overlay-class-name="otd-collaborative"
    placement="bottomLeft"
    trigger="click"
    @open-change="handleVisible"
  >
    <template #content>
      <Tabs v-model:activeKey="currentMenuVal">
        <TabPane v-for="tab in menu" :key="tab.value" :tab="tab.label" />
      </Tabs>
      <Input
        class="otd-collaborative-input"
        v-model:value="keyword"
        :placeholder="searchPlaceholder"
        @keypress.enter="handleCreate"
      >
        <template #prefix>
          <i class="otdIconfont otd-icon-sousuo"></i>
        </template>
        <template #suffix v-if="isCreate">
          <i @click="handleCreate" class="otdIconfont otd-icon-a-catentersize24 placeholder-hover"></i>
        </template>
      </Input>
      <Divider class="otd-collaborative-line" />
      <span class="otd-collaborative-createTip" v-show="isCreate"> {{ createTip }} </span>
      <OtdScrollbar style="height: 200px" v-loading="loading">
        <div
          class="otd-collaborative-content"
          v-for="(item, index) in dataList"
          :key="item.id"
          @click="handleSelect(item)"
        >
          <div class="placeholder-hover">
            <Input
              ref="inputRef"
              v-if="item.isEdit"
              :default-value="item.name"
              :placeholder="searchPlaceholder"
              @blur="(e) => handleRename(e, item)"
              @press-enter="($event.target as HTMLInputElement).blur()"
              @click.stop
            >
              <template #prefix> <i class="otdIconfont otd-icon-a-cateditsize24"></i> </template>
              <template #suffix> <i class="otdIconfont otd-icon-a-catentersize24 placeholder-hover"></i> </template>
            </Input>
            <div v-else class="otd-collaborative-content-name otd-truncate">{{ item.name }}</div>
            <i class="otdIconfont otd-icon-check" v-if="modelValue === item.id"></i>
            <OtdMoreAction
              v-if="isEditor(item)"
              class="more-hover"
              :actions="actions"
              :data="item"
              :list="dataList"
              :index="index"
              :expand-number="0"
              actionType="icon"
              hide-expand-name
              destroy-popup-on-hide
            />
          </div>
        </div>
      </OtdScrollbar>
    </template>
    <Tooltip :title="currentName" placement="top">
      <div class="otd-collaborative-select otd-box-left">
        <i class="otdIconfont otd-icon-yiriqingdan" />
        <div class="otd-collaborative-select-name otd-truncate">
          {{ [label, modelValue && currentName].filter(Boolean).join(': ') }}
        </div>
      </div>
    </Tooltip>
  </Popover>
</template>
<script lang="tsx" setup>
  import { Input, Popover, Divider, Tabs, TabPane, Tooltip } from 'ant-design-vue';
  import { TabItemType } from '/@/components/OtdTabFilter';
  import { OtdMoreAction } from '/@/components/OtdMoreAction';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { computed, ref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { MoreActionItem } from '../../OtdMoreAction';
  import { getProps, getEmits } from './props';
  import { nextTick } from 'vue';
  import { unref } from 'vue';
  import { PublicAlignConfig } from '/@/setting';

  const props = defineProps(getProps());
  const emit = defineEmits(getEmits());

  const { t } = useI18n();
  const { createConfirm } = useMessage();
  const menu: TabItemType[] = [
    { value: 'noArchived', label: t('common.teamworkChecklist') },
    { value: 'isArchived', label: t('common.archivedInventory') },
  ];
  const visible = ref(false);
  const currentMenuVal = ref('noArchived');
  const currentName = ref<string>();
  const keyword = ref(undefined);
  const modelValue = computed({
    get: () => {
      if (props.value && !currentName.value) {
        currentName.value = props.data.find((item) => item.id === props.value)?.name;
      }
      return props.value;
    },
    set: (value) => emit('update:value', value),
  });
  //是否点击的是归档清单类别
  const isArchived = computed(() => currentMenuVal.value === 'isArchived');
  const dataList = computed(() =>
    props.data.filter((item) => item.isArchived === unref(isArchived) && item.name.includes(unref(keyword) ?? '')),
  );
  const isCreate = computed(() => keyword.value && dataList.value.length <= 0);
  const inputRef = ref();
  // 操作
  const actions: MoreActionItem[] = [
    // 重命名
    {
      id: 1,
      icon: 'otd-icon-a-cateditsize24',
      name: t('common.tag.rename'),
      action: (data) => {
        data.isEdit = true;
        nextTick(() => {
          const input = unref(inputRef).pop();
          input.focus();
        });
      },
    },
    // 删除
    {
      id: 2,
      icon: 'otd-icon-a-catdeletesize24',
      name: t('common.delText'),
      color: 'red',
      action: (data, list, index) => {
        createConfirm({
          title: t('common.deleteListTip', { name: data?.name }),
          iconType: 'warning',
          onOk: () => {
            currentName.value = '';
            emit('delete', data, list, index);
          },
        });
      },
    },
    // 归档
    {
      id: 3,
      customRender: () => {
        return (
          <>
            <i class={['otdIconfont', isArchived.value ? 'otd-icon-quxiaoguidang' : 'otd-icon-guidang']}></i>
            <span class="add-icon__text">{isArchived.value ? t('common.cancelArchive') : t('common.archive')}</span>
          </>
        );
      },
      action: (data) => {
        createConfirm({
          title: isArchived.value
            ? t('common.cancelArchive', { name: data?.name })
            : t('common.archiveTip', { name: data?.name }),
          content: isArchived.value ? '' : t('common.archiveTipContent'),
          iconType: 'warning',
          onOk: () => {
            if (isArchived.value) {
              emit('cancelArchive', data);
            } else {
              emit('archive', data);
            }
          },
        });
      },
    },
  ];
  function handleSelect(data) {
    if (modelValue.value === data.id) {
      modelValue.value = '';
      currentName.value = '';
      emit('clear', data);
    } else {
      modelValue.value = data.id;
      if (!data.isEdit) {
        for (let item of props.data) {
          if (data.id === item.id) {
            currentName.value = item.name;
            emit('select', item);
            break;
          }
        }
      }
    }
  }
  function handleVisible(val) {
    //如果是打开弹窗
    val && (currentMenuVal.value = 'noArchived');
  }
  function handleRename({ target }, data) {
    data.isEdit = false;
    if (target.value === data.name) return;
    if (!target.value) return (target.value = data.name);
    data.name = target.value;
    currentName.value = data.name;
    emit('rename', data);
  }
  function handleCreate() {
    unref(isCreate) && emit('create', keyword.value);
  }
</script>
<style lang="less" scoped>
  .otd-collaborative {
    &-createTip {
      width: 100%;
      padding-left: 14px;
      color: var(--otd-help-color);
      height: 34px;
      line-height: 34px;
      display: inline-block;
    }
    &-input {
      height: 34px;
      line-height: 34px;
      margin: 4px 0;
      .otdIconfont {
        font-size: 18px;
        color: var(--otd-help-color);
      }
    }
    &-content {
      height: 46px;
      line-height: 46px;
      align-items: center;
      cursor: pointer;
      font-weight: 500;
      padding: 6px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid var(--otd-border-color);
      .placeholder-hover {
        width: 100%;
        height: 100%;
        align-items: center;
        column-gap: 1px;
        .otd-icon-check {
          padding: 5px;
        }
      }
      .ant-input-affix-wrapper {
        padding-left: 0px;
      }
      &-name {
        width: 100%;
      }
    }
    &-select {
      width: fit-content;
      justify-content: space-between;
      font-size: 14px;
      column-gap: 4px;
      cursor: pointer;
      &-name {
        max-width: 300px;
      }
    }
  }
</style>
