import { getProps, getEmits } from './props';
import { ExtractPropTypes } from 'vue';
import { EmitType } from '/#/global';
interface UserWithOrgProjOrg {
  id?: string;
  /** 组织名称 */
  displayName?: string | undefined;
}

export interface IUserWithOrgProj {
  /** id */
  id?: string;
  /** 用户所属组织 */
  orgUnits?: UserWithOrgProjOrg[] | undefined;
  /** 名字 */
  name?: string | undefined;
  /** email */
  email?: string | undefined;
  /** 手机号 */
  phoneNumber?: string | undefined;
  /** 用户状态 */
  isActive?: boolean;
  creationTime?: string | undefined;
}

export interface UserWithOrgProjType extends IUserWithOrgProj {
  isEmailError?: boolean;
  isPhoneError?: boolean;
  matchingSuccess?: boolean;
  newPhone?: string;
  newEmail?: string;
  notSync?: boolean;
}

export type SynchronizeAccountsPropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
export type SynchronizeAccountsEmitType = EmitType<ReturnType<typeof getEmits>[number]>;
