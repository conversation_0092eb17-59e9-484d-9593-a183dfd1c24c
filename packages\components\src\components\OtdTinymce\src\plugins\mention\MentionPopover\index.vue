<template>
  <Popover
    v-model:open="showDropdown"
    overlay-class-name="otd-popover in-tab"
    trigger="click"
    placement="topLeft"
    :arrow="false"
    destroy-tooltip-on-hide
  >
    <template #content>
      <div ref="popoverRef" class="otd-mention-popover" @click.stop>
        <div class="otd-mention-popover__header">
          <Tabs v-model:active-key="currentTab" class="otd-tab no-bottom" @change="handelChangeTab">
            <TabPane v-for="item in atMentionType" :key="item.value" :tab="item.label" />
          </Tabs>
          <!-- 搜索容器 start -->
          <div class="otd-popover__search">
            <Input
              v-model:value="searchValue"
              :bordered="false"
              :placeholder="searchPlaceholder || t('common.searchText')"
              allowClear
              @change="handleSearch()"
            >
              <template #prefix>
                <i class="otdIconfont otd-icon-sousuo" />
              </template>
            </Input>
          </div>
          <!-- 搜索容器 end -->
        </div>
        <div class="otd-mention-popover__body">
          <div class="otd-mention-popover__loading" v-if="loading">
            <OtdSpin inline :tip="t('common.loadingText')" />
          </div>
          <ul class="otd-mention-group" v-else>
            <h1 class="otd-selete-title">{{ t('common.userSearch.suggestion') }}</h1>
            <OtdScrollbar v-if="dropdownData.length > 0">
              <BasicSearchOptionItem
                v-for="(item, index) in dropdownData"
                :key="item.value"
                :item="item"
                :is-active="currentIndex === index"
                :select-item="() => handleSelect(item, index)"
              >
                <OtdUserOptionItem size="24px" :item="item" :type="currentTab" />
              </BasicSearchOptionItem>
            </OtdScrollbar>
            <Empty class="otd-mention-group__empty otd-box-center" v-else :image="Empty.PRESENTED_IMAGE_SIMPLE"></Empty>
          </ul>
        </div>
      </div>
    </template>
    <div class="otd-mention-seat" :style="{ top: `${popoverSeat[0]}px`, left: `${popoverSeat[1]}px` }">1</div>
  </Popover>
</template>
<script lang="ts" setup>
  import { Empty, Input, Popover, TabPane, Tabs } from 'ant-design-vue';
  import { OtdSpin } from '/@/components/OtdLoading';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMentionPopover } from './useMentionPopover';
  import { OtdUserOptionItem } from '/@/components/OtdUserSearch';
  import { computed } from 'vue';
  import { BasicSearchOptionItem } from '/@/components/BasicSearchPopover';

  const emit = defineEmits(['select', 'search']);
  const { t } = useI18n();

  const searchPlaceholder = computed(() => {
    return t('common.searchText');
  });

  const {
    showDropdown,
    loading,
    popoverSeat,
    currentIndex,
    currentTab,
    searchValue,
    dropdownData,
    atMentionType,
    handleSelect,
    handleSearch,
    handelChangeTab,
    setSearchValue,
    setActive,
    setDropdownSeat,
    getActiveData,
    setShow,
    setData,
    setLoading,
  } = useMentionPopover();

  defineExpose({
    setSearchValue,
    setActive,
    setDropdownSeat,
    getActiveData,
    setShow,
    setData,
    setLoading,
  });
</script>
<style lang="less" scoped>
  .otd-mention-popover {
    display: flex;
    flex-direction: column;
    width: 230px;
    height: 300px;
    &__header {
    }
    &__body {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      flex: 1;
      .otd-mention-group {
        padding: 4px 6px;
        margin-bottom: 0;
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        :deep(.ant-empty-normal) {
          margin-block: 20px;
        }
        :deep(.otd-user-search__item) {
          & + .otd-user-search__item {
            margin-top: 2px;
          }
        }
        &__empty {
          flex: 1;
          flex-direction: column;
        }
      }
    }
    &__loading {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 60px;
    }
  }
  .otd-mention-seat {
    position: fixed;
    left: 0;
    top: 0;
    opacity: 0;
  }
</style>
