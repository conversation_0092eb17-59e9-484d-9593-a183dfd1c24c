<template>
  <!-- <Dropdown :trigger="['click']" :overlayStyle="{ minWidth: '160px' }"> -->
  <Tooltip :title="tip">
    <div class="otd-ai-summary placeholder-hover" :class="[className]" @click="QuickQuestion[0].action?.()">
      <BasicIcon icon="otd-ai|svg" />
      <slot></slot>
    </div>
  </Tooltip>
  <!-- <template #overlay>
      <Menu>
        <MenuItem v-for="item in QuickQuestion" :key="item.id" @click="item.action(item)">
          {{ item.name }}
        </MenuItem>
      </Menu>
    </template>
  </Dropdown> -->

  <QuestionModal @register="registerQuestion" :title="questionTitle" :detail="detail" />
</template>
<script lang="ts" setup>
  import type { SummarizeTaskType } from './type';
  import type { Recordable } from '/#/global';
  import type { MoreActionItem } from '/@/components/OtdMoreAction/';
  import { PropType } from 'vue';
  import { Tooltip /* Dropdown, Menu, MenuItem */ } from 'ant-design-vue';
  import { QuestionModal } from './components/QuestionModal';
  import { useModal } from '/@/components/BasicModal';
  import { BasicIcon } from '/@/components/BasicIcon';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { setSummarizeTask } from './useAiQuestion';

  const props = defineProps({
    detail: Object as PropType<Recordable>,
    className: String,
    questionTitle: String,
    SummarizeTask: Function as PropType<SummarizeTaskType>,
    tip: String,
  });

  const { t } = useI18n();
  const [registerQuestion, { openModal }] = useModal();

  setSummarizeTask(props.SummarizeTask!);

  const QuickQuestion: MoreActionItem[] = [
    {
      id: 1,
      name: t('routes.taskMgmt.summary'),
      action: () => {
        openModal(true, { detail: props.detail });
      },
    },
  ];
</script>
<style lang="less" scoped>
  .otd-ai-summary {
    display: flex;
    align-items: center;
    column-gap: 2px;
  }
</style>
