<template>
  <div :style="containerStyle">
    <img
      ref="imgRef"
      :src="src"
      :alt="alt || 'image'"
      :style="[{ 'max-width': '100%' }, imgStyle]"
      :crossorigin="crossorigin"
    />
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, PropType, ref } from 'vue';
  import Cropper from 'cropperjs';
  import { omit } from 'lodash-es';

  const props = defineProps({
    containerStyle: Object,
    src: {
      type: String,
      default: '',
    },
    alt: String,
    imgStyle: Object,

    viewMode: Number,
    dragMode: String,
    initialAspectRatio: Number,
    aspectRatio: Number,
    data: Object,
    preview: [String, Array, Element, NodeList],
    responsive: {
      type: Boolean,
      default: true,
    },
    restore: {
      type: Boolean,
      default: true,
    },
    checkCrossOrigin: {
      type: Boolean,
      default: true,
    },
    checkOrientation: {
      type: Boolean,
      default: true,
    },
    crossorigin: {
      type: String as PropType<'' | 'anonymous' | 'use-credentials' | undefined>,
    },
    modal: {
      type: Boolean,
      default: true,
    },
    guides: {
      type: Boolean,
      default: true,
    },
    center: {
      type: Boolean,
      default: true,
    },
    highlight: {
      type: Boolean,
      default: true,
    },
    background: {
      type: Boolean,
      default: true,
    },
    autoCrop: {
      type: Boolean,
      default: true,
    },
    autoCropArea: Number,
    movable: {
      type: Boolean,
      default: true,
    },
    rotatable: {
      type: Boolean,
      default: true,
    },
    scalable: {
      type: Boolean,
      default: true,
    },
    zoomable: {
      type: Boolean,
      default: true,
    },
    zoomOnTouch: {
      type: Boolean,
      default: true,
    },
    zoomOnWheel: {
      type: Boolean,
      default: true,
    },
    wheelZoomRatio: Number,
    cropBoxMovable: {
      type: Boolean,
      default: true,
    },
    cropBoxResizable: {
      type: Boolean,
      default: true,
    },
    toggleDragModeOnDblclick: {
      type: Boolean,
      default: true,
    },

    minCanvasWidth: Number,
    minCanvasHeight: Number,
    minCropBoxWidth: Number,
    minCropBoxHeight: Number,
    minContainerWidth: Number,
    minContainerHeight: Number,

    ready: Function,
    cropstart: Function,
    cropmove: Function,
    cropend: Function,
    crop: Function,
    zoom: Function,
  });

  const imgRef = ref();
  let cropper: Cropper;

  function init() {
    const data = omit(props, ['containerStyle', 'src', 'alt', 'imgStyle']) as Cropper.Options;
    cropper = new Cropper(imgRef.value, data);
  }

  onMounted(() => {
    init();
  });

  function reset() {
    return cropper.reset();
  }
  function clear() {
    return cropper.clear();
  }
  function initCrop() {
    return cropper.crop();
  }
  function replace(url) {
    var onlyColorChanged = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;

    return cropper.replace(url, onlyColorChanged);
  }
  function enable() {
    return cropper.enable();
  }
  function disable() {
    return cropper.disable();
  }
  function destroy() {
    return cropper.destroy();
  }
  function move(offsetX, offsetY) {
    return cropper.move(offsetX, offsetY);
  }
  function moveTo(x) {
    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : x;

    return cropper.moveTo(x, y);
  }
  function relativeZoom(ratio) {
    return cropper.zoom(ratio);
  }
  function zoomTo(ratio, _originalEvent) {
    return cropper.zoomTo(ratio, _originalEvent);
  }
  function rotate(degree) {
    return cropper.rotate(degree);
  }
  function rotateTo(degree) {
    return cropper.rotateTo(degree);
  }
  function scaleX(_scaleX) {
    return cropper.scaleX(_scaleX);
  }
  function scaleY(_scaleY) {
    return cropper.scaleY(_scaleY);
  }
  function scale(scaleX) {
    var scaleY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : scaleX;

    return cropper.scale(scaleX, scaleY);
  }
  function getData() {
    var rounded = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;

    return cropper.getData(rounded);
  }
  function setData(data) {
    return cropper.setData(data);
  }
  function getContainerData() {
    return cropper.getContainerData();
  }
  function getImageData() {
    return cropper.getImageData();
  }
  function getCanvasData() {
    return cropper.getCanvasData();
  }
  function setCanvasData(data) {
    return cropper.setCanvasData(data);
  }
  function getCropBoxData() {
    return cropper.getCropBoxData();
  }
  function setCropBoxData(data) {
    return cropper.setCropBoxData(data);
  }
  function getCroppedCanvas() {
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};

    return cropper.getCroppedCanvas(options);
  }
  function setAspectRatio(aspectRatio) {
    return cropper.setAspectRatio(aspectRatio);
  }
  function setDragMode(mode) {
    return cropper.setDragMode(mode);
  }

  defineExpose({
    reset,
    clear,
    initCrop,
    replace,
    enable,
    disable,
    destroy,
    move,
    moveTo,
    relativeZoom,
    zoomTo,
    rotate,
    rotateTo,
    scaleX,
    scaleY,
    scale,
    getData,
    setData,
    getContainerData,
    getImageData,
    getCanvasData,
    setCanvasData,
    getCropBoxData,
    setCropBoxData,
    getCroppedCanvas,
    setAspectRatio,
    setDragMode,
  });
</script>
<style lang="less" scoped></style>
