<template>
  <div style="width: 720px; padding: 0 32px">
    <OtdLineUp
      v-model:value="data"
      clearable
      :before-change="handleBeforeChange"
      @change="handleChange"
      @click-item="handleClickItem"
    >
      <template #title>111</template>
    </OtdLineUp>
  </div>
</template>
<script lang="ts" setup>
  import { OtdLineUp } from '@otd/otd-ui';
  import { ref } from 'vue';

  const data = ref([]);
  function handleChange(...data) {
    console.log(data);
  }
  function handleClickItem(data) {
    console.log(data, 'handleClickItem');
  }

  function handleBeforeChange(selected, data) {
    console.log(selected, data);
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve(true);
        // resolve(false);
        // reject(true);
      }, 0);
    });
  }
</script>
<style lang="less" scoped></style>
