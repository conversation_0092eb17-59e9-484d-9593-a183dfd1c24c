<template>
  <OtdTable bordered :columns="columns" show-table-setting :data-source="data">
    <template #bodyCell="{ column }">
      <template v-if="column.key === 'operation'">
        <a>Publish</a>
      </template>
    </template>
    <template #expandedRowRender>
      <OtdTable
        bordered
        :columns="innerColumns"
        :data-source="innerData"
        :pagination="false"
        :row-selection="{}"
      ></OtdTable>
    </template>
  </OtdTable>
</template>
<script lang="tsx" setup>
  import { Input, OtdEditCellItem, OtdStatus, OtdTable } from '@otd/otd-ui';
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <OtdStatus v-model:value={record.platform} />
          </OtdEditCellItem>
        );
      },
    },
    {
      title: 'Platform',
      dataIndex: 'platform',
      key: 'platform',
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <Input v-model:value={record.name} />
          </OtdEditCellItem>
        );
      },
    },
    { title: 'Version', dataIndex: 'version', key: 'version' },
    { title: 'Upgraded', dataIndex: 'upgradeNum', key: 'upgradeNum' },
    { title: 'Creator', dataIndex: 'creator', key: 'creator' },
    { title: 'Date', dataIndex: 'createdAt', key: 'createdAt' },
    { title: 'Action', key: 'operation', defaultHidden: true },
  ];

  interface DataItem {
    key: number;
    name: string;
    platform: string;
    version: string;
    upgradeNum: number;
    creator: string;
    createdAt: string;
  }

  const data: DataItem[] = [];
  for (let i = 0; i < 3; ++i) {
    data.push({
      key: i,
      name: `Screem ${i + 1}`,
      platform: 'iOS',
      version: '10.3.4.5654',
      upgradeNum: 500,
      creator: 'Jack',
      createdAt: '2014-12-24 23:12:00',
    });
  }

  const innerColumns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <OtdStatus v-model:value={record.platform} />
          </OtdEditCellItem>
        );
      },
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <Input v-model:value={record.date} />
          </OtdEditCellItem>
        );
      },
    },
    { title: 'Status', key: 'state' },
    { title: 'Upgrade Status', dataIndex: 'upgradeNum', key: 'upgradeNum' },
    {
      title: 'Action',
      dataIndex: 'operation',
      key: 'operation',
    },
  ];

  interface innerDataItem {
    key: number | string;
    date: string;
    name: string;
    upgradeNum: string;
    children?: innerDataItem[];
  }

  const innerData: innerDataItem[] = [];
  for (let i = 0; i < 3; ++i) {
    innerData.push({
      key: i,
      date: '2014-12-24 23:12:00',
      name: `This is production name ${i + 1}`,
      upgradeNum: 'Upgraded: 56',
      children: new Array(3).fill(0).map((_, index) => {
        return {
          key: i + '-' + index,
          date: '2014-12-24 23:12:00',
          name: `This is production name ${i + 1}-${index}`,
          upgradeNum: 'Upgraded: 56',
        };
      }),
    });
  }
</script>
