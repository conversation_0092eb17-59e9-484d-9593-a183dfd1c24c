## 子任务

<demo src="../demo/Subtask/basic.vue" title="子任务"></demo>

## 属性

| 参数                  | 说明                       | 类型     | 可选值 | 默认值 | 版本 |
| --------------------- | -------------------------- | -------- | ------ | ------ | ---- |
| value                 | 子任务数组                 | array    | --     | --     | 1.0  |
| sync                  | 点击同步的回调             | function | --     | --     | 1.0  |
| change                | 行内数据变化时的回调       | function | --     | --     | 1.0  |
| remove                | 删除子任务时的变化时的回调 | function | --     | --     | 1.0  |
| toDetail              | 点击查看详情的回调         | function | --     | --     | 1.0  |
| addBatchSubtask       | AI 生成子任务时的添加回调  | function | --     | --     | 1.0  |
| handleTemplateSubtask | 点击按模板生成子任务的回调 | function | --     | --     | 1.0  |
| detail                | 当前父任务的值             | object   | --     | --     | 1.0  |
| getUserList           | 获取人员的方法             | function | --     | --     | 1.0  |
| generateSubTaskTitles | AI 生成子任务的方法        | function | --     | --     | 1.0  |
| isShowClosed          | 是否显示已关闭的任务       | boolean  | --     | --     | 1.0  |
