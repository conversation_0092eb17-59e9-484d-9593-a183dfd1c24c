<template>
  <Switch
    class="otd-dark-mode-toggle"
    :checkedValue="ThemeEnum.LIGHT"
    :unCheckedValue="ThemeEnum.DARK"
    :checked="getDarkMode"
    @click="toggleDarkMode"
  >
    <template #checkedChildren>
      <BasicSvgIcon name="sun" />
    </template>
    <template #unCheckedChildren>
      <BasicSvgIcon name="moon" />
    </template>
  </Switch>
</template>
<script lang="ts" setup>
import { Switch } from "ant-design-vue";
import { HandlerEnum } from "../../OtdProjectConfig/src/enum";
import { baseHandler } from "../../OtdProjectConfig/src/handler";
import { BasicSvgIcon } from "/@/components/BasicIcon";
import { ThemeEnum } from "/@/enums/appEnum";
import { useRootSetting } from "/@/storage/projectConfigStorage";

const { getDarkMode } = useRootSetting();

function toggleDarkMode(value) {
  baseHandler(HandlerEnum.DARK_MODE, value);
}
</script>
<style lang="less" scoped>
.otd-dark-mode-toggle {
  border: 1px solid var(--otd-border-color);
  height: 32px;
  line-height: 32px;
  &.ant-switch {
    background-color: var(--otd-black-bg) !important;
    :deep(.ant-switch-handle) {
      top: 4px;
      width: 22px;
      height: 22px;
      &::before {
        border-radius: var(--otd-circle-radius);
      }
    }
    :deep(.ant-switch-inner) {
      padding-inline-start: 42px;
      padding-inline-end: 10px;
      .ant-switch-inner-checked {
        margin-inline-start: calc(-100% + 22px - 70px);
        margin-inline-end: calc(100% - 22px + 70px);
      }
      .ant-switch-inner-unchecked {
        margin-top: -32px;
        margin-inline-start: 0;
        margin-inline-end: 0;
      }
    }
    &.ant-switch-checked {
      background-color: var(--otd-basic-bg) !important;
      :deep(.ant-switch-handle) {
        inset-inline-start: calc(100% - 28px);
      }
      :deep(.ant-switch-inner) {
        padding-inline-start: 10px;
        padding-inline-end: 42px;
        .ant-switch-inner-checked {
          margin-inline-start: 0;
          margin-inline-end: 0;
        }
        .ant-switch-inner-unchecked {
          margin-inline-start: calc(100% - 22px + 70px);
          margin-inline-end: calc(-100% + 22px - 70px);
        }
      }
    }
  }
}
</style>
