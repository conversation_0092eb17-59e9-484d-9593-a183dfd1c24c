{"name": "otd-ui", "version": "1.0.9", "type": "module", "main": "./src/index.ts", "scripts": {"start": "vite --host", "build": "vite build", "type:check": "vue-tsc", "preview": "vite preview", "debug": "vite --debug hmr"}, "files": ["dist"], "dependencies": {"vue": "^3.4.23"}, "devDependencies": {"@vitejs/plugin-vue": "^3.0.3", "typescript": "^5.0.2", "vite": "^3.2.5", "vue-tsc": "^1.8.5"}, "peerDependencies": {"@types/lodash-es": "^4.17.6", "vue": "^3.3.4"}}