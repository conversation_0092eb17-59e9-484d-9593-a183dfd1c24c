import { NullableDateType } from 'ant-design-vue/es/vc-picker/interface';
import { Dayjs } from 'dayjs';
import { Frequency, WeekdayStr } from 'rrule';
import { getEmits, getProps } from './props';
import { ExtractPropTypes } from 'vue';
import { EmitType } from '/#/global';

export type DateValueType = NullableDateType<Dayjs>;
export type DateStringType = NullableDateType<string>;

export type ByType = 'day' | 'week';
export type FrequencyFormType = {
  interval: number;
  freq: Frequency;
  byType?: ByType;
  typeInterval?: number;
  byweekday?: WeekdayStr;
};

export type DatePickerPropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
export type DatePickerEmitsType = EmitType<ReturnType<typeof getEmits>[number]>;
