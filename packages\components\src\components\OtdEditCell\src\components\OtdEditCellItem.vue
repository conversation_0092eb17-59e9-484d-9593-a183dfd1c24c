<template>
  <component
    :is="formItem ? FormItem : 'div'"
    class="otd-edit-cell"
    :class="{ 'left-label': leftLabel, 'hide-error-tip': hideErrorTip }"
    v-bind="formItem ? getProps : {}"
  >
    <div class="otd-edit-cell__skeleton" v-if="loading"><SkeletonButton block active></SkeletonButton></div>
    <div class="otd-edit-cell__content" :class="getClass" v-else>
      <slot />
    </div>
  </component>
</template>
<script lang="ts" setup>
  import { FormItem, SkeletonButton } from 'ant-design-vue';
  import { computed } from 'vue';
  import { omit } from 'lodash-es';
  import { getFormItemProps } from '../props';
  import { getFormConfig } from '../useProvideConfig';

  const props = defineProps(getFormItemProps());

  const getProps = computed(() => {
    return omit(props, ['offset']);
  });
  const getClass = computed(() => [
    `is-${props.align}`,
    props.isFull && 'is-full',
    (props.bordered ?? parBorder) && 'is-border',
  ]);

  const { loading, bordered: parBorder } = getFormConfig();
</script>
<style lang="less" scoped>
  .otd-edit-cell {
    --y: v-bind(offset[0] + 'px');
    --x: v-bind(offset[1] + 'px');
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: var(--y) var(--x);
    min-height: calc(22px + calc(var(--y) * -2));
    box-sizing: content-box;
    white-space: nowrap;
    &.left-label {
      :deep(.ant-form-item-label) {
        position: absolute;
        top: 2px;
        left: 0;
        z-index: 1;
      }
    }
    &.ant-form-item-has-error {
      :deep(.ant-input) {
        &::placeholder {
          color: var(--otd-error-color);
        }
      }
      &.hide-error-tip {
        :deep(.ant-form-item-explain-error) {
          display: none;
        }
      }
    }
    &__skeleton {
      padding: 4px;
      flex: 1 0 0;
      width: 100%;
      z-index: 1;
      background-color: var(--otd-basic-bg);
      :deep(.ant-skeleton) {
        height: 100%;
        .ant-skeleton-button {
          height: 100%;
        }
      }
    }
    &__content {
      cursor: pointer;
      flex: 1 0 0;
      width: 100%;
      :deep(> div) {
        height: 100%;
      }
      :deep(.otd-data-bubble) {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        border: 1px solid transparent;
        border-radius: var(--otd-small-radius);
        padding: 0 calc(var(--x) * -1);
        background-color: transparent;
        &:hover {
          background-color: var(--otd-gray3-hover);
          // border-color: var(--otd-border-gray);
          .placeholder-hover {
            background-color: var(--otd-gray3-hover);
          }
        }
        &.ant-popover-open,
        &.ant-dropdown-open {
          background-color: var(--otd-gray-hover);
          border-color: var(--otd-primary-color);
          .placeholder-hover {
            background-color: var(--otd-gray-hover);
          }
        }
      }
      &.is-border {
        :deep(.otd-data-bubble) {
          border-color: var(--otd-border-gray);
        }
      }
      &.is-full {
        :deep(.otd-data-bubble) {
          // padding: calc(var(--y) * -1) calc(var(--x) * -1);
        }
      }
      &.is-left {
        :deep(.otd-data-bubble) {
          justify-content: flex-start;
        }
      }
      &.is-right {
        :deep(.otd-data-bubble) {
          justify-content: flex-end;
        }
      }
      &.is-center {
        :deep(.otd-data-bubble) {
          justify-content: center;
        }
      }

      &:not(.is-border) {
        // 下拉框
        :deep(.ant-select) {
          display: flex;
          width: 100%;
          height: 100%;
          background-color: transparent;
          border: 1px solid transparent;
          box-shadow: none !important;
          border-radius: var(--otd-default-radius);
          &:hover {
            background-color: var(--otd-gray3-hover);
          }
          .ant-select-selector {
            border: none;
            height: auto;
            display: flex;
            align-items: center;
            background-color: transparent;
            box-shadow: none !important;
            .ant-select-selection-search {
              inset-inline-start: 0;
              inset-inline-end: 0;
              input {
                padding: 11px;
                height: 100%;
                border-radius: 0;
              }
            }
          }
          &:hover .ant-select-selector {
            border: none;
            box-shadow: none;
          }
        }
        :deep(.ant-select.ant-select-multiple) {
          .ant-select-selection-search {
            width: 100% !important;
            margin-inline-start: 4px;
            input {
              padding: 0;
            }
          }
        }

        :deep(.ant-select-open) {
          border: 1px solid var(--otd-primary-color);
          background-color: var(--otd-gray-hover);
          & input:focus {
            outline: none !important;
          }
          & [type='search'] {
            outline-offset: 0px;
          }
        }
        // 自动完成无下拉框状态
        :deep(.ant-select-auto-complete) {
          input:focus {
            outline: 1px solid var(--otd-primary-color);
            border-radius: var(--otd-default-radius) !important;
          }
          [type='search'] {
            outline-offset: 0px;
          }
        }

        :deep(.ant-input-affix-wrapper) {
          position: unset;
          padding: 0;
          border-width: 0;
          .ant-input {
            border: 1px solid transparent;
          }
          &.ant-input-affix-wrapper-focused {
            .ant-input-suffix {
              display: inline-block;
            }
          }
          .ant-input-suffix {
            display: none;
            position: absolute;
            right: 6px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        :deep(.ant-input),
        :deep(.ant-input-number) {
          padding: 5px 8px;
          border-color: transparent;
          box-shadow: unset;
          width: 100%;
          height: 100%;
        }

        //  文本框
        :deep(.ant-input),
        :deep(.ant-input-affix-wrapper) {
          width: 100%;
          height: 100%;
          background-color: transparent;
          box-shadow: unset;
          border-radius: var(--otd-default-radius);
          &.ant-input-disabled {
            background-color: inherit;
            color: inherit;
            cursor: inherit;
          }
          &:focus {
            border-color: var(--otd-primary-color);
            background-color: var(--otd-gray-hover);
          }
          &.ant-input-affix-wrapper-focused,
          &:focus {
            & + .otd-more-action {
              display: none !important;
            }
          }
          &:hover {
            background-color: var(--otd-gray3-hover);
          }
        }

        :deep(.ant-input-number) {
          &.ant-input-number-disabled {
            background-color: inherit;
            color: inherit;
            cursor: inherit;
          }
          .ant-input-number-handler-wrap {
            .ant-input-number-handler {
              border-width: 0;
            }
          }
          .ant-input-number-input-wrap {
            height: 100%;
            .ant-input-number-input {
              height: 100%;
              padding: 0;
            }
          }
          &-focused {
            border-color: var(--otd-primary-color);
            background-color: var(--otd-gray-hover);
            .ant-input-number-handler {
              background-color: var(--otd-gray-hover);
            }
          }
          &:hover {
            background-color: var(--otd-gray3-hover);
            .ant-input-number-handler {
              background-color: var(--otd-gray3-hover);
            }
          }
        }

        //  日期
        :deep(.ant-picker) {
          display: flex;
          width: 100%;
          height: 100%;
          background-color: transparent;
          box-shadow: unset;
          border-color: transparent;
          padding: 0;
          border-radius: var(--otd-default-radius);
          &:hover {
            background-color: var(--otd-gray3-hover);
          }
          &.ant-picker-focused {
            border-color: var(--otd-primary-color);
            background-color: var(--otd-gray-hover);
          }
          .ant-picker-clear {
            right: 6px;
          }
          .ant-picker-input {
            height: 100%;
            input {
              padding: 0 11px;
              height: 100%;
              border-radius: 0;
              &:focus {
                box-shadow: 0 0 0 1px rgba(5, 115, 255, 0.1);
              }
            }
          }

          .ant-picker-suffix {
            display: none !important;
          }
        }
      }
    }
    :deep(.ant-row) {
      flex: 1;
      display: flex;
      width: 100%;
      .ant-form-item-control-input,
      .ant-form-item-control-input-content {
        height: 100%;
      }
    }
  }

  .otd-edit-cell_active {
    outline: 1px solid var(--otd-primary-color);
    outline-offset: -1px;
  }
</style>
