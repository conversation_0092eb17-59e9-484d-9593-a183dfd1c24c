<template>
  <Menu
    :selectedKeys="selectedKeys"
    :defaultSelectedKeys="defaultSelectedKeys"
    v-bind="$attrs"
    @click="handleClickItem"
  >
    <template v-for="item in showMenus" :key="item.path">
      <BasicSubMenuItem :item="item" :show-title="showTitle" />
    </template>
  </Menu>
</template>
<script lang="ts" setup>
  import { Menu } from 'ant-design-vue';
  import BasicSubMenuItem from './components/BasicSubMenuItem.vue';
  import { computed, PropType } from 'vue';
  import { MenuType } from './types';
  import { MenuInfo } from 'ant-design-vue/es/menu/src/interface';

  const props = defineProps({
    items: {
      type: Array as PropType<MenuType[]>,
      default: () => [],
    },
    selectedKeys: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    showTitle: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['update:selectedKeys', 'menu-click']);

  const showMenus = computed(() => {
    return props.items?.filter((item) => !item.meta?.hideMenu) ?? [];
  });

  const selectedKeys = computed({
    get() {
      return props.selectedKeys;
    },
    set(val) {
      emit('update:selectedKeys', val);
    },
  });
  const defaultSelectedKeys = [...(props.selectedKeys ?? [])];

  function handleClickItem(data: MenuInfo) {
    selectedKeys.value = data.keyPath as string[];
    emit('menu-click', data);
  }
</script>
<style lang="less" scoped>
  .ant-menu-light {
    background-color: transparent;
  }
</style>
