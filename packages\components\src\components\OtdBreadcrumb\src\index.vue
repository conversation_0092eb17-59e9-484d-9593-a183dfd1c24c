<template>
  <div class="otd-breadcrumb" v-if="data.length > 0 || (showAppTitle && appTitle)">
    <Breadcrumb>
      <BreadcrumbItem v-if="showAppTitle">{{ appTitle }}</BreadcrumbItem>
      <BreadcrumbItem v-for="route in data" :key="route.value">{{ route.label }}</BreadcrumbItem>
    </Breadcrumb>
    <h1 v-if="showTitle && data.length > 0">{{ lastData.label }}</h1>
  </div>
</template>
<script lang="ts" setup>
  import { Breadcrumb, BreadcrumbItem } from 'ant-design-vue';
  import { computed, PropType } from 'vue';
  import { BreadcrumbItemType } from './type';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
  const props = defineProps({
    data: {
      type: Array as PropType<BreadcrumbItemType[]>,
      default: () => [],
    },
    showTitle: {
      type: Boolean,
      default: false,
    },
    showAppTitle: {
      type: Boolean,
      default: false,
    },
  });

  const lastData = computed(() => {
    const { data } = props;
    return data[data.length - 1];
  });

  const { getGlobalProvide } = useGlobalConfig();
  const { appTitle } = getGlobalProvide();
</script>
<style lang="less" scoped>
  .otd-breadcrumb {
    padding-top: 10px;
    .ant-breadcrumb {
      font-size: 12px;
      line-height: 16px;
    }
    > h1 {
      margin-top: 10px;
      font-size: 24px;
      line-height: 28px;
      margin-bottom: 0;
    }
  }
</style>
