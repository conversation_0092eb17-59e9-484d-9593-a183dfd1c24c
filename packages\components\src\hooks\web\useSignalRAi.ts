import * as signalR from '@microsoft/signalr';
import { OtdSignalConfig, SignalRQueryType } from './useSignalR';

let connection: signalR.HubConnection | null;
export function useSignalRAi({ socketUrl, token, handler, logout }: SignalRQueryType) {
  /**
   * 开始连接SignalR
   */
  async function startConnect() {
    if (!connection) {
      try {
        connectionsignalR();
        return connection!.start();
      } catch (err) {
        console.log(err);
        setTimeout(() => startConnect(), 5000);
      }
    } else {
      return Promise.resolve();
    }
  }

  /**
   * 关闭SignalR连接
   */
  function closeConnect() {
    return connection!?.stop().then(() => {
      return (connection = null);
    });
  }

  async function connectionsignalR() {
    if (!socketUrl || !token) return;
    connection = OtdSignalConfig(socketUrl, token, logout);
    // 接收AI文本消息
    connection.on('ReceiveChatResponseTextAsync', ReceiveAiMessageHandlerAsync);
  }

  /**
   * 接收AI文本消息
   * @param message 消息体
   */
  function ReceiveAiMessageHandlerAsync(message: any) {
    if (!handler) return;
    handler('', false);
    if (message !== 'STREAM DATA: [DONE]') {
      handler(message, true);
    } else {
      handler('', false);
    }
  }

  return { startConnect, closeConnect };
}
