<template>
  <OtdExpandTreeTable
    bordered
    :columns="columns"
    show-table-setting
    :data-source="data"
    :is-expand="(record) => record.key === 1"
    :scroll="{ y: 300 }"
  >
    <template #expandedRowRender>
      <OtdTable ref="tableRef" bordered :columns="innerColumns" :data-source="innerData" :pagination="false">
      </OtdTable>
    </template>
  </OtdExpandTreeTable>
</template>
<script lang="tsx" setup>
  import { OtdTable, OtdExpandTreeTable, OtdEditCellItem, Input, OtdStatus } from '@otd/otd-ui';
  import { ref, unref, watch } from 'vue';
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <Input v-model:value={record.name} />
          </OtdEditCellItem>
        );
      },
    },
    {
      title: 'Platform',
      dataIndex: 'platform',
      key: 'platform',
      width: 120,
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <OtdStatus v-model:value={record.platform} />
          </OtdEditCellItem>
        );
      },
    },
    { title: 'Version', dataIndex: 'version', key: 'version', width: 120 },
    { title: 'Upgraded', dataIndex: 'upgradeNum', key: 'upgradeNum', width: 120 },
    { title: 'Creator', dataIndex: 'creator', key: 'creator', width: 120 },
    { title: 'Date', dataIndex: 'createdAt', key: 'createdAt', width: 120 },
  ];

  interface DataItem {
    key: number | string;
    id: number | string;
    name: string;
    platform: string;
    version: string;
    upgradeNum: number;
    creator: string;
    createdAt: string;
    children?: DataItem[];
  }

  const data: DataItem[] = [];
  const tableRef = ref([]);
  watch(
    () => unref(tableRef),
    () => {
      console.log(unref(tableRef));
    },
    { deep: true },
  );
  for (let i = 0; i < 3; ++i) {
    data.push({
      key: i,
      id: i,
      name: `Screem ${i + 1}`,
      platform: 'iOS',
      version: '10.3.4.5654',
      upgradeNum: 500,
      creator: 'Jack',
      createdAt: '2014-12-24 23:12:00',
      children:
        i === 1
          ? []
          : [1, 2, 3].map((item) => ({
              key: i + `${item}`,
              id: i + `${item}`,
              name: `Screem ${i + 1}-${item}`,
              platform: 'iOS',
              version: '10.3.4.5654',
              upgradeNum: 500,
              creator: 'Jack',
              createdAt: '2014-12-24 23:12:00',
            })),
    });
  }

  const innerColumns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <Input v-model:value={record.date} />
          </OtdEditCellItem>
        );
      },
    },
    { title: 'Name', dataIndex: 'name', key: 'name', width: 120 },
    { title: 'Status', key: 'state', width: 120 },
    { title: 'Upgrade Status', dataIndex: 'upgradeNum', key: 'upgradeNum', width: 120 },
    {
      title: 'Action',
      dataIndex: 'operation',
      key: 'operation',
      width: 120,
    },
  ];

  interface innerDataItem {
    key: number | string;
    date: string;
    name: string;
    upgradeNum: string;
    children?: innerDataItem[];
  }

  const innerData: innerDataItem[] = [];
  for (let i = 0; i < 3; ++i) {
    innerData.push({
      key: i,
      date: '2014-12-24 23:12:00',
      name: `This is production name ${i + 1}`,
      upgradeNum: 'Upgraded: 56',
      children: new Array(3).fill(0).map((_, index) => {
        return {
          key: i + '-' + index,
          date: '2014-12-24 23:12:00',
          name: `This is production name ${i + 1}-${index}`,
          upgradeNum: 'Upgraded: 56',
          children: new Array(3).fill(0).map((_, index) => {
            return {
              key: i + '-' + index,
              date: '2014-12-24 23:12:00',
              name: `This is production name ${i + 1}-${index}`,
              upgradeNum: 'Upgraded: 56',
            };
          }),
        };
      }),
    });
  }
</script>
