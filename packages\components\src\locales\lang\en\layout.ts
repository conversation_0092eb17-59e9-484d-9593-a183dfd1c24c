export default {
  askAi: 'Odin AI',
  rootDirectory: 'Root Directory',
  header: {
    // user dropdown
    dropdownItemDoc: 'Document',
    dropdownItemLoginOut: 'Log out',

    tooltipErrorLog: 'Error log',
    tooltipLock: 'Lock screen',
    tooltipNotify: 'Notification',

    tooltipEntryFull: 'Full Screen',
    tooltipExitFull: 'Exit Full Screen',

    // lock
    lockScreenPassword: 'Lock screen password',
    lockScreen: 'Lock screen',
    lockScreenBtn: 'Locking',

    home: 'Overview',
    forgetFormTitle: 'Reset password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    editPasswordMessage: 'The passwords entered twice are inconsistent. Procedure',

    help: 'Help',
    changelog: 'Changelog',
    expandHeader: 'Expand header',
    collapseHeader: 'Collapse header',
  },
  lock: {
    unlock: 'Click to unlock',
    alert: 'Lock screen password error',
    backToLogin: 'Back to login',
    entry: 'Enter the system',
    placeholder: 'Please enter the lock screen password',
  },
  config: {
    setting: 'Setting',
    drawerTitle: 'Configuration',
    darkMode: 'Dark mode',
    grayMode: 'Gray mode',
    colorWeak: 'Color Weak Mode',
    interfaceSettings: 'Interface settings',
    on: 'On',
    off: 'Off',
  },
};
