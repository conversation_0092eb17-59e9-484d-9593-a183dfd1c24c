import { Sortable } from 'sortablejs';
import { computed, ComputedRef, getCurrentInstance, ref, Ref, unref } from 'vue';
import { TableInstancePropsType } from '../types';
import { Recordable } from '/#/global';
import { getKey } from './useCustomRow';
import useFlattenRecords from '../../../OtdBasicTable/src/hooks/useFlattenRecords';
export function useDraggableRow(
  propsRef: ComputedRef<TableInstancePropsType>,
  tableData: Ref<Recordable[]>,
  expandOption: ComputedRef<{ expandedRowKeys?: string[]; onExpandedRowsChange?: (keys: string[]) => void }>,
) {
  const { proxy, emit } = getCurrentInstance()!;
  const { rowKey } = propsRef.value;

  const getRowKey = computed(() => {
    const rowKey = unref(propsRef).rowKey;
    if (typeof rowKey === 'function') {
      return rowKey;
    }
    return (record) => {
      const key = record && record[rowKey!];
      return key;
    };
  });

  const mergedExpandedKeys = computed(() => new Set(expandOption.value.expandedRowKeys));

  const childrenColumnName = computed(() => unref(propsRef).childrenColumnName ?? 'children');

  const flattenData = useFlattenRecords(tableData, childrenColumnName, mergedExpandedKeys, getRowKey, ref({}));

  // 初始化表格拖动
  const initSortable = () => {
    const { tableWrapperRef } = proxy?.$refs as any;
    const target = tableWrapperRef.querySelector('.ant-table-tbody');
    let currentDragKey: string | null = null;
    new Sortable(target, {
      group: {
        name: 'shared',
      },
      handle: '.ant-table-row',
      draggable: '.ant-table-row',
      dragClass: 'blue-background-class',
      animation: 300,
      filter: '.item[disabled]',
      sort: true,
      onStart({ oldIndex }) {
        const source = flattenData.value[--oldIndex].record; // 当前
        const rowKeyValue = getKey(source, rowKey);
        const keyIndex = expandOption.value.expandedRowKeys?.findIndex((key) => key === rowKeyValue) ?? -1;
        if (keyIndex > -1) {
          expandOption.value.expandedRowKeys?.splice(keyIndex, 1);
          currentDragKey = rowKeyValue;
        }
      },
      async onEnd({ newIndex, oldIndex }) {
        --newIndex;
        --oldIndex;
        const dataSource = [...flattenData.value];
        const [current] = dataSource.splice(oldIndex, 1);
        const { record } = current; // 当前
        /*new logic start*/
        if (oldIndex === newIndex) {
          return;
        }
        const prevData = dataSource[--newIndex];
        const nextData = dataSource[++newIndex];
        // const isFirst =
        //   prevData?.record.id === record.parentTaskId ||
        //   !prevData?.record.parentTaskId ||
        //   prevData?.record.parentTaskId != record.parentTaskId; //是否移动到分组头
        // const isEnd =
        //   nextData?.record.id === record.parentTaskId ||
        //   !nextData?.record.parentTaskId ||
        //   nextData?.record.parentTaskId != record.parentTaskId; //尾
        /*end*/

        // if (current.indent > 0) {
        //   record.__parent_data[childrenColumnName.value].splice(current.index, 1);
        // }
        // const prevData = dataSource[newIndex - 2]; // 上一个
        // if (prevData?.indent > 0 && prevData.record.__parent_data) {
        //   current.indent = 1;
        //   prevData.record.__parent_data[childrenColumnName.value].push(record);
        // } else {
        //   current.indent = 0;
        //   dataSource.splice(newIndex, 0, current);
        // }
        // const nextData = dataSource[newIndex + 1]; // 下一个

        tableData.value = dataSource.filter((item) => item.indent === 0).map(({ record }) => record);
        setTimeout(() => {
          if (currentDragKey !== null) {
            expandOption.value.expandedRowKeys?.push(currentDragKey);
            currentDragKey = null;
          }
        });
        emit?.('drag-end', {
          // prevRecord: isFirst ? null : prevData?.record,
          // nextRecord: isEnd ? null : nextData?.record,
          prevRecord: prevData?.record,
          nextRecord: nextData?.record,
          record,
          dataSource: unref(tableData),
          newIndex,
          oldIndex,
        });
      },
    });
  };
  return {
    initSortable,
  };
}
