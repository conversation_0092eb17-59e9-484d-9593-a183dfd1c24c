<template>
  <div class="otd-avatar" :style="{ minWidth: size, width: size, height: size }">
    <img :src="handleImage(url)" alt="" />
  </div>
</template>
<script lang="ts" setup>
  import defaultHeader from '/@/assets/images/default-header.svg';

  defineProps({
    url: {
      type: String,
      default: '',
    },
    size: {
      type: String,
      default: '22px',
    },
  });

  function handleImage(url) {
    return url || defaultHeader;
  }
</script>
<style lang="less" scoped>
  .otd-avatar {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    overflow: hidden;
    border: 1px solid var(--otd-white-text);
    > img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background-color: var(--otd-basic-bg);
    }
  }
</style>
