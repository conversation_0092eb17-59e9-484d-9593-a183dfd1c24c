## 标签

<demo src="../demo/Tag/basic.vue" title="标签"></demo>

## 输入框样式

<demo src="../demo/Tag/iptStyle.vue" title="输入框样式"></demo>

## 属性

| 参数           | 说明                                         | 类型     | 可选值 | 默认值 | 版本 |
| -------------- | -------------------------------------------- | -------- | ------ | ------ | ---- |
| value(v-model) | 绑定值                                       | array    | --     | []     | 1.0  |
| placeholder    | placeholder 值                               | string   | --     | ''     | 1.0  |
| iptStyle       | 是否为输入框样式                             | boolean  | --     | false  | 1.0  |
| isShowIcon     | 是否展示 placeholder 的图标                  | boolean  | --     | false  | 1.0  |
| options        | 可选的下拉标签                               | array    | --     | []     | 1.0  |
| updateTags     | 远程获取下拉标签                             | function | --     | --     | 1.0  |
| disabled       | 是否禁用                                     | boolean  | --     | false  | 1.0  |
| maxTagCount    | 最大展示数量，超过后在后方显示剩余标签的个数 | number   | --     | --     | 1.0  |
| change         | 标签改变时的回调方法                         | function | --     | --     | 1.0  |
| delete         | 删除标签时的回调方法                         | function | --     | --     | 1.0  |
| rename         | 重命名标签时的回调方法                       | function | --     | --     | 1.0  |
| save           | 保存标签时的回调方法                         | function | --     | --     | 1.0  |
| create         | 创建标签时的回调方法                         | function | --     | --     | 1.0  |
