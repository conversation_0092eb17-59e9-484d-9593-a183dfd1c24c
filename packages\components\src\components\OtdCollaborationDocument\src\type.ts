import { getProps, getEmits } from './props';
import { ExtractPropTypes } from 'vue';
import { EmitType } from '/#/global';

export interface ISetTaskDocLinkInputDetail {
  id?: string;
  name?: string;
  docLink?: string;
  temporaryId?: string | number;
}
export type CollaborationDocumentPropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
export type CollaborationDocumentEmitType = EmitType<ReturnType<typeof getEmits>[number]>;
