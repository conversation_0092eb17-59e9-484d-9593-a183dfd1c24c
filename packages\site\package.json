{"name": "site", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vitepress dev docs --host", "start": "npm run dev", "build": "vitepress build docs", "serve": "vitepress serve docs"}, "dependencies": {"otd-ui": "workspace:^", "vue": "^3.3.4"}, "devDependencies": {"@vitejs/plugin-vue": "^3.0.3", "@vitejs/plugin-vue-jsx": "^2.1.1", "typescript": "^5.0.2", "vite": "^3.2.5", "vite-plugin-vitepress-demo": "^2.1.0", "vitepress": "1.0.0-rc.25", "vue-tsc": "^1.8.5"}}