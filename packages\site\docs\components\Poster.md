## 海报

<demo src="../demo/Poster/basic.vue" title="海报"></demo>

## 属性

| 参数        | 说明     | 类型                                     | 可选值 | 默认值 | 版本 |
| ----------- | -------- | ---------------------------------------- | ------ | ------ | ---- |
| width       | 宽度     | String                                   | --     | 200px  | 1.0  |
| height      | 高度     | String                                   | --     | 300px  | 1.0  |
| ratio       | 缩放比例 | Number                                   | --     | 1      | 1.0  |
| drawOptions | 绘制配置 | [DrawOptionType](#drawoptiontype-说明)[] | --     | --     | 1.0  |

## DrawOptionType 说明

| 参数       | 说明 | 类型                              | 可选值           | 版本 |
| ---------- | ---- | --------------------------------- | ---------------- | ---- |
| type       | 类型 | String                            | `image` , `text` | 1.0  |
| content    | 内容 | String \| () => Promise\<String\> | --               | 1.0  |
| coordinate | 坐标 | [Number,Number]                   | --               | 1.0  |
| style      | 样式 | [StyleType](#styletype-说明)      | --               | 1.0  |

## StyleType 说明

| 参数         | 说明     | 类型   | 版本 |
| ------------ | -------- | ------ | ---- |
| width        | 宽度     | Number | 1.0  |
| height       | 高度     | Number | 1.0  |
| color        | 字体颜色 | String | 1.0  |
| fontSize     | 字体大小 | Number | 1.0  |
| fontFamily   | 字体     | String | 1.0  |
| borderRadius | 圆角大小 | Number | 1.0  |

## 方法

| 方法       | 说明                     | 参数 | 版本 |
| ---------- | ------------------------ | ---- | ---- |
| toDataURL  | 获取海报 base64 格式内容 | --   | 1.0  |
| drawPoster | 重新绘制海报             | --   | 1.0  |
