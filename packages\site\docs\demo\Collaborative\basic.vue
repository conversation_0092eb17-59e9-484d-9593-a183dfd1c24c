<template>
  <!-- 协作清单 -->
  {{ currentValue }}
  <OtdCollaborative
    v-model:value="currentValue"
    :loading="loading"
    :data="data"
    label="协作清单"
    searchPlaceholder="搜索或创建清单"
    createTip="回车创建新清单"
    @create="handleCreate"
    @delete="handleDelete"
    @rename="handleRename"
    @archive="handleArchive"
    @cancelArchive="handleCancelArchive"
    @select="handleSelect"
    @clear="handleClear"
  />
</template>
<script lang="ts" setup>
  import { OtdCollaborative } from '@otd/otd-ui';
  import { ref } from 'vue';
  const loading = ref(true);
  const data = ref<any[]>([]);
  const currentValue = ref('3a121600-d4b2-37a8-1b45-232f4a909349');

  setTimeout(() => {
    data.value = [
      {
        name: '很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长',
        collaborators: [
          {
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            name: 'limin',
            avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
            isOwner: true,
            authority: 2,
          },
        ],
        isArchived: false,
        archivedByUserId: null,
        archivedTime: null,
        lastModificationTime: '2024-04-23T17:26:46.726866',
        lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-04-22T11:22:55.551932',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        id: '3a121600-d4b2-37a8-1b45-232f4a909349',
        extraProperties: {},
      },
      {
        name: 'hhhh',
        collaborators: [
          {
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            name: 'limin',
            avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
            isOwner: true,
            authority: 2,
          },
        ],
        isArchived: false,
        archivedByUserId: null,
        archivedTime: null,
        lastModificationTime: '2024-04-23T17:26:46.726866',
        lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-04-22T11:22:55.551932',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        id: '3a121600-d4b2-37a8-1b45-232f4a909',
        extraProperties: {},
      },
      {
        name: 'hhhh',
        collaborators: [
          {
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            name: 'limin',
            avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
            isOwner: true,
            authority: 2,
          },
        ],
        isArchived: false,
        archivedByUserId: null,
        archivedTime: null,
        lastModificationTime: '2024-04-23T17:26:46.726866',
        lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-04-22T11:22:55.551932',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        id: '3a121600-d4b2-37a8-1b45-232',
        extraProperties: {},
      },
      {
        name: '99999',
        collaborators: [
          {
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            name: 'limin',
            avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
            isOwner: true,
            authority: 2,
          },
        ],
        isArchived: false,
        archivedByUserId: null,
        archivedTime: null,
        lastModificationTime: '2024-04-23T17:26:46.726866',
        lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-04-22T11:22:55.551932',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        id: '3a121600-d4b2-37a8-1b45-232f4a9093knkn',
        extraProperties: {},
      },
      {
        name: 'aZ',
        collaborators: [
          {
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            name: 'limin',
            avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
            isOwner: true,
            authority: 2,
          },
        ],
        isArchived: true,
        archivedByUserId: null,
        archivedTime: null,
        lastModificationTime: null,
        lastModifierId: null,
        creationTime: '2023-12-08T15:26:33.752748',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        id: '3a0f5a7f-0315-8fe5-1d10-01aa6c856f57',
        extraProperties: {},
      },
      {
        name: '  xx',
        collaborators: [
          {
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            name: 'limin',
            avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
            isOwner: true,
            authority: 2,
          },
        ],
        isArchived: false,
        archivedByUserId: null,
        archivedTime: null,
        lastModificationTime: null,
        lastModifierId: null,
        creationTime: '2023-12-08T15:26:27.3692',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        id: '3a0f5a7e-ea25-c718-af31-db43eacea012',
        extraProperties: {},
      },
    ];
    loading.value = false;
  }, 1000);

  function handleClear(data) {
    console.log('清除', data);
  }
  function handleCancelArchive(data) {
    console.log('取消归档', data);
  }
  function handleArchive(data) {
    console.log('归档', data);
  }
  function handleSelect(data) {
    console.log('选择', data);
  }
  function handleRename(data) {
    console.log('重命名', data);
  }
  function handleDelete(data) {
    console.log('删除', data);
  }
  function handleCreate(data) {
    console.log('创建', data);
  }
</script>
<style lang="less" scoped></style>
