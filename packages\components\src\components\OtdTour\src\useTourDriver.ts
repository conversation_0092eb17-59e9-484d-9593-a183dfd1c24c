import { Config, driver } from 'driver.js';
import { DriveStepType, TourDriverConfigType } from './type';
import { ComponentInternalInstance, getCurrentInstance, render, VNode } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';

function getVNodeToString(vnode: string | VNode, instance?: ComponentInternalInstance) {
  if (typeof vnode === 'string') return vnode;
  const container = document.createElement('div');
  render(vnode, container);
  vnode.component && instance && (vnode.component.parent = instance);
  return container.outerHTML;
}

function getStepRender(config: TourDriverConfigType, instance?: ComponentInternalInstance) {
  config.steps = config.steps?.map((item) => {
    const { popover = {} } = item;
    popover.title && (popover.title = getVNodeToString(popover.title, instance));
    popover.description && (popover.description = getVNodeToString(popover.description, instance));
    return item;
  });
  return config;
}

const triggerMap = {
  click: 'click',
  hover: 'mouseenter',
};

export function useTourDriver() {
  const instance = getCurrentInstance()!;
  const { t } = useI18n();
  const DefaultConfig: TourDriverConfigType = {
    progressText: '{{current}} / {{total}}',
    nextBtnText: t('common.tour.next'),
    prevBtnText: t('common.tour.previous'),
    doneBtnText: t('common.emoticons.Done'),
    stagePadding: 4,
  };
  let driverObj: ReturnType<typeof driver>;

  function setOnHighlightStarted(config: TourDriverConfigType) {
    const onHighlightStarted = config.onHighlightStarted;
    config.onHighlightStarted = (element, step, opt) => {
      const { trigger } = step;
      trigger && element?.addEventListener(triggerMap[trigger], goToNextStep, { once: true });
      onHighlightStarted?.call(config, element, step, opt);
    };
  }

  // 下一步
  function goToNextStep() {
    const step = driverObj.getActiveStep() as DriveStepType;
    setTimeout(() => {
      driverObj.moveNext();
    }, step.delay ?? 200);
  }

  function getDriverConfig(config: TourDriverConfigType): Config {
    getStepRender(config, instance);
    setOnHighlightStarted(config);
    return Object.assign({} as Config, DefaultConfig, config);
  }

  function createDriver(config: TourDriverConfigType) {
    driverObj = driver(getDriverConfig(config));
    return driverObj;
  }

  return {
    createDriver,
  };
}
