<template>
  <OtdTaskTemplate
    placeholder="通过任务模板添加子任务"
    :disabled="false"
    :folderRequest="FolderRequest"
    :getTaskList="getTaskList"
    :draggable="true"
    @confirm="handleConfirm"
    @dragEnd="dragEnd"
  />
</template>
<script lang="tsx" setup>
  import { OtdTaskTemplate, FolderRequestType } from '@otd/otd-ui';
  function handleConfirm(val, { resolve }) {
    console.log('选中的任务', val);
    setTimeout(() => {
      resolve();
    }, 600);
  }
  function dragEnd(val) {
    console.log('sx,111', val);
  }
  function getTaskList() {
    return Promise.resolve([
      {
        taskTemplateId: '3a12b1c7-4261-0265-0489-00a902b0e96c',
        parentTaskId: null,
        title: '确定沙龙名单',
        description: null,
        autoCompletion: true,
        responsibleOrganizeId: null,
        responsibleOrganizeName: '',
        checkerOrganizeId: null,
        checkerOrganizeName: '',
        responsibleUserId: null,
        responsibleUserName: '',
        responsibleUserAvatar: '',
        preTaskItemId: null,
        preTaskItemTitle: '',
        checkUsers: [],
        sort: 1,
        followUsers: [
          {
            userId: '3fa85f64-5717-4562-b3fc-2c963f66afo6',
            name: '周星驰2',
            avatar: 'string',
            creationTime: '2024-10-29T08:04:33.709Z',
          },
          {
            userId: '3fa85f64-5717-4562-b3fc-2c963f66afb6',
            name: '吴孟达',
            avatar: 'string',
            creationTime: '2024-10-29T08:04:33.709Z',
          },
        ],
        childTaskItems: [
          {
            taskTemplateId: '3a12b1c7-4261-0265-0489-00a902b0e96c',
            parentTaskId: '3a12b1c7-428d-f4ad-a9f5-3834b841ef80',
            title: '初步列出意向邀请嘉宾名单',
            description: null,
            autoCompletion: true,
            responsibleOrganizeId: null,
            responsibleOrganizeName: '',
            checkerOrganizeId: null,
            checkerOrganizeName: '',
            responsibleUserId: null,
            responsibleUserName: '',
            responsibleUserAvatar: '',
            preTaskItemId: null,
            preTaskItemTitle: '',
            checkUsers: [],
            followUsers: [
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afo6',
                name: '周星驰2',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afb6',
                name: '吴孟达2',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
            ],
            sort: 101,
            childTaskItems: null,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2024-05-22T09:20:48.776443',
            creatorId: null,
            id: '3a12b1c7-443d-d463-7f47-1d619257037c',
            extraProperties: {},
          },
          {
            taskTemplateId: '3a12b1c7-4261-0265-0489-00a902b0e99c',
            parentTaskId: '3a12b1c7-428d-f4ad-a9f5-3834b841ef80',
            title: '邀请名单',
            description: null,
            autoCompletion: true,
            responsibleOrganizeId: null,
            responsibleOrganizeName: '',
            checkerOrganizeId: null,
            checkerOrganizeName: '',
            responsibleUserId: null,
            responsibleUserName: '',
            responsibleUserAvatar: '',
            preTaskItemId: null,
            preTaskItemTitle: '',
            checkUsers: [],
            followUsers: [
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afo6',
                name: '周星驰2',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afb6',
                name: '吴孟达2',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
            ],
            sort: 101,
            childTaskItems: null,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2024-05-22T09:20:48.776443',
            creatorId: null,
            id: '3a12b1c7-443d-d463-7f47-1d619257039c',
            extraProperties: {},
          },
          {
            taskTemplateId: '3a12b1c7-4261-0265-0489-00a902b0e96c',
            parentTaskId: '3a12b1c7-428d-f4ad-a9f5-3834b841ef80',
            title: '嘉宾联系确认是否参加',
            description: null,
            autoCompletion: true,
            responsibleOrganizeId: null,
            responsibleOrganizeName: '',
            checkerOrganizeId: null,
            checkerOrganizeName: '',
            responsibleUserId: null,
            responsibleUserName: '',
            responsibleUserAvatar: '',
            preTaskItemId: '3a12b1c7-443d-d463-7f47-1d619257037c',
            preTaskItemTitle: '初步列出意向邀请嘉宾名单',
            checkUsers: [],
            followUsers: [
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afo6',
                name: '周星驰',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afb6',
                name: '吴孟达',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
            ],
            sort: 201,
            childTaskItems: null,
            lastModificationTime: '2024-05-22T09:20:49.310686',
            lastModifierId: null,
            creationTime: '2024-05-22T09:20:48.798755',
            creatorId: null,
            id: '3a12b1c7-4497-abd8-cb48-b5c7b32586ff',
            extraProperties: {},
          },
          {
            taskTemplateId: '3a12b1c7-4261-0265-0489-00a902b0e96c',
            parentTaskId: '3a12b1c7-428d-f4ad-a9f5-3834b841ef80',
            title: '最终敲定嘉宾名单',
            description: null,
            autoCompletion: true,
            responsibleOrganizeId: null,
            responsibleOrganizeName: '',
            checkerOrganizeId: null,
            checkerOrganizeName: '',
            responsibleUserId: null,
            responsibleUserName: '',
            responsibleUserAvatar: '',
            preTaskItemId: '3a12b1c7-4497-abd8-cb48-b5c7b32586ff',
            preTaskItemTitle: '嘉宾联系确认是否参加',
            checkUsers: [],
            followUsers: [
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afo6',
                name: '周星驰',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afb6',
                name: '吴孟达',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
            ],
            sort: 301,
            childTaskItems: null,
            lastModificationTime: '2024-05-22T09:20:49.338794',
            lastModifierId: null,
            creationTime: '2024-05-22T09:20:48.814526',
            creatorId: null,
            id: '3a12b1c7-44a8-49f0-076a-2db821bc2181',
            extraProperties: {},
          },
        ],
        lastModificationTime: null,
        lastModifierId: null,
        creationTime: '2024-05-22T09:20:48.452609',
        creatorId: null,
        id: '3a12b1c7-428d-f4ad-a9f5-3834b841ef80',
        extraProperties: {},
      },
      {
        taskTemplateId: '3a12b1c7-4261-0265-0489-00a902b0e96c',
        parentTaskId: null,
        title: '沙龙后续工作',
        description: null,
        autoCompletion: true,
        responsibleOrganizeId: null,
        responsibleOrganizeName: '',
        checkerOrganizeId: null,
        checkerOrganizeName: '',
        responsibleUserId: null,
        responsibleUserName: '',
        responsibleUserAvatar: '',
        preTaskItemId: null,
        preTaskItemTitle: '',
        checkUsers: [],
        followUsers: [
          {
            userId: '3fa85f64-5717-4562-b3fc-2c963f66afo6',
            name: '周星驰',
            avatar: 'string',
            creationTime: '2024-10-29T08:04:33.709Z',
          },
          {
            userId: '3fa85f64-5717-4562-b3fc-2c963f66afb6',
            name: '吴孟达',
            avatar: 'string',
            creationTime: '2024-10-29T08:04:33.709Z',
          },
        ],
        sort: 2501,
        childTaskItems: [
          {
            taskTemplateId: '3a12b1c7-4261-0265-0489-00a902b0e96c',
            parentTaskId: '3a12b1c7-4619-7380-982c-da2d316851fb',
            title: '收拾现场',
            description: null,
            autoCompletion: true,
            responsibleOrganizeId: null,
            responsibleOrganizeName: '',
            checkerOrganizeId: null,
            checkerOrganizeName: '',
            responsibleUserId: null,
            responsibleUserName: '',
            responsibleUserAvatar: '',
            preTaskItemId: null,
            preTaskItemTitle: '',
            checkUsers: [],
            followUsers: [
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afo6',
                name: '周星驰2',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afb6',
                name: '吴孟达2',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
            ],
            sort: 2601,
            childTaskItems: null,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2024-05-22T09:20:49.197088',
            creatorId: null,
            id: '3a12b1c7-4627-78e3-8fdf-04b006a106a8',
            extraProperties: {},
          },
          {
            taskTemplateId: '3a12b1c7-4261-0265-0489-00a902b0e96c',
            parentTaskId: '3a12b1c7-4619-7380-982c-da2d316851fb',
            title: '准备报道',
            description: null,
            autoCompletion: true,
            responsibleOrganizeId: null,
            responsibleOrganizeName: '',
            checkerOrganizeId: null,
            checkerOrganizeName: '',
            responsibleUserId: null,
            responsibleUserName: '',
            responsibleUserAvatar: '',
            preTaskItemId: null,
            preTaskItemTitle: '',
            checkUsers: [],
            followUsers: [
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afo6',
                name: '周星驰',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afb6',
                name: '吴孟达',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
            ],
            sort: 2701,
            childTaskItems: null,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2024-05-22T09:20:49.211951',
            creatorId: null,
            id: '3a12b1c7-4637-f461-bc15-a7505126c551',
            extraProperties: {},
          },
          {
            taskTemplateId: '3a12b1c7-4261-0265-0489-00a902b0e96c',
            parentTaskId: '3a12b1c7-4619-7380-982c-da2d316851fb',
            title: '持续跟进意向客户',
            description: null,
            autoCompletion: true,
            responsibleOrganizeId: null,
            responsibleOrganizeName: '',
            checkerOrganizeId: null,
            checkerOrganizeName: '',
            responsibleUserId: null,
            responsibleUserName: '',
            responsibleUserAvatar: '',
            preTaskItemId: null,
            preTaskItemTitle: '',
            checkUsers: [],
            followUsers: [
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afo6',
                name: '周星驰',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
              {
                userId: '3fa85f64-5717-4562-b3fc-2c963f66afb6',
                name: '吴孟达',
                avatar: 'string',
                creationTime: '2024-10-29T08:04:33.709Z',
              },
            ],
            sort: 2801,
            childTaskItems: null,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2024-05-22T09:20:49.228534',
            creatorId: null,
            id: '3a12b1c7-4646-2751-dce1-6a4b1cc83df3',
            extraProperties: {},
          },
          {
            taskTemplateId: '3a12b1c7-4261-0265-0489-00a902b0ew6c',
            parentTaskId: '3a12b1c7-4619-7380-982c-da2d316851fb',
            title: '收集市场反馈',
            description: null,
            autoCompletion: true,
            responsibleOrganizeId: null,
            responsibleOrganizeName: '',
            checkerOrganizeId: null,
            checkerOrganizeName: '',
            responsibleUserId: null,
            responsibleUserName: '',
            responsibleUserAvatar: '',
            preTaskItemId: null,
            preTaskItemTitle: '',
            checkUsers: [],
            followUsers: [],
            sort: 2801,
            childTaskItems: null,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2024-05-22T09:20:49.228534',
            creatorId: null,
            id: '3a12b1c7-4646-2751-dce1-6a4b1cc83cf3',
            extraProperties: {},
          },
        ],
        lastModificationTime: null,
        lastModifierId: null,
        creationTime: '2024-05-22T09:20:49.181936',
        creatorId: null,
        id: '3a12b1c7-4619-7380-982c-da2d316851fb',
        extraProperties: {},
      },
    ]);
  }

  const FolderRequest: FolderRequestType = {
    getList: () => {
      const time = Date.now();
      return Promise.resolve({
        items: [
          {
            name: '测试-任务模板01',
            description: '测试-任务模板01',
            parentDirectory: null,
            type: 'Directory',
            hasChildDirectory: true,
            hasChildTemplate: true,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2023-05-31T16:26:17.633994',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0b8317-0e82-e436-108c-4fb3d4df99fc',
            extraProperties: {},
          },
          {
            name: '测试-任务模板02',
            description: '',
            parentDirectory: null,
            type: 'Directory',
            hasChildDirectory: false,
            hasChildTemplate: true,
            lastModificationTime: '2023-05-31T16:40:38.489108',
            lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            creationTime: '2023-05-31T16:26:23.600571',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0b8317-25ec-45c0-4509-a0caaee07e1f',
            extraProperties: {},
          },
        ],
      });
    },
    getResourceList: () => {
      const time = Date.now();
      return Promise.resolve({
        items: [
          {
            name: '测试-任务模板01',
            description: '测试-任务模板01',
            parentDirectory: null,
            type: 'Directory',
            hasChildDirectory: true,
            hasChildTemplate: true,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2023-05-31T16:26:17.633994',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0b8317-0e82-e436-108c-4fb3d4df99fc',
            extraProperties: {},
          },
          {
            name: '测试-任务模板02',
            description: '',
            parentDirectory: null,
            type: 'Directory',
            hasChildDirectory: false,
            hasChildTemplate: true,
            lastModificationTime: '2023-05-31T16:40:38.489108',
            lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            creationTime: '2023-05-31T16:26:23.600571',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0b8317-25ec-45c0-4509-a0caaee07e1f',
            extraProperties: {},
          },
          {
            name: '测试任务12',
            description: '测试任务11',
            parentDirectoryId: null,
            childMaxLevel: 0,
            isDeleted: false,
            deleterId: null,
            deletionTime: null,
            lastModificationTime: '2023-09-06T14:17:41.252426',
            lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            creationTime: '2023-04-28T09:50:25.986653',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0ad7ba-c5e5-458c-67ae-e20cd0319a7f',
            extraProperties: {},
          },
          {
            name: '测试任务2',
            description: '测试任务2',
            parentDirectoryId: null,
            childMaxLevel: 0,
            isDeleted: false,
            deleterId: null,
            deletionTime: null,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2023-04-28T10:33:05.794403',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0ad7e1-d5ff-b0dd-7018-e273ba033e40',
            extraProperties: {},
          },
        ],
      });
    },
    create: (form) => {
      // console.log(form, 'create');
      return Promise.resolve();
    },
    update: (form) => {
      // console.log(form, 'update');
      return Promise.resolve();
    },
    delete: () => {
      return Promise.resolve();
    },
    move: (...arg) => {
      return Promise.resolve();
    },
  };
</script>
<style lang="less" scoped></style>
