<template>
  <OtdTable class="otd-expand-tree" @register="register" v-bind="$attrs" @expand="handleExpand">
    <template #[slot]="data" v-for="(_, slot) in otherSlots" :key="slot">
      <slot :name="slot" v-bind="data"></slot>
    </template>
    <template #expandedRowRender="data">
      <div class="otd-expand-tree-children" v-if="isExpand(data.record)">
        <slot name="expandedRowRender" v-bind="data"></slot>
      </div>
    </template>
  </OtdTable>
</template>
<script lang="tsx" setup name="OtdExpandTreeTable">
  import { omit } from 'lodash-es';
  import type { Recordable } from '/#/global';
  import { OtdTable } from '/@/components/OtdTable/src/OtdTable';
  import { computed, PropType, useSlots } from 'vue';

  defineProps({
    isExpand: {
      type: Function as PropType<(data) => boolean>,
      default: () => true,
    },
  });

  const emit = defineEmits(['register', 'expand']);

  const slots = useSlots();
  const otherSlots = computed(() => omit(slots ?? {}, ['expandedRowRender']));

  function register(...arg) {
    emit('register', ...arg);
  }

  function handleExpand(status: boolean, record: Recordable) {
    emit('expand', status, record);
  }
</script>
<style lang="less" scoped>
  :deep(.ant-table-expanded-row) {
    border-top-width: 0;
    > .ant-table-cell {
      border-top-width: 0 !important;
      padding: 0 !important;
    }
  }

  :deep(.ant-table-expand-icon-col) {
    width: 0;
  }
  :deep(.otd-arrow) {
    position: relative;
    z-index: 2;
  }
  :deep(.otd-expand-tree) {
    .otd-expand-tree-children {
      padding: 14px 16px;
    }
    > .ant-spin-nested-loading
      > .ant-spin-container
      > .ant-table
      > .ant-table-container
      > .ant-table-body
      > table
      > .ant-table-tbody {
      > .ant-table-row {
        cursor: pointer;
        .row(@n, @i: 0) when (@i < @n) {
          &.ant-table-row-level-@{i} {
            .otd-arrow {
              margin-left: (12px * @i) - 6px;
            }
            .ant-table-cell:nth-of-type(2) {
              padding-left: ((12px * @i)) + 36px + 14;
            }
          }
          .row(@n, (@i + 1));
        }
        .row(50);
      }
    }
  }
</style>
