<template>
  <div class="otd-task-type-trigger otd-box-left">
    <OtdIconPark :type="value.icon" :key="value.icon" theme="filled" />
    <span style="flex: 1" v-if="!isIcon">{{ value.label }}</span>
  </div>
</template>
<script lang="ts" setup>
  import { PropType } from 'vue';
  import { OtdIconPark } from '/@/components/BasicIcon';

  defineProps({
    value: {
      type: Object as PropType<{ icon: string; label: string }>,
      required: true,
    },
    isIcon: {
      type: Boolean,
      default: false,
    },
  });
</script>
<style lang="less" scoped>
  .otd-task-type-trigger {
    column-gap: 4px;
    flex: 1;
  }
</style>
