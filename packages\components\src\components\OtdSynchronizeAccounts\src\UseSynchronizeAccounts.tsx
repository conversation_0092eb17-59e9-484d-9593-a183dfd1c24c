import type { TableColumnPropsType } from '/@/components/OtdTable/src/OtdTable';
import type { UserWithOrgProjType } from './type';
import { createVNode, ref, getCurrentInstance, unref } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { ExcelData } from '/@/components/OtdExcelAction/index';
import { cloneDeep, flatten } from 'lodash-es';
import type {
  SynchronizeAccountsEmitType,
  SynchronizeAccountsPropsType,
} from '/@/components/OtdSynchronizeAccounts/src/type';

export function UseSynchronizeAccounts() {
  const { props } = getCurrentInstance() as unknown as {
    props: SynchronizeAccountsPropsType;
    emit: SynchronizeAccountsEmitType;
  };
  const visible = ref(false);
  const importLoading = ref(false);
  const saveLoading = ref(false);
  const tableLoading = ref(false);
  const isUpload = ref(true);
  const systemTableList = ref<UserWithOrgProjType[]>([]);
  const syncedList = ref<UserWithOrgProjType[]>([]);
  const SystemUserMap = new Map<string, UserWithOrgProjType[]>();
  const ExcelUserMap = new Map<string, UserWithOrgProjType[]>();
  // 异常
  const ErrorUserMap = new Map<string, UserWithOrgProjType[]>();
  // 正常
  const NormalUserMap = new Map<string, UserWithOrgProjType[]>();
  let syetenDafaultList: UserWithOrgProjType[] = [];
  const { t } = useI18n();

  const tableColumns: TableColumnPropsType[] = [
    {
      title: t('common.SynchronizeAccounts.userManagement_name'),
      dataIndex: 'name',
      align: 'left',
    },
    {
      title: t('common.SynchronizeAccounts.userManagement_email'),
      dataIndex: 'email',
      align: 'left',
    },
    {
      title: t('common.SynchronizeAccounts.userManagement_phone'),
      dataIndex: 'phoneNumber',
      align: 'left',
    },
    {
      title: t('common.SynchronizeAccounts.userManagement_department'),
      dataIndex: 'name',
      align: 'left',
      ellipsis: true,
      customRender: ({ record }) => {
        return <div>{record.orgUnits.map((item) => item.displayName).join(',')}</div>;
      },
    },
    {
      title: '',
      dataIndex: 'operation',
      width: '300px',
    },
  ];

  function setMapValue(values: Map<string, UserWithOrgProjType[]>, data: UserWithOrgProjType) {
    if (!values.has(data.name!)) {
      values.set(data.name!, [data]);
    } else {
      const names = values.get(data.name!);
      names!.push(data);
    }
  }

  function setUserMap(list: UserWithOrgProjType[]) {
    SystemUserMap.clear();
    list.filter((item: UserWithOrgProjType) => {
      if (item.isActive) {
        const users = unref(SystemUserMap);
        item.newEmail = item.email;
        item.newPhone = item.phoneNumber;
        setMapValue(users, item);
      }
    });
    systemTableList.value = flatten(Array.from(SystemUserMap.values()));
  }

  /**
   * 列表
   * @param
   * @returns
   */
  function getTableListAsync() {
    tableLoading.value = true;
    props
      .getList()
      .then((res) => {
        syetenDafaultList = res ?? [];
        setUserMap(res ?? []);
        tableLoading.value = false;
      })
      .catch(() => {
        tableLoading.value = false;
      });
  }

  function handleRemoveWXUser(index) {
    if (isUpload.value) return;
    Modal.confirm({
      title: `${t('common.SynchronizeAccounts.userManagement_okRemover')}?`,
      icon: createVNode(ExclamationCircleOutlined),
      centered: true,
      closable: true,
      onOk() {
        systemTableList.value.splice(index, 1);
        message.success(t('common.operationSuccess'));
      },
      onCancel() {},
      class: 'otd-dialog',
    });
  }

  function judgeUserInfo(user: UserWithOrgProjType, excel: UserWithOrgProjType, isSync = true) {
    let isError = false;
    const email = user.newEmail ?? user.email;
    if ((email || excel.email) && email !== excel.email) {
      user.isEmailError = true;
      isError = true;
    } else {
      delete user.isEmailError;
    }
    const phoneNumber = user.newPhone ?? user.phoneNumber;
    if ((phoneNumber || excel.phoneNumber) && phoneNumber != excel.phoneNumber) {
      user.isPhoneError = true;
      isError = true;
    } else {
      delete user.isPhoneError;
    }
    if (!isError) {
      user.matchingSuccess = true;
      if (isSync) {
        user.notSync = true;
      }
    }
    return isError;
  }

  const keyMap = {
    phoneNumber: 'newPhone',
    email: 'newEmail',
  };

  function getUnuseedExcel(name: string, key = 'email') {
    return (
      ExcelUserMap.get(name!)
        ?.map((item) => ({
          name: item.name + '-' + item[key],
          email: item.email,
          phoneNumber: item.phoneNumber,
        }))
        .filter((option) => {
          return option[key] && SystemUserMap.get(name!)?.findIndex((user) => user[keyMap[key]] == option[key]) === -1;
        }) ?? []
    );
  }

  function setUserInfo(user, excel) {
    user.newEmail = user.email || excel.email;
    user.newPhone = user.phoneNumber || excel.phoneNumber;
    judgeUserInfo(user, excel, false);
    if (user.matchingSuccess) {
    }
  }

  function judgeErrorUser(users: UserWithOrgProjType[], key: string) {
    if (users.length > 1) {
      /**
       *  const excels = ExcelUserMap.get(key)!;
       *       const isError = users.every((user) => {
       *         const list = getUnuseedExcel(key);
       *         console.log(list);
       *         return excels.every((excel) => {
       *           const result = judgeUserInfo(user, excel);
       *           if (list.length === 1) {
       *             console.log('2222222');
       *             setUserInfo(user, excel);
       *           }
       *           console.log(result);
       *           return result;
       *         });
       *       });
       *       return isError;
       */
      const excels = ExcelUserMap.get(key)!;
      let isError;
      users.forEach((user) => {
        const phoneNumber = user.newPhone ?? user.phoneNumber;
        const email = user.newEmail ?? user.email;
        const emailTarget = excels.find((item) => item.email == email);
        const phoneTarget = excels.find((item) => item.phoneNumber == phoneNumber);
        if (
          (emailTarget?.email === email && emailTarget?.phoneNumber == phoneNumber) ||
          (phoneTarget?.email === email && phoneTarget?.phoneNumber == phoneNumber)
        ) {
          // 全等已同步
          user.notSync = true;
        }
        if (emailTarget) {
          if (emailTarget.email && email && emailTarget.email != email) {
            isError = true;
            user.isEmailError = true;
          }
          user.phoneNumber = emailTarget?.phoneNumber;
          user.newPhone = emailTarget?.phoneNumber;
        }

        if (phoneTarget) {
          if (phoneTarget.phoneNumber && phoneNumber && phoneTarget.phoneNumber != phoneNumber) {
            user.isPhoneError = true;
            isError = true;
          }
          user.email = phoneTarget?.email;
          user.newEmail = phoneTarget?.email;
        }
        // 无报错,姓名匹配
        if (!user.isEmailError && !user.isPhoneError && !user.notSync) {
          user.matchingSuccess = true;
        }
      });
      return isError;
    } else {
      const [user] = users;
      if (ExcelUserMap.has(key)) {
        const [excel] = ExcelUserMap.get(key)!;
        const result = judgeUserInfo(user, excel);
        setUserInfo(user, excel);
        return result;
      } else {
        return false;
      }
    }
  }

  function loadDataSuccess(excelDataList: ExcelData[]) {
    importLoading.value = true;
    tableLoading.value = true;
    ExcelUserMap.clear();
    setUserMap(cloneDeep(syetenDafaultList));
    for (const excelData of excelDataList) {
      const { results } = excelData;
      results.slice(9).map((item) => {
        const data: UserWithOrgProjType = {
          name: item['填写须知：'],
          email: item['__EMPTY_7'],
          phoneNumber: item['__EMPTY_5'],
        };
        setMapValue(ExcelUserMap, data);
      });
    }
    unref(SystemUserMap).forEach((item, key) => {
      const isError = judgeErrorUser(item, key);
      if (isError) {
        ErrorUserMap.set(key, item);
      } else {
        NormalUserMap.set(key, item);
      }
    });
    const ErrorData = flatten(Array.from(ErrorUserMap.values()));
    const NormalData = flatten(Array.from(NormalUserMap.values()));
    const Arr = [...ErrorData, ...NormalData];
    systemTableList.value = Arr.filter((item) => !item.notSync);
    syncedList.value = Arr.filter((item) => item.notSync);
    importLoading.value = false;
    isUpload.value = false;
    tableLoading.value = false;
    message.success(t('common.SynchronizeAccounts.userManagement_inductsSuccess'));
  }

  function handleSelectChange(option, record: UserWithOrgProjType) {
    record.newEmail = option.email || record.email;
    record.newPhone = option.phoneNumber || record.phoneNumber;
    judgeUserInfo(record, option, false);
  }

  function closeModal() {
    systemTableList.value = [];
    syncedList.value = [];
    isUpload.value = false;
    SystemUserMap.clear();
    ExcelUserMap.clear();
    ErrorUserMap.clear();
    NormalUserMap.clear();
  }

  function handleSave() {
    saveLoading.value = true;
    // const _userServiceProxy = new UsersServiceProxy();
    const saveList = systemTableList.value.map((item) => {
      const phone = String(item.newPhone) ?? String(item.phoneNumber);
      return {
        id: item.id,
        name: item.name,
        phoneNumber: phone === 'null' ? '' : phone ?? '',
        email: item.newEmail ?? item.email,
      };
    });

    props
      .batchUpdateUser(saveList)
      .then(() => {
        visible.value = false;
        saveLoading.value = false;
      })
      .catch(() => {
        saveLoading.value = false;
      });
  }

  return {
    visible,
    importLoading,
    systemTableList,
    SystemUserMap,
    ExcelUserMap,
    syncedList,
    saveLoading,
    tableColumns,
    isUpload,
    tableLoading,
    getUnuseedExcel,
    closeModal,
    handleSave,
    handleSelectChange,
    getTableListAsync,
    handleRemoveWXUser,
    loadDataSuccess,
  };
}
