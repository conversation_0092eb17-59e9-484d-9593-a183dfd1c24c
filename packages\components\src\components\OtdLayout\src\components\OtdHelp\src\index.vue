<template>
  <!-- <Tooltip :title="t('layout.header.help')" placement="bottom" :mouseEnterDelay="0.5"> -->
  <MenuItem class="otd-help-picker" @click.stop="openHelpDocument">
    <template #icon>
      <i class="otdIconfont otd-icon-A_icons_help_d"></i>
    </template>
    <span>{{ t('layout.header.help') }}</span>
  </MenuItem>
  <!-- </Tooltip> -->
</template>
<script lang="ts" setup name="OtdFullScreen">
  // import { Tooltip } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
  import { MenuItem } from 'ant-design-vue';

  const { t } = useI18n();
  const { getGlobalProvide } = useGlobalConfig();
  const { helpDocument } = getGlobalProvide();
  function openHelpDocument() {
    helpDocument && window.open(helpDocument);
  }
</script>
