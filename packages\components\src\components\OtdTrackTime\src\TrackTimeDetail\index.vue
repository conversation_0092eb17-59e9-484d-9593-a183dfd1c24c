<template>
  <BasicModal
    width="720px"
    :title="title"
    :can-fullscreen="false"
    :mask-closable="false"
    :footer="null"
    centered
    @register="register"
  >
    <div class="otd-track-time-detail">
      <!-- 整体进展 -->
      <div class="otd-track-time-detail__content">
        <h3>{{ t('common.trackTime.ProgressOverview') }}</h3>
        <div class="otd-track-time-detail__content-body">
          <!-- 计划工时 -->
          <div class="flex-1">
            <span class="otd-desc-text"> {{ t('common.trackTime.plannedTrackingTime') }}</span>
            <span class="ml-6px"> {{ getCacheValue(hourDetail, 'planSpendHours') }}{{ t('common.hours') }} </span>
          </div>
          <!-- 实际工时 -->
          <div class="flex-1">
            <span class="otd-desc-text"> {{ t('common.trackTime.actualTrackingTime') }}</span>
            <span class="ml-6px"> {{ getCacheValue(hourDetail, 'realSpendHours') }}{{ t('common.hours') }} </span>
          </div>
          <div class="flex-1">
            <Progress
              :strokeWidth="6"
              :percent="
                computedPercent(
                  getCacheValue(hourDetail, 'realSpendHours'),
                  getCacheValue(hourDetail, 'planSpendHours'),
                )
              "
              :show-info="false"
              :status="
                Number(getCacheValue(hourDetail, 'realSpendHours')) >
                Number(getCacheValue(hourDetail, 'planSpendHours'))
                  ? 'exception'
                  : undefined
              "
            />
          </div>
        </div>
      </div>
      <!-- 详情 -->
      <div class="otd-track-time-detail__content">
        <div class="otd-box-left">
          <h3>{{ t('common.detail') }}</h3>
          <FormItem :label="t('common.trackTime.WorkHourCategory')">
            <Select
              v-model:value="currentCategory"
              :options="categoryOptions"
              :placeholder="t('common.trackTime.WorkHourCategory')"
              allow-clear
              @change="handleChangeCategory"
            />
          </FormItem>
        </div>
        <div class="otd-track-time-detail__content-body is-table">
          <OtdTable
            :columns="tableColumns"
            :data-source="hourDetail.users"
            :pagination="false"
            :showIndexColumn="false"
            :scroll="{ y: 360 }"
            row-key="userId"
            bordered
          >
            <template #expandedRowRender="{ record }">
              <div class="p-8px">
                <h3>{{ t('common.trackTime.HoursRecord') }}</h3>
                <ol class="hours-record-group" start="1">
                  <li
                    v-for="item in getCacheValue(record, 'taskHours')"
                    :key="item.id"
                    @click="handleEditRecord(item, record)"
                  >
                    <div class="hours-record-item placeholder-hover">
                      <div class="hours-record-item__content">
                        <div class="otd-box-left">
                          <span v-if="item.category">[ {{ item.category }} ]</span>
                          <span>{{ getHourType(item.hourType) }}:</span>
                          <span>{{ item.workHour }} {{ t('common.hours') }} ,</span>
                          <span> {{ item.workDate ? formatToDate(item.workDate) : NOTDATA }} </span>
                        </div>
                        <div class="otd-desc-text otd-truncate">{{ item.remark }}</div>
                      </div>
                      <OtdMoreAction
                        :disabled="disabled"
                        :data="{ data: mergeHourInfo(item, record) }"
                        :actions="actions"
                        actionType="icon"
                      />
                    </div>
                  </li>
                </ol>
              </div>
            </template>
          </OtdTable>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { FormItem, Progress, Select } from 'ant-design-vue';
  import { BasicModal } from '/@/components/BasicModal';
  import { useTrackTimeDetail } from './useTrackTimeDetail';
  import { ActionItem } from '/@/utils/types';
  import { PropType } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { ITaskHourDetailDto } from '../type';
  import { OtdMoreAction } from '/@/components/OtdMoreAction';
  import { OtdTable } from '/@/components/OtdTable';
  import { formatToDate } from '/@/tool';
  import { NOTDATA } from '/@/settings/const';
  import { LabelValueOptions } from '/#/global';

  defineProps({
    title: {
      type: String,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    loadDetail: {
      type: Function as PropType<() => Promise<ITaskHourDetailDto>>,
      default: undefined,
    },
    // 操作
    actions: {
      type: Array as PropType<ActionItem[]>,
      default: () => [],
    },
    categoryOptions: {
      type: Array as PropType<LabelValueOptions>,
    },
  });
  const emit = defineEmits(['register', 'delete-detail', 'reload']);
  const { t } = useI18n();

  const [
    register,
    {
      currentCategory,
      getCacheValue,
      handleChangeCategory,
      hourDetail,
      tableColumns,
      computedPercent,
      getHourType,
      loadHourDetail,
      mergeHourInfo,
      handleEditRecord,
    },
  ] = useTrackTimeDetail();

  defineExpose({
    reload: loadHourDetail,
  });
</script>
<style lang="less" scoped>
  .otd-track-time-detail {
    :deep(.ant-progress) {
      width: 100px;
      margin: 0;
      display: inline-flex;
      align-items: center;
      .ant-progress-inner {
        display: block;
      }
    }
    &__content {
      padding: 4px 8px 20px 8px;

      > .otd-box-left {
        justify-content: space-between;
        margin-bottom: 10px;
        h3 {
          line-height: 24px;
        }
        .ant-form-item {
          margin-bottom: 0;
          .ant-select {
            width: 120px;
          }
        }
      }
      & + & {
        border-top: 1px solid var(--otd-border-color);
        padding-top: 20px;
      }
      &-body {
        display: flex;
        :deep(.ant-table-row) {
          position: sticky;
          top: 0;
          z-index: 1;
        }
        &.is-table {
          height: 400px;
        }
        .flex-1 {
          flex: 1;
        }
      }
    }
    :deep(&__action) {
      display: flex;
      align-items: center;
      .otd-more-action {
        margin-left: 10px;
      }
    }
    .hours-record-group {
      padding-left: 0;
      margin-bottom: 0;
      > li {
        list-style: decimal;
        margin-left: 40px;
        &::marker {
          color: var(--otd-gray3-color);
          display: flex;
        }
      }
      .hours-record-item {
        display: flex;
        line-height: 22px;
        width: 100%;
        &__content {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          .otd-box-left {
            column-gap: 10px;
          }
        }
      }
    }
  }
</style>
