export const SIDE_BAR_MINI_WIDTH = 68;
export const SIDE_BAR_SHOW_TIT_MINI_WIDTH = 80;

/**
 * 主题
 */
export enum ThemeEnum {
  DARK = "dark",
  LIGHT = "light",
}

/**
 * 国际化
 */
export enum LocaleEnum {
  ZH_CN = "zh_CN",
  EN_US = "en",
}

/**
 * 路由切换动画
 */
export enum RouterTransitionEnum {
  ZOOM_FADE = "zoom-fade",
  ZOOM_OUT = "zoom-out",
  FADE_SIDE = "fade-slide",
  FADE = "fade",
  FADE_BOTTOM = "fade-bottom",
  FADE_SCALE = "fade-scale",
}

/**
 * 排序
 */
export enum SortEmnu {
  ASC = "asc",
  DESC = "desc",
}
