import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
import { FormSchema, useForm } from '/@/components/BasicForm';
import { useI18n } from '/@/hooks/web/useI18n';
import { useModalInner } from '/@/components/BasicModal';
import { useFolder } from '../../../useFolder';

export function useCreateFolder() {
  const { emit } = getCurrentInstance() as ComponentInternalInstance;
  const { t } = useI18n();
  const isEdit = ref(false);

  const createForm: FormSchema[] = [
    {
      field: 'id',
      label: '',
      ifShow: false,
      component: 'Input',
    },
    // 父级文件夹id
    {
      field: 'parentDirectory',
      label: '',
      ifShow: false,
      component: 'Input',
    },
    // 名称
    {
      field: 'name',
      component: 'Input',
      label: t('common.name'),
      required: true,
      colProps: { span: 24 },
    },
    // 描述
    {
      field: 'description',
      component: 'InputTextArea',
      label: t('common.description'),
      colProps: { span: 24 },
      componentProps: { autosize: { minRows: 1, maxRows: 5 } },
    },
  ];
  const [registerForm, { resetFields, setFieldsValue, getFieldsValue, validate }] = useForm({
    layout: 'vertical',
    labelWidth: 120,
    schemas: createForm,
    showActionButtonGroup: false,
  });
  const [registerModal, { changeOkLoading, closeModal }] = useModalInner(({ parentDirectory = -1, data }) => {
    isEdit.value = !!data?.id;
    setFieldsValue({ parentDirectory: parentDirectory || -1, ...(data ?? {}) });
  });

  const { updateFolder, createFolder } = useFolder();

  const submitForm = async () => {
    try {
      await validate();
      changeOkLoading(true);
      let request = getFieldsValue();
      if (request.id) {
        await updateFolder(request);
      } else {
        await createFolder(request);
      }
      closeModal();
      changeOkLoading(false);

      emit('reload');
    } catch (error) {
      changeOkLoading(false);
    }
  };

  function cancelForm() {
    resetFields();
    isEdit.value = false;
    return Promise.resolve(true);
  }
  return {
    isEdit,
    registerForm,
    cancelForm,
    submitForm,
    registerModal,
  };
}
