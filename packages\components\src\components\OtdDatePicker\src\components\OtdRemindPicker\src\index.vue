<template>
  <div class="otd-remind-picker">
    <i class="otdIconfont otd-icon-lingdang-xianxing"></i>
    <Select
      class="otd-noborder-select"
      :placeholder="t('common.datePicker.DueReminder')"
      :bordered="false"
      :show-arrow="false"
      :dropdownStyle="{ minWidth: '200px' }"
      :options="pickerOptions"
      :open="selectVisible"
      placement="topLeft"
      allow-clear
      v-bind="$attrs"
      @select="handleSelect"
      @change="handleChange"
      @dropdown-visible-change="(open) => (selectVisible = open)"
    >
      <template #dropdownRender="{ menuNode: menu }">
        <VNodes :vnodes="menu" />
        <button class="otd-noborder-select__custom" @click="handleSelectCustom">
          {{ t('common.repeatPicker.custom') }}
        </button>
      </template>
    </Select>
    <RemindPickerModal @register="registerModal" @ok="handleConfirm" />
  </div>
</template>
<script lang="ts" setup>
  import type { DateValueType } from '/@/components/OtdDatePicker/src/type';
  import { useAttrs, watchEffect, type PropType } from 'vue';
  import { Select } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useRemindPicker } from './useRemindPicker';
  import { VNodes } from '/@/components/OtdDatePicker/src/useDatePicker';
  import RemindPickerModal from './RemindPickerModal.vue';
  defineProps({
    dates: {
      type: Array as unknown as PropType<[DateValueType, DateValueType]>,
    },
  });
  defineEmits(['change']);

  const { t } = useI18n();
  const [
    registerModal,
    { pickerOptions, selectVisible, getTipStringText, handleSelectCustom, handleConfirm, handleSelect, handleChange },
  ] = useRemindPicker();

  const attrs = useAttrs();
  watchEffect(() => {
    if (!attrs.value && pickerOptions?.[4]) {
      pickerOptions?.splice(4, 1);
    }
    if (attrs.value && !pickerOptions.find((item) => item.value === attrs.value)) {
      pickerOptions[4] = {
        value: attrs.value as string,
        label: getTipStringText(attrs.value as string),
      };
    }
  });
</script>
<style lang="less" scoped>
  .otd-remind-picker {
    display: flex;
    align-items: center;
    column-gap: 2px;
    line-height: 1;
    .otd-noborder-select {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
