export enum TagAuthEnum {
  Public = 0,
  Private = 1,
}
export enum TagTypeEnum {
  TaskItem = 0,
}
export type TagDto = {
  id?: string;
  creationTime?: string;
  creatorId?: string | undefined;
  /** 标签名字 */
  tagName?: string | undefined;
  /** 颜色 */
  color?: string | undefined;
  tagAuth?: TagAuthEnum;
  tagType?: TagTypeEnum;
};

export interface ICreateTagInput {
  /** 标签名字 */
  tagName: string;
  /** 颜色 */
  color: string;
  tagAuth?: TagAuthEnum;
  tagType?: TagTypeEnum;
}

/**
 * hex转rgb
 * @param {string} str  色值，如：#409EFF
 * @returns rgb数组[64, 158, 255]
 */
export function hexToRgb(str) {
  let hexs: number[] = [];
  let reg = /^\#?[0-9A-Fa-f]{6}$/;
  if (!reg.test(str)) return alert('色值不正确');
  str = str.replace('#', ''); // 去掉#
  hexs = str.match(/../g); // 切割成数组 409EFF => ['40','9E','FF']
  // 将切割的色值转换为16进制
  for (let i = 0; i < hexs.length; i++) hexs[i] = parseInt(hexs[i].toString(), 16);
  return hexs; // 返回rgb色值[64, 158, 255]
}

/**
 * 颜色字符串转16进制
 * @param str
 * @returns
 */
export function colorNameToHex(str) {
  let reg = /^\#?[0-9A-Fa-f]{6}$/;
  if (reg.test(str)) return str;
  let ctx = document.createElement('canvas').getContext('2d');
  ctx!.fillStyle = str;
  const color = ctx!.fillStyle;
  ctx = null;
  return color;
}

/**
 * 颜色加深
 * @param {string} color  色值，如：##409EFF
 * @param {number} level 调整幅度，0~1之间
 * @returns 最终颜色加深的rgb数组
 */
export function getDarkColor(color, level): string {
  let reg = /^\#?[0-9A-Fa-f]{6}$/;
  if (!reg.test(color)) {
    color = colorNameToHex(color);
  }
  let rgb = hexToRgb(color);
  for (let i = 0; i < 3; i++) {
    rgb[i] = Math.floor(rgb[i] - rgb[i] * level); // 始终保持在0-255之间
  }
  return rgbToHex(rgb); // [32, 79, 127]
}
/**
 * 颜色减淡
 * @param {string} color  色值，如：##409EFF
 * @param {number} level 调整幅度，0~1之间
 * @returns {array} 最终颜色减淡的rgb数组
 */
export const getLightColor = (color, level): string => {
  let reg = /^\#?[0-9A-Fa-f]{6}$/;
  if (!reg.test(color)) {
    color = colorNameToHex(color);
  }
  let rgb = hexToRgb(color) ?? ([] as number[]);
  // 循环对色值进行调整
  for (let i = 0; i < 3; i++) {
    rgb[i] = Math.floor((255 - rgb[i]) * level + rgb[i]); // 始终保持在0-255之间
  }
  return rgbToHex(rgb); // [159, 206, 255]
};

/**
 * rgb转hex
 * @param {number} r 红色色值，如：64
 * @param {number} g 绿色色值，如：158
 * @param {number} b 蓝色色值，如：255
 * @returns 最终rgb转hex的值，如：64,158,255 -> #409EFF
 */
export function rgbToHex(arr) {
  let r = arr[0];
  let g = arr[1];
  let b = arr[2];
  let reg = /^\d{1,3}$/; // 限定1-3位 -> 0~255
  if (!reg.test(r) || !reg.test(g) || !reg.test(b)) {
    throw new Error('Color Error');
  }
  let hex = [r.toString(16), g.toString(16), b.toString(16)];
  // 转换的值如果只有一位则补0
  for (let i = 0; i < 3; i++) {
    if (hex[i].length === 1) hex[i] = `0${hex[i]}`;
  }
  return `#${hex.join('')}`; // #409eff
}
