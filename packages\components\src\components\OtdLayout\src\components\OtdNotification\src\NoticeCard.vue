<template>
  <div class="otd-card-list__item">
    <div class="otd-card-list__item-title">
      <div class="otd-card-list__item-title__content">
        <h2 class="otd-card-list__item-title__text otd-truncate" :title="item.title">
          {{ item.title }}
        </h2>
        <span class="otd-unread" v-if="!item.read"></span>
      </div>
      <OtdMoreAction
        :data="item"
        :index="index"
        :list="list"
        :actions="moreAction"
        :expand-number="1"
        action-type="icon"
        hide-expand-name
      />
    </div>
    <div class="otd-card-list__item-content" :title="removeHtmlTag(item.content)" v-html="item.content"></div>
    <span class="otd-card-list__item-date">{{ formatToDateTime(item.creationTime) }}</span>
  </div>
</template>
<script lang="ts" setup>
  import { PropType, unref } from 'vue';
  import { PagingNotificationListOutput } from './data';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { removeHtmlTag } from '/@/utils/tool';
  import { MoreActionItem, OtdMoreAction } from '/@/components/OtdMoreAction';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useNotifyStorage } from '/@/storage/notifyStorage';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
  import { MessageKeyMap } from '/@/hooks/web/useSignalR';

  const props = defineProps({
    item: {
      type: Object as PropType<PagingNotificationListOutput>,
      default: () => ({}),
    },
    index: {
      type: Number,
    },
    list: {
      type: Array as PropType<PagingNotificationListOutput[]>,
      default: () => [],
    },
  });

  const { t } = useI18n();
  const useNotify = useNotifyStorage();
  const { getGlobalProvide } = useGlobalConfig();
  const { setReadNotify } = getGlobalProvide();

  const moreAction: MoreActionItem[] = [
    {
      id: 1,
      name: t('common.markRead'),
      icon: 'otd-icon-mark',
      isHide(data) {
        return data.read;
      },
      action: async (data) => {
        if (!setReadNotify) return;
        await setReadNotify(data.id);
        data.read = true;
        const key = MessageKeyMap[props.item.messageType!];
        useNotify.setNotifyCount(key, unref(useNotify.getNotifyCount)[key]! - 1);
      },
    },
  ];
</script>
<style lang="less" scoped>
  :deep(.otd-more-action) {
    .add-icon {
      .otdIconfont {
        font-size: 20px;
      }
    }
  }
</style>
