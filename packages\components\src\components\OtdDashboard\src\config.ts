import { EChartsOption } from 'echarts';

export const DashboardColor = [
  '#4E83FD',
  '#9A81FD',
  '#D881FD',
  '#FD81E4',
  '#FD81A7',
  '#FD9A81',
  '#FDD881',
  '#E4FD81',
  '#A7FD81',
  '#FCC545',
  '#FBB309',
  '#457CFC',
  '#81E4FD',
  '#81FDD8',
  '#81FD9A',
];

export const DashboardConfig = (): EChartsOption => ({
  backgroundColor: 'transparent',
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: {
    textStyle: {
      color: '#6E7079',
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  dataZoom: [
    {
      type: 'inside',
    },
  ],
  color: DashboardColor,
  xAxis: [
    {
      type: 'category',
      data: [],
    },
  ],
  yAxis: [
    {
      minInterval: 1,
      type: 'value',
    },
  ],
  series: [],
});
