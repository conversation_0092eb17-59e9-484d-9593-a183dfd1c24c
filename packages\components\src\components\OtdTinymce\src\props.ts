import { RawEditorSettings } from 'tinymce';
import { PropType } from 'vue';
import { plugins, toolbar } from './tinymce';

export const getProps = (simple = false) => ({
  modelValue: {
    type: String,
  },
  options: {
    type: Object as PropType<Partial<RawEditorSettings>>,
    default: {},
  },
  toolbar: {
    type: Array as PropType<string[]>,
    default: toolbar,
  },
  plugins: {
    type: Array as PropType<string[]>,
    default: simple ? [] : plugins,
  },
  height: {
    type: [Number, String] as PropType<string | number>,
    required: false,
    default: 400,
  },
  bottomMargin: {
    type: Number,
  },
  maxHeight: {
    type: Number,
    required: false,
  },
  minHeight: {
    type: Number,
    required: false,
  },
  width: {
    type: [Number, String] as PropType<string | number>,
    required: false,
    default: 'auto',
  },
  placeholder: {
    type: String,
  },
  isReverse: {
    type: Boolean,
    default: false,
  },
  isSimple: {
    type: Boolean,
    default: false,
  },
  bordered: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
