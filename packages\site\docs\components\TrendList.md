## 趋势列表

<demo src="../demo/TrendList/basic.vue" title="趋势列表"></demo>

## 属性

| 参数      | 说明         | 类型                                                      | 可选值 | 默认值  | 版本 |
| --------- | ------------ | --------------------------------------------------------- | ------ | ------- | ---- |
| data      | 数据         | Object[]                                                  | --     | {}      | 1.0  |
| fields    | 字段映射     | \{key:string; label:string; value:string; status:string\} | --     | --      | 1.0  |
| height    | 高度         | Number                                                    | --     | --      | 1.0  |
| maxHeight | 最大高度     | Number                                                    | --     | --      | 1.0  |
| percent   | 是否是百分比 | Boolean                                                   | --     | `false` | 1.0  |

## 事件

| 名称       | 说明   | 回调参数       | 版本 |
| ---------- | ------ | -------------- | ---- |
| click-item | 点击项 | (data) => void | 1.0  |

## 插槽

| 参数  | 说明     | 类型 | 版本 |
| ----- | -------- | ---- | ---- |
| title | 标题插槽 | Slot | 1.0  |
