<script setup lang="ts"></script>

<template>
    <OtdDatePicker
        v-model:value="taskDetail.taskDates"
        v-model:remind="taskDetail.remind"
        v-model:repeat="taskDetail.repeat"
        :not-tip-color="{
            start: taskDetail.status !== TaskStatusEnum.UnStarted,
            end: taskDetail.status === TaskStatusEnum.Close,
        }"
        :disabled="Permission.planSpanStart(taskDetail)"
        :placeholder="getTaskDateLabel()"
        @change="() => updateTaskDates()"
        />

</template>

<style scoped></style>

 import {OtdDatePicker} from '~/packages/components/src/index.vue';
