import { ExtractPropTypes } from 'vue';
import { getProps } from './props';
import { TaskStatusEnum } from '/@/components/OtdStatus';

export type TaskOptionItemType = {
  label: string;
  value: string;
  status: TaskStatusEnum;
  user: {
    id: string;
    avatar: string;
    name: string;
  };
  source: {
    relatedName: string;
    relatedType: string;
    subRelatedType: string;
  };
};

export type TaskSearchPropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
