import { ExtractPropTypes } from 'vue';
import { getEmits, getProps } from './props';
import { EmitType } from '/#/global';

export type SearchOptionType = {
  label?: string;
  value?: string;
  [K: string]: any;
};

export type SearchQueryType = {
  keyword: string;
  data: SearchOptionType[];
  fetching: boolean;
};

export type SearchPopoverPropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
export type SearchPopoverEmitType = EmitType<ReturnType<typeof getEmits>[number]>;
