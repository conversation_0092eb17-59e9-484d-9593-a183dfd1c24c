import type { LocaleSetting } from '/#/config';
import type { Nullable } from '/#/global';
import { computed, ref, unref } from 'vue';
import { LocaleEnum } from '/@/enums/appEnum';
import { LOCALE_KEY } from '/@/enums/cacheEnum';
import { localeSetting } from '/@/settings';
import { createLocalStorage } from '/@/utils/cache';

const ls = createLocalStorage();
const lsLocaleSetting = (): LocaleSetting => ls.get(LOCALE_KEY) || localeSetting;
const localeInfo = ref<Nullable<LocaleSetting>>(lsLocaleSetting());

export function useLocaleStorage() {
  return {
    getLocale: computed(() => unref(localeInfo)?.locale ?? LocaleEnum.ZH_CN),
    getShowPicker(): boolean {
      return !!unref(localeInfo)?.showPicker;
    },
    setLocaleInfo(info: Partial<LocaleSetting>) {
      localeInfo.value = { ...unref(localeInfo), ...(info as LocaleSetting) };
      ls.set(LOCALE_KEY, unref(localeInfo));
    },
    initLocale() {
      this.setLocaleInfo({
        ...localeSetting,
        ...lsLocaleSetting(),
      });
    },
  };
}
