<template>
  <Dropdown
    v-model:open="visible"
    overlay-class-name="otd-dropdown"
    :trigger="['click']"
    :disabled="disabled"
    placement="bottomLeft"
  >
    <div
      class="otd-data-bubble"
      :class="{ 'placeholder-disabled': disabled, 'otd-action-clearable__label': clearable }"
      data-bubble
    >
      <div class="otd-priority placeholder-hover">
        <Tooltip :title="tipTitle">
          <PriorityTrigger
            :value="currentTag"
            :placeholder="placeholder"
            :default-value="defaultValue"
            :options="getOption"
            :is-simple="isSimple"
          />
        </Tooltip>
        <Tooltip :title="t('common.delText')" v-if="currentTagMap && !disabled && clearable">
          <CloseCircleFilled class="otd-action-clearable" @click.stop="handleChange({ key: null })" />
        </Tooltip>
      </div>
    </div>
    <template #overlay>
      <Menu @click="handleChange">
        <div class="px-6px pt-2px pb-6px">
          <MenuItem class="otd-priority-item" v-for="item in getOption" :key="item.value">
            <i class="otdIconfont otd-icon-qizhi" :style="{ color: item.color }"></i>
            <span class="otd-priority-item__text">{{ item.label }}</span>
          </MenuItem>
        </div>
        <MenuDivider />
        <div class="px-6px pt-6px pb-2px">
          <MenuItem class="otd-priority-item">
            <i class="iconfont icon-qingchu"></i>
            <span class="otd-priority-item__text">{{ t('common.clearText') }}</span>
          </MenuItem>
        </div>
      </Menu>
    </template>
  </Dropdown>
</template>
<script lang="ts" setup>
  import { ref, computed, watch, unref } from 'vue';
  import { Dropdown, Menu, MenuItem, MenuDivider, Tooltip } from 'ant-design-vue';
  import { CloseCircleFilled } from '@ant-design/icons-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getEmits, getProps } from './props';
  import { ColorOptionType } from '../../OtdColor';
  import { usePriority } from './usePriority';
  import PriorityTrigger from './PriorityTrigger.vue';

  const props = defineProps(getProps());
  const emit = defineEmits(getEmits());
  const { t } = useI18n();

  const currentTag = ref<string | number>();
  const visible = ref(false);
  const { PriorityDictionary } = usePriority();
  const getOption = computed(() => props.options ?? PriorityDictionary);

  // 当前优先级
  const currentTagMap = computed<ColorOptionType | undefined>(() => {
    const data = unref(getOption).find((item) => item.value === currentTag.value);
    return data ?? props?.defaultValue ?? undefined;
  });

  // 提示内容
  const tipTitle = computed(() => {
    if (!props.isSimple && !unref(currentTagMap)) return undefined;
    return [props.tipLabel, unref(currentTagMap)?.label].filter(Boolean).join(' : ');
  });

  // Tag改变事件
  function handleChange({ key }) {
    if (currentTag.value === key) return false;
    currentTag.value = key;
    emit('update:value', unref(currentTag));
    if (key === null) {
      emit('clear', undefined);
    } else {
      emit('change', key, unref(currentTagMap));
    }
    visible.value = false;
  }

  watch(
    () => props.value,
    (value) => {
      currentTag.value = value ?? props?.defaultValue?.value ?? null;
    },
    { immediate: true },
  );
</script>
<style lang="less" scoped>
  .otd-dropdown .ant-dropdown-menu {
    width: 200px;
  }
  .otd-priority {
    color: var(--otd-gray4-color);
    cursor: pointer;
    border-radius: 4px;
    border: 1px solid transparent;
    text-align: center;

    &.is-simple {
    }
  }
  :deep(.otd-priority-item) {
    .ant-dropdown-menu-title-content {
      display: flex;
      align-items: center;
    }
    &__text {
      display: inline-flex;
      margin-left: 8px;
      width: 100px;
    }
  }
</style>
