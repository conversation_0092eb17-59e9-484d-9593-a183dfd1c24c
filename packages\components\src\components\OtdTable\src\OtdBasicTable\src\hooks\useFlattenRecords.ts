import type { GetRowK<PERSON>, Key } from 'ant-design-vue/es/vc-table/interface';
import type { Ref } from 'vue';
import { computed, unref, useSlots } from 'vue';
import { BasicTableFilterType } from '../type';

type RecordItemType<T> = { record: T; indent: number; index: number; isFooter?: boolean };
type flatRecordParamsType<T> = {
  record: T;
  indent: number;
  childrenColumnName: string;
  expandedKeys: Set<Key>;
  getRowKey: GetRowKey<T>;
  index: number;
  filter: { hasExpendFooter: boolean; otherFilter?: BasicTableFilterType };
};

// recursion (flat tree structure)
function flatRecord<T>(params: flatRecordParamsType<T>) {
  const { record, indent, childrenColumnName, expandedKeys, getRowKey, index, filter } = params;
  const { hasExpendFooter, otherFilter = {} } = filter;
  const result: RecordItemType<T>[] = [];
  result.push({ record, indent, index });
  const key = getRowKey(record);
  const expanded = expandedKeys?.has(key);

  if (record && Array.isArray(record[childrenColumnName]) && expanded) {
    const { length } = record[childrenColumnName];
    // expanded state, flat record
    for (let i = 0; i < length; i += 1) {
      const data = record[childrenColumnName][i];
      data['__parent_data'] = record;
      const tempArr = flatRecord<T>({
        record: data,
        indent: indent + 1,
        childrenColumnName,
        expandedKeys,
        getRowKey,
        index: i,
        filter,
      });

      const isTrue = Object.keys(otherFilter).every((key) => {
        const { indent: ind = 0, handler } = otherFilter[key];
        return ind <= indent ? handler(data) : true;
      });
      if (isTrue) {
        result.push(...tempArr);
      }
    }
    if (hasExpendFooter) {
      result.push({ record, indent, index, isFooter: true });
    }
  }

  return result;
}

/**
 * flat tree data on expanded state
 *
 * @export
 * @template T
 * @param {*} data : table data
 * @param {string} childrenColumnName : 指定树形结构的列名
 * @param {Set<Key>} expandedKeys : 展开的行对应的keys
 * @param {GetRowKey<T>} getRowKey  : 获取当前rowKey的方法
 * @param {Ref<BasicTableFilterType | undefined>} filter  : 过滤器对象
 * @returns flattened data
 */
export default function useFlattenRecords<T = unknown>(
  dataRef: Ref<T[]>,
  childrenColumnNameRef: Ref<string>,
  expandedKeysRef: Ref<Set<Key>>,
  getRowKey: Ref<GetRowKey<T>>,
  filter: Ref<BasicTableFilterType | undefined>,
) {
  const slot = useSlots();
  const hasExpendFooter = !!slot['expend-footer'];

  const result: Ref<RecordItemType<T>[]> = computed(() => {
    const childrenColumnName = childrenColumnNameRef.value;
    const expandedKeys = expandedKeysRef.value;
    const data = dataRef.value;
    if (expandedKeys?.size) {
      const temp: RecordItemType<T>[] = [];
      // collect flattened record
      for (let i = 0; i < data?.length; i += 1) {
        const record = data[i];
        temp.push(
          ...flatRecord<T>({
            record,
            indent: 0,
            childrenColumnName,
            expandedKeys,
            getRowKey: unref(getRowKey),
            index: i,
            filter: { hasExpendFooter, otherFilter: unref(filter) },
          }),
        );
      }
      return temp;
    }

    return data?.map((item, index) => {
      return {
        record: item,
        indent: 0,
        index,
      };
    });
  });

  return result;
}
