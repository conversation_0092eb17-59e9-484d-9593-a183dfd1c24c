import { PropType } from 'vue';
import { ITaskHourDetailDto, TrackTimeDataType } from './type';
import { mutable } from '/@/tool';
import { LabelValueOptions } from '/#/global';

export enum HourTypeEnum {
  Plan = 0,
  Real = 1,
}

export const getProps = () => ({
  data: {
    type: Object as PropType<TrackTimeDataType>,
    default: () => ({}),
  },
  loadDetail: {
    type: Function as PropType<() => Promise<ITaskHourDetailDto>>,
    default: undefined,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  defaultDate: {
    type: Array as unknown as PropType<[string | undefined, string | undefined]>,
    default: undefined,
  },
  categoryOptions: {
    type: Array as PropType<LabelValueOptions>,
  },
  defaultCategory: {
    type: String,
  },
});

const emit = ['register', 'submit', 'delete', 'delete-detail', 'reload'] as const;
export const getEmits = () => mutable(emit);
