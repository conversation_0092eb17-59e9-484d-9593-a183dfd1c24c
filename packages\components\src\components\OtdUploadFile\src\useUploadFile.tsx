import type { MoreActionItem } from '/@/components/OtdMoreAction';
import type { UploadFile } from 'ant-design-vue';
import type { TableColumnPropsType } from '/@/components/OtdTable';
import type { UploadFileEmitType, UploadFilePropsType } from '/@/components/OtdUploadFile/src/type';
import { Progress } from 'ant-design-vue';
import { useFileType } from './useFileType';
import { ref, getCurrentInstance, computed } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { downloadByUrl } from '/@/utils/file/download';
import { formatBytes } from '/@/utils/tool';
import { OtdMoreAction } from '/@/components/OtdMoreAction';
import { useUploadAttachment } from './components/UseUploadAttachment';
import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
import { formatToTime } from '/@/tool';
import { ERROR_COLOR } from '/@/setting';

export function useFileFormat() {
  const { getGlobalProvide } = useGlobalConfig();
  const { getUserInfo } = getGlobalProvide();
  function convertAttachmentFormat(data) {
    return {
      uid: (data.fileId || data.id || data.uid) as string,
      name: (data.fileOriginalName || data.name || '') + (data.extension || ''),
      size: data.fileSize || data.size,
      fileName: data.fileOriginalName,
      type: data.type?.toString(),
      url: data.downloadUrl || data.fileUrl,
      preview: data.imagePreviewUrl,
      creationTime: data.creationTime,
      creatorName: data.creatorName ?? getUserInfo!.realName,
    };
  }
  return {
    convertAttachmentFormat,
  };
}

export function useUploadFile() {
  const { emit, props } = getCurrentInstance() as unknown as {
    emit: UploadFileEmitType;
    props: UploadFilePropsType;
    attrs: any;
  };
  const { t } = useI18n();
  const { handleRemoveFile, cancelUpload } = useUploadAttachment();
  const { fileTypeToIcon } = useFileType();

  // 文件列内容
  const fileGroup = ref<UploadFile[]>([]);
  const tableFileList = computed({
    get: () => props.fileList,
    set: (value) => emit('update:fileList', value),
  });
  const operationActions: MoreActionItem[] = [
    // 下载
    {
      id: 'download',
      icon: 'otd-icon-a-Property1Default-1',
      name: t('common.download'),
      isHide: ({ record }) => !record.creationTime,
      action: ({ record }) => {
        downloadByUrl({
          url: record.url,
          fileName: record.name,
        });
      },
    },
    // 删除
    {
      id: 'delete',
      icon: 'otd-icon-a-catdeletesize24',
      color: ERROR_COLOR,
      name: t('common.delText'),
      isHide: ({ record }) => !record.creationTime,
      action: async ({ record }) => {
        handleRemoveFile(record).then((res) => {
          // @ts-ignore
          emit('update:fileList', res);
        });
      },
    },
    // 预览
    {
      id: 'preview',
      icon: 'otd-icon-view',
      name: t('common.upload.preview'),
      isHide: ({ record }) => !(record.creationTime && props.previewFile),
      action: ({ record }) => {
        props.previewFile!(record);
      },
    },
    //     取消上传
    {
      id: 'cancel-upload',
      icon: 'otd-icon-a-catthicksize24',
      name: t('common.cancelText'),
      isHide: ({ record }) => record.creationTime,
      action: ({ record, index }) => {
        cancelUpload(record, index);
      },
    },
  ];

  const columns: TableColumnPropsType[] = [
    //   文件名称
    {
      title: t('common.uploadFile.fileName'),
      key: 'name',
      ellipsis: true,
      customRender({ record }) {
        return (
          <div class="otd-box-left" title={record.name}>
            <img style="height: 22px;" src={fileTypeToIcon(record.name)} alt="" />
            <span class="otd-truncate" style="margin-left: 6px">
              {record.name}
            </span>
          </div>
        );
      },
    },
    //   文件大小
    {
      title: t('common.uploadFile.fileSize'),
      key: 'size',
      customRender({ record }) {
        return <div>{formatBytes(record.size)}</div>;
      },
    },
    //   上传时间
    {
      title: t('common.uploadFile.uploadTime'),
      key: 'creationTime',
      customRender({ record }) {
        return (
          <>
            {record.creationTime || record.percent >= 100 ? (
              <div>{record.creationTime ? formatToTime(record.creationTime) : undefined}</div>
            ) : (
              <Progress percent={record.percent} size="small" />
            )}
          </>
        );
      },
    },
    //   上传用户
    {
      title: t('common.uploadFile.uploadUser'),
      key: 'creatorName',
      customRender({ record, index }) {
        return (
          <div className="otd-table-action">
            <span>{record.creatorName}</span>
            <OtdMoreAction
              data={{ record, index }}
              list={tableFileList.value}
              size="large"
              hide-expand-name
              destroy-popup-on-hide
              expand-number={3}
              actions={operationActions}
            />
          </div>
        );
      },
    },
  ];

  return {
    fileGroup,
    columns,
    tableFileList,
  };
}
