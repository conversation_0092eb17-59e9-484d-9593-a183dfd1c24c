<template>
  <Tooltip
    :overlayClassName="`${prefixCls}__wrap`"
    autoAdjustOverflow
    :overlayStyle="getOverlayStyle"
    :placement="placement"
    :getPopupContainer="() => getPopupContainer()"
  >
    <template #title>
      <div :style="getTooltipStyle">
        <RenderTitle />
      </div>
    </template>
    <span :class="prefixCls">
      <slot><InfoCircleOutlined /></slot>
    </span>
  </Tooltip>
</template>
<script lang="tsx" setup>
import type { CSSProperties, PropType } from "vue";
import { computed } from "vue";
import { Tooltip } from "ant-design-vue";
import type { TooltipPlacement } from "ant-design-vue/es/tooltip";
import { InfoCircleOutlined } from "@ant-design/icons-vue";
import { getPopupContainer } from "/@/utils";
import { isString, isArray } from "/@/utils/is";
import { useDesign } from "/@/hooks/web/useDesign";

const props = defineProps({
  /**
   * Help text max-width
   * @default: 600px
   */
  maxWidth: { type: String, default: "600px" },
  /**
   * Whether to display the serial number
   * @default: false
   */
  showIndex: { type: Boolean },
  /**
   * Help text font color
   * @default: #ffffff
   */
  color: { type: String, default: "#ffffff" },
  /**
   * Help text font size
   * @default: 14px
   */
  fontSize: { type: String, default: "14px" },
  /**
   * Help text list
   */
  placement: { type: String as PropType<TooltipPlacement>, default: "right" },
  /**
   * Help text list
   */
  text: { type: [Array, String] as PropType<string[] | string> }
});

const { prefixCls } = useDesign("basic-help");

const getTooltipStyle = computed((): CSSProperties => ({ color: props.color, fontSize: props.fontSize }));

const getOverlayStyle = computed((): CSSProperties => ({ maxWidth: props.maxWidth }));

function RenderTitle() {
  const textList = props.text;

  if (isString(textList)) {
    return <p v-html={textList}></p>;
  }

  if (isArray(textList)) {
    return textList.map((text, index) => {
      return (
        <p key={text}>
          <>
            {props.showIndex ? `${index + 1}. ` : ""}
            {text}
          </>
        </p>
      );
    });
  }
  return null;
}
</script>

<style lang="less" scoped>
@prefix-cls: ~"@{namespace}-basic-help";

.@{prefix-cls} {
  display: inline-block;
  margin-left: 6px;
  font-size: 14px;
  color: var(--otd-help-color);
  cursor: pointer;

  &:hover {
    color: var(--otd-primary-text);
  }

  &__wrap {
    p {
      margin-bottom: 0;
    }
  }
}
</style>
