<template>
  <component
    :is="getComponent()"
    class="otd-history-filter-item"
    :class="{ 'is-edit': data.isEdit }"
    :value="data[fieldNames.value]"
    :title="data[fieldNames.label]"
  >
    <div class="otd-history-filter-item__body otd-layout-action" v-if="!data.isEdit">
      <i class="otdIconfont" :class="[data.icon]"></i>
      <span class="otd-title-text otd-truncate" v-if="!isClosed">
        {{ data[fieldNames.label] }}
      </span>
      <i v-if="data.isArchived" class="otdIconfont icon-quxiaoguidang hideIsArchived" />
    </div>
    <div v-else>
      <Input
        class="otd-input"
        v-model:value="itemName"
        ref="inputRef"
        @blur="handleInputBlur(itemName, data)"
        @press-enter="($event.target as HTMLInputElement).blur()"
      />
    </div>
  </component>
</template>
<script lang="ts" setup>
  import { ref, watch, nextTick } from 'vue';
  import { Radio, Input, Checkbox } from 'ant-design-vue';

  const RadioButton = Radio.Button;
  const props = defineProps({
    data: {
      type: Object,
      default: () => ({}),
    },

    isClosed: {
      type: Boolean,
      default: false,
    },
    isCheck: {
      type: Boolean,
      default: false,
    },
    fieldNames: {
      type: Object,
      default: () => ({ label: 'title', value: 'type' }),
    },
  });

  const emit = defineEmits(['enter']);

  const itemName = ref('');

  function handleInputBlur(value, data) {
    data.isEdit = false;
    emit('enter', value, data);
  }

  const inputRef = ref();
  watch(
    () => props.data.isEdit,
    (value) => {
      if (value) {
        itemName.value = props.data.title;
        nextTick(() => {
          inputRef.value.focus();
        });
      }
    },
  );

  function getComponent() {
    return props.isCheck ? Checkbox : RadioButton;
  }
</script>
<style lang="less" scoped>
  .otd-history-filter-item {
    width: 100%;
    padding: 6px 8px;
    height: auto;
    line-height: 1;
    border: 0 !important;
    border-radius: 8px !important;
    user-select: none;
    line-height: 22px;
    position: relative;
    display: flex;
    align-items: center;
    &__body {
      display: flex;
      align-items: center;
      padding: 0;
      .otd-title-text {
        font-weight: normal;
        flex: 1;
        font-size: 12px;
      }
    }
    &.hideIsArchived {
      display: inline-flex;
    }
    &.is-edit {
      padding: 3px 8px;
    }
    &:hover {
      background-color: var(--otd-gray-hover);
      :deep(.hide-action) {
        display: inline-flex;
      }
      .hideIsArchived {
        display: none;
      }
    }
    :deep(.hide-action) {
      display: none;
    }
    &.ant-radio-button-wrapper {
      &::before {
        content: unset;
      }
      &:focus-within {
        box-shadow: unset;
      }
    }
    &.ant-radio-button-wrapper-checked {
      background-color: var(--otd-primary-color);
      color: var(--otd-primary-color) !important;
    }
    &.ant-checkbox-wrapper {
      padding-right: 30px;
      padding-left: 0;
      position: relative;
      &.ant-checkbox-wrapper-checked {
        &::before {
          position: absolute;
          right: 8px;
          font-size: 14px;
          content: '\e615';
          font-family: otdIconfont;
        }
      }
      :deep(.ant-checkbox) {
        display: none;
      }
    }
    & + .task-item {
      margin-top: 4px;
      margin-left: 0;
    }
    .otdIconfont {
      margin-right: 6px;
      font-size: 18px;
    }
  }
</style>
