export default {
  okText: '确认',
  closeText: '关闭',
  clearText: '清除',
  more: '更多',
  to: '至',
  from: '从',
  cancelText: '取消',
  none: '空',
  inputText: '请输入',
  chooseText: '请选择',
  queryText: '查询',
  delText: '删除',
  deledText: '删除',
  handoverText: '交接',
  editText: '编辑',
  editedText: '编辑',
  image: '图片',
  download: '下载',
  saveText: '保存',
  addDate: '添加日期',
  addTime: '添加时间',
  resetText: '重置',
  searchText: '搜索',
  loadingText: '加载中...',
  loadErrorText: '加载失败，点击重试',
  noMoreDataText: '没有更多',
  openText: '打开',
  viewText: '查看',
  reply: '回复',
  redo: '刷新',
  send: '发送',
  color: '颜色',
  comment: '评论',
  addContent: '添加{content}',
  emoticon: '表情',
  back: '返回',
  account: '账号名',
  email: '邮箱',
  phone: '手机号',
  information: '消息',
  notify: '通知',
  markRead: '标记为已读',
  readAll: '全部已读',
  operationSuccess: '操作成功',
  operationFail: '操作失败',
  personCount: '{count}名{text}',
  add: '添加',
  added: '添加',
  tip: '提示',
  askDelete: '确认删除吗',
  askCancel: '确认取消吗',
  askEdit: '确认修改吗',
  name: '名称',
  title: '标题',
  description: '描述',
  create: '创建',
  removeAttachmentTip: '确定移除附件"{name}"？',
  archive: '归档',
  cancelArchive: '取消归档',
  groupTitle: '分组标题',
  deleteListTip: '删除清单“{name}”？',
  archiveTip: '归档清单"{name}"?',
  archiveTipContent: '归档不会改变任务状态，已归档的清单可在折叠列表查看',
  user: '用户',
  task: '任务',
  project: '项目',
  time: '时间',
  hours: '小时',
  taskList: '任务列表',
  verificationDepartment: '确认人部门',
  detail: '详情',
  predecessorTask: '前置任务',
  entry: '录入',
  autoCompletion: '自动完成',
  resDepartment: '负责人部门',
  taskTemplate: '任务模板',
  pin: '固定',
  teamworkChecklist: '团队协作清单',
  archivedInventory: '已归档清单',
  subtask: {
    subtask: '子任务',
    syncEndTime: '同步截止时间',
    viewDetails: '查看详情',
    taskName: '任务名称',
    resPerson: '负责人',
    verifier: '确认人',
    watcher: '关注人',
    creator: '创建人',
    startTime: '开始时间',
    endTime: '截止时间',
    addSubtask: '添加子任务',
    createSubtaskTip: '通过任务模板添加子任务',
    AIcreateSubtaskTip: 'AI任务分解',
    aiRebuild: '重新生成',
  },
  action: {
    start: '开始',
    stop: '暂停',
    complete: '完成',
    reject: '退回',
    confirm: '确认',
    cancel: '取消',
  },
  status: {
    status: '状态',
    notStarted: '未开始',
    verifying: '待确认',
    onHold: '已暂停',
    ongoing: '进行中',
    completed: '已完成',
    rejected: '已退回',
    cancelled: '已取消',
  },
  is: '是',
  filter: '筛选',
  value: '值',
  verify: '请填写',
  countdown: {
    normalText: '获取验证码',
    sendText: '{0}秒后重新获取',
  },
  form: {
    putAway: '收起',
    unfold: '展开',
    maxTip: '字符数应小于{0}位',
    apiSelectNotFound: '请等待数据加载完成...',
  },
  modal: {
    cancelText: '取消',
    okText: '确认',
    close: '关闭',
    maximize: '最大化',
    restore: '还原',
  },
  table: {
    settingDens: '密度',
    settingDensDefault: '默认',
    settingDensMiddle: '中等',
    settingDensSmall: '紧凑',
    settingColumn: '列设置',
    settingColumnShow: '列展示',
    settingIndexColumnShow: '序号列',
    settingSelectColumnShow: '勾选列',
    settingFixedLeft: '固定到左侧',
    settingFixedRight: '固定到右侧',
    settingFullScreen: '全屏',
    index: '序号',
    total: '共 {total} 条数据',
  },
  upload: {
    save: '保存',
    upload: '上传',
    imgUpload: '图片上传',
    uploaded: '已上传',

    operating: '操作',
    del: '删除',
    download: '下载',
    saveWarn: '请等待文件上传后，保存!',
    saveError: '没有上传成功的文件，无法保存!',

    preview: '预览',
    choose: '选择文件',

    accept: '支持{0}格式',
    acceptUpload: '只能上传{0}格式文件',
    maxSize: '单个文件不超过{0}MB',
    maxSizeMultiple: '只能上传不超过{0}MB的文件!',
    maxNumber: '最多只能上传{0}个文件',

    legend: '略缩图',
    fileName: '文件名',
    fileSize: '文件大小',
    fileStatue: '状态',

    startUpload: '开始上传',
    uploadSuccess: '上传成功',
    uploadError: '上传失败',
    uploading: '上传中',
    uploadWait: '请等待文件上传结束后操作',
    reUploadFailed: '重新上传失败文件',
    modalTitle: '头像上传',
    cutMessageWarning: '请选择正确的图片',
  },
  tag: {
    tag: '标签',
    delText: '删除',
    copy: '复制',
    rename: '重命名',
    renameTag: '修改标签名',
    changeColor: '修改颜色',
    public: '公共',
    private: '私有',
    all: '全部',
    tagCreateTip: '搜索或创建标签',
    create: '创建',
    copySuccessTip: '复制成功',
    setPublic: '设为公共',
    setPrivate: '设为私有',
  },
  priority: {
    priority: '优先级',
    Urgent: '紧急',
    High: '高',
    Normal: '中',
    Low: '低',
  },
  userSearch: {
    searchingText: '搜索中...',
    selected: '已选择',
    suggestion: '建议',
  },
  collaborationDocument: {
    collaborativeDoc: '协作文档',
    collaborativeName: '文档名称',
    collaborativeLink: '文档链接',
  },
  uploadFile: {
    attachment: '附件',
    addAttachment: '添加附件',
    attachmentCount: '{count} 个附件',
    fileName: '文件名称',
    fileSize: '文件大小',
    uploadTime: '上传时间',
    uploadUser: '上传用户',
  },
  folder: {
    moveTo: '移动到',
    askMoveTo: '确定移动到',
    newFolder: '新建文件夹',
    removeFolder: '删除文件夹',
    updateFolder: '更新文件夹',
  },
  board: {
    createTask: '创建任务',
    createGroup: '创建分组',
    deleteGroupTip: '删除分组后，分组内的任务将回到默认分组中',
  },
  ai: {
    summaryTask: '总结任务',
  },
  trackTime: {
    WorkHourCategory: '工时类别',
    splitWorkHour: '拆分工时',
    TotalDuration: '总时长',
    trackTime: '工时',
    plannedTrackingTime: '计划工时',
    actualTrackingTime: '实际工时',
    planned: '计划',
    actual: '实际',
    WorkHourExecutor: '工时执行者',
    ExecutionTime: '执行时间',
    WorkHours: '工时数',
    WorkHoursTotal: '总工时数',
    WorkProgress: '工作进度',
    WorkHourType: '工时类型',
    WorkHoursDetails: '工时详情',
    ProgressOverview: '整体进展',
    HoursRecord: '工时记录',
    total: '总数',
    everyDay: '每天',
  },
  seniorFilter: {
    clearAll: '清空',
    field: '字段',
    condition: '条件',
    addNewFilter: '新增新条件',
    advancedFilters: '高级筛选',
    saveTheFilterResults: '保存当前筛选条件',
    saveFilter: '保存筛选条件',
    editTheFilterResults: '编辑当前筛选条件',
    filterName: '筛选条件名称',
    savedFilters: '筛选列表',
    verifyFilterName: '{verify}筛选条件名称',
  },
  emoticons: {
    Done: '完成',
    FingerHeart: '比心',
    Like: '赞',
    Ok: 'OK',
    Flower: '花',
  },
  datePicker: {
    startDate: '开始日期',
    dueDate: '截止日期',
    Today: '今天',
    Yesterday: '昨天',
    Later: '今晚',
    Tomorrow: '明天',
    ThisWeekend: '本周末',
    NextWeek: '下周',
    NextWeekend: '下周末',
    ThisMonth: '本月',
    LastMonth: '上月',
    ThisYear: '今年',
    LastYeat: '去年',
    ThisWeek: '本周',
    LastWeek: '上周',
    '2Weeks': '两周',
    '4Weeks': '四周',
    DueReminder: '到期提醒',
    RepeatSetup: '重复设置',
    placeholder: '请选择日期',
  },
  history: {
    createTask: '创建任务',
    deleteTask: '删除任务',
    assignPeople: '分配人员',
    endTime: '截止时间',
    taskDescription: '任务描述',
    taskProgress: '任务进度',
    uploadAttachment: '上传附件',
    attachment: '附件',
    collaborativeList: '协作清单',
  },
  repeatPicker: {
    custom: '自定义重复',
    frequency: '重复频率',
    recurrenceRule: '重复规则',
    every: '每',
    day: '天',
    days: '天',
    weeks: '周',
    months: '个月',
    years: '年',
    byDay: '按天',
    byWeek: '按星期',
    Sunday: '周日',
    Monday: '周一',
    Tuesday: '周二',
    Wednesday: '周三',
    Thursday: '周四',
    Friday: '周五',
    Saturday: '周六',
    '1st': '第1个',
    '2nd': '第2个',
    '3rd': '第3个',
    numth: '第{n}个',
  },
  RemindPicker: {
    beforeDueDay: '截止前{n}{unit}',
    onTheDay: '当天 {time}',
    custom: '自定义提醒',
    reminderRule: '提醒规则',
  },
  SynchronizeAccounts: {
    userManagement_name: '姓名',
    userManagement_email: '邮箱',
    userManagement_phone: '手机号码',
    userManagement_department: '部门',
    userManagement_synced: '已完成',
    userManagement_binding: '绑定关系',
    userManagement_binding1: '将未绑定过',
    userManagement_binding2: '与',
    userManagement_binding3: '建立绑定关系',
    userManagement_binding4: '企业微信的组织用户',
    userManagement_binding5: '本次同步的企业微信用户',
    userManagement_inducts: '导入',
    userManagement_inductsSuccess: '导入成功,用户已同步',
    userManagement_nameMatching: '姓名匹配',
    userManagement_nameSynced: '已同步',
    userManagement_phoneClash: '手机号冲突',
    userManagement_phoneRepeat: '手机号重复',
    userManagement_emailRepeat: '邮箱重复',
    userManagement_emailClash: '邮箱冲突',
    userManagement_remove: '移除',
    userManagement_okRemover: '确定要移除吗',
    synchronize_accounts: '同步账号',
  },
  lineUp: {
    addTip: '在此添加你最重要的任务。',
    addTaskTip: '添加置顶',
    recent: '最近',
  },
  showClosed: {
    title: '显示已关闭',
    closed: '已关闭',
  },
  poster: {
    generating: '生成中...',
  },
  tour: {
    previous: '上一步',
    next: '下一步',
  },
  taskType: {
    title: '任务类型',
  },
};
