<script lang="tsx">
  import { PickerPanel } from 'ant-design-vue/es/vc-picker';
  import { useProvideRange } from 'ant-design-vue/es/vc-picker/RangeContext';
  import { useProvidePanel } from 'ant-design-vue/es/vc-picker/PanelContext';
  import dayjsGenerateConfig from 'ant-design-vue/lib/vc-picker/generate/dayjs';
  import { useLocaleReceiver } from 'ant-design-vue/es/locale-provider/LocaleReceiver';
  import { computed, defineComponent, PropType, ref, unref, VNode } from 'vue';
  import enUS from 'ant-design-vue/es/calendar/locale/en_US';
  import { Dayjs } from 'dayjs';
  import { NullableDateType } from 'ant-design-vue/es/vc-picker/interface';
  import { isUnDef, judgeDateBefore } from '/@/tool';
  import { omit } from 'lodash-es';

  export default defineComponent({
    props: {
      value: {
        type: [Object, Array] as PropType<Dayjs | [NullableDateType<Dayjs>, NullableDateType<Dayjs>] | null>,
      },
      isRange: {
        type: Boolean,
        default: false,
      },
      index: {
        type: Number as PropType<0 | 1>,
      },
    },
    emit: ['update:value', 'change'],
    setup(props, { attrs, slots, emit }) {
      const pre = ref('otd-picker');

      const [contextLocale] = useLocaleReceiver('DatePicker', enUS);
      function prevIcon(): VNode {
        return slots.prevIcon?.() || <span class={`${pre.value}-prev-icon`}></span>;
      }
      function nextIcon(): VNode {
        return slots.nextIcon?.() || <span class={`${pre.value}-next-icon`}></span>;
      }

      function superPrevIcon(): VNode {
        return slots.superPrevIcon?.() || <span class={`${pre.value}-super-prev-icon`}></span>;
      }
      function superNextIcon(): VNode {
        return slots.superNextIcon?.() || <span class={`${pre.value}-super-next-icon`}></span>;
      }

      const inRange = ref(false);
      const hoverRangedValue = ref([] as any);
      const panelPosition = ref<false | 'left' | 'right'>(false);
      const lastIndex = computed(() => props.index ?? 1);
      const dateContent = computed<NullableDateType<[NullableDateType<Dayjs>, NullableDateType<Dayjs>] | Dayjs>>({
        get: () => props.value,
        set: (value) => emit('update:value', value),
      });

      if (props.isRange) {
        useProvideRange({
          rangedValue: dateContent as any,
          hoverRangedValue,
          inRange,
          panelPosition,
        });
        useProvidePanel({
          onDateMouseenter(date) {
            const dates = unref(dateContent);
            if (
              (!dates![0] && !dates![1]) ||
              dates![0]?.isAfter(date) ||
              (dates![1] && props.index !== 0) ||
              dates![1]?.isBefore(date)
            )
              return (hoverRangedValue.value.length = 0);
            hoverRangedValue.value = judgeDateBefore([dates![0] ?? dates![1], date]);
          },
          onDateMouseleave() {
            if (!isUnDef(props.index)) return;
            const dates = unref(dateContent);
            hoverRangedValue.value = judgeDateBefore([dates![0] ?? dates![1], null]);
          },
        });
      }

      function setDateRange(date) {
        const index = unref(lastIndex);
        const [start, end] = unref(dateContent) as [NullableDateType<Dayjs>, NullableDateType<Dayjs>];
        let dates: [NullableDateType<Dayjs>, NullableDateType<Dayjs>] = [null, null];
        if (end && index === 0 && end.isBefore(date)) {
          dates = [date, null];
        } else if (start && index !== 0 && start.isAfter(date)) {
          dates = [date, date];
        } else {
          dates = unref(dateContent) as any;
          dates[unref(lastIndex)] = date;
        }
        return dates;
      }

      function handleSelect(date) {
        if (props.isRange) {
          const dates = setDateRange(date);
          hoverRangedValue.value[unref(lastIndex)] = date;
          dateContent.value = dates;
          emit('change', dates);
        } else {
          dateContent.value = date;
        }
      }

      const lightDate = computed(() => {
        const date = unref(dateContent);
        return date?.[unref(lastIndex)] ?? date?.[0] ?? null;
      });
      return () => (
        <div>
          <PickerPanel
            value={unref(lightDate)}
            prefixCls={unref(pre)}
            locale={unref(contextLocale)!.lang}
            generateConfig={dayjsGenerateConfig}
            {...omit(attrs, ['onChange', 'onUpdate:value'])}
            onSelect={handleSelect}
          >
            {{
              prevIcon: () => prevIcon(),
              nextIcon: () => nextIcon(),
              superPrevIcon: () => superPrevIcon(),
              superNextIcon: () => superNextIcon(),
            }}
          </PickerPanel>
        </div>
      );
    },
  });
</script>
<style lang="less" scoped>
  @import './style.less';
</style>
