import { getCurrentInstance, ref, unref } from 'vue';
import { TimelineMode } from './TimelineChart/TimelineMode';
import { Recordable } from '/#/global';
import { usePriority } from '/@/components/OtdPriority/src/usePriority';
import { StatusColorMap } from '/@/components/OtdStatus/src/useStatus';
import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
import { useRootSetting } from '/@/storage/projectConfigStorage';
import { WorkloadTimelineEmitsType, WorkloadTimelinePropsType } from './type';

export function useWorkloadTimeline() {
  const { props, emit } = getCurrentInstance() as unknown as {
    props: WorkloadTimelinePropsType;
    emit: WorkloadTimelineEmitsType;
  };
  const loading = ref(false);
  const timeline = ref<TimelineMode>();
  const { getDarkMode } = useRootSetting();
  const { getGlobalProvide } = useGlobalConfig();
  const { getRootContainer } = getGlobalProvide();
  const { PriorityDictionaryMap } = usePriority();
  // 创建时间线
  function createTimeline(dateRange: [string, string], timelineData: Recordable[]) {
    if (timeline.value) {
      timeline.value.update(dateRange, timelineData);
    } else {
      timeline.value = new TimelineMode({
        view: 'otd-timeline-canvas',
        dateRange,
        itemsKey: 'taskItems',
        themeMode: unref(getDarkMode),
        timelineData,
        theme: {
          timelineColorHandler: ({ record }) => {
            return StatusColorMap.background[record.status];
          },
          priorityHandler: ({ record }) => {
            return PriorityDictionaryMap[record.priority]?.color ?? '#afb3bc';
          },
        },
        container: getRootContainer,
        isCanEditer: ({ record }) => props.isCanEditer?.(record) ?? true,
        onDrop: (data) => props.drapEnd!(data),
        onResizeEnd: (data) => props.resizeEnd!(data),
        onMoveEnd: (data) => props.moveEnd!(data),
        onClick: ({ data: { record }, onDelete }) => emit('click', { record, onDelete }),
      });
    }
  }

  // 获取时间线数据
  function getTimelineData(range) {
    loading.value = true;
    (props as Recordable)
      ?.request?.()
      .then((res) => {
        createTimeline(range, res);
      })
      .finally(() => {
        loading.value = false;
      });
  }
  return {
    createTimeline,
    timeline,
    loading,
    getTimelineData,
  };
}
