<template>
  <div class="otd-trend-list">
    <div class="otd-trend-list__header"> <slot name="title" /> </div>
    <div class="otd-trend-list__body" :style="{ maxHeight: `${maxHeight}px`, height: `${height}px` }">
      <div class="otd-trend-list__item" v-for="item in data" :key="item[getFields.key]" @click="handleClickItem(item)">
        <div class="otd-trend-list__item-title otd-box-left otd-truncate">
          <OtdStatusTrigger :value="item[getFields.status]" hide-text is-text />
          <span class="otd-truncate">{{ item[getFields.label] }}</span>
        </div>
        <div class="otd-trend-list__item-percent otd-box-left" :style="`--width: ${Math.min(item.percent, 100)}%`">
          <span>{{ item.percent }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed, PropType, unref, watchEffect } from 'vue';
  import { Recordable } from '/#/global';
  import { OtdStatusTrigger } from '/@/components/OtdStatus';

  const props = defineProps({
    data: {
      type: Array as PropType<Recordable[]>,
      default: () => [],
    },
    fields: {
      type: Object as PropType<Partial<{ key: string; label: string; value: string; status: string }>>,
    },
    height: {
      type: Number,
    },
    maxHeight: {
      type: Number,
    },
    percent: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['click-item']);

  const getFields = computed(() => ({ key: 'key', label: 'label', value: 'value', status: 'status', ...props.fields }));
  const getValueTotal = computed(() => props.data.reduce((total, item) => total + item[unref(getFields).value], 0));

  function getPercentValue() {
    const { value } = unref(getFields);
    let total = 0;
    props.data.map((item, index) => {
      if (props.percent) {
        item.percent = item.value;
      } else {
        const percent = Math.round(Math.max(Number((item[value] / unref(getValueTotal)).toFixed(2)) * 100, 1));
        item.percent = props.data.length - 1 !== index ? percent : Math.max(100 - total, 1);
        total += item.percent;
      }
    });
  }

  watchEffect(getPercentValue);

  function handleClickItem(data) {
    emit('click-item', data);
  }
</script>
<style lang="less" scoped>
  @prefix: ~'otd-trend-list';

  .@{prefix} {
    &__header {
      line-height: 26px;
    }
    &__body {
      background-color: var(--otd-basic-bg);
      border-radius: var(--otd-default-radius);
      box-shadow: var(--otd-box-shadow);
      border: 1px solid var(--otd-border-color);
      margin-top: 6px;
      overflow: auto;
    }
    &__item {
      display: flex;
      font-size: 14px;
      line-height: 1;
      padding: 6px 6px 6px 12px;
      cursor: pointer;

      &-title {
        flex: 1;
        column-gap: 8px;
      }
      &-percent {
        --width: 0;
        position: relative;
        width: 200px;
        height: 24px;
        &::after {
          content: '';
          position: absolute;
          right: 0;
          display: block;
          width: var(--width);
          height: 100%;
          background-color: var(--otd-success-color);
          border-radius: var(--otd-default-radius);
          opacity: 0.2;
        }
        > span {
          position: absolute;
          right: 10px;
          font-weight: bold;
          font-size: 12px;
        }
      }
      &:hover {
        background-color: var(--otd-gray-hover);
        .@{prefix}__item-title {
          color: var(--otd-primary-color);
        }
      }
      & + & {
        border-top: 1px solid var(--otd-border-color);
      }
    }
  }
</style>
