import { PropType } from 'vue';
import { MoreActionItem } from '/@/components/OtdMoreAction';
import { mutable } from '/@/tool';
import { Recordable } from '/#/global';

export const getProps = () => ({
  list: {
    type: Array as PropType<Recordable[]>,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  hoverTip: {
    type: String,
  },
  deleteTip: {
    type: String,
  },
  action: {
    type: Array as PropType<MoreActionItem[]>,
    default: () => [],
  },
  hideAction: {
    type: Boolean,
    default: false,
  },
  hideActionItem: {
    type: Function as PropType<(data, action) => boolean>,
  },
  hideTaskCreate: {
    type: Function as PropType<(data) => boolean>,
  },
  hideGroupCreate: {
    type: Boolean,
  },
  listLabel: {
    type: String,
    default: 'items',
  },
});

const emit = ['update:list', 'deleteGroup', 'createGroup', 'editGroup', 'moveCard', 'moveGroup'] as const;

export const getEmits = () => mutable(emit);
