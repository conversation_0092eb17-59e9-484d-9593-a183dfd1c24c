import { Square } from './Square';
import { CanvasDrawType, ImageInfoType } from '../type';
import { merge } from 'lodash-es';

/**
 * 图片
 */
export class ImageRect {
  private info: ImageInfoType = {
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    isDraw: true,
  };
  private tool!: CanvasDrawType;
  private ImageCache: HTMLImageElement | null = null;
  constructor(tool: CanvasDrawType, info: ImageInfoType) {
    this.tool = tool;
    this.info = merge(this.info, info);
    if (this.info.isDraw) {
      this.draw();
    }
  }
  // 获取在线图片
  private getOnlineImage(url?: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      if (url) {
        var img = new Image(this.info.width, this.info.height);
        img.src = url;
        img.onload = () => {
          resolve(img);
        };
      } else {
        reject('Image url is Error');
      }
    });
  }
  // 绘制圆形图片
  private async drawImage() {
    const { ctx } = this.tool;
    const { image, x = 0, y = 0, width, height } = this.info;
    if (image) {
      this.ImageCache = image;
    } else if (!this.ImageCache) {
      const img = await this.getOnlineImage(this.info.url);
      this.ImageCache = img;
    }
    const square = new Square(this.tool, {
      ...this.info,
      isDraw: false,
      onFill: () => {
        ctx.save();
        ctx.clip();
        ctx.drawImage(this.ImageCache as HTMLImageElement, x, y, width, height);
        ctx.restore();
      },
    });
    square.draw();
    return this.ImageCache;
  }
  get getImage() {
    return this.ImageCache;
  }

  draw() {
    return this.drawImage();
  }
  // 销毁方块
  destroy() {
    this.ImageCache = null;
  }
}
