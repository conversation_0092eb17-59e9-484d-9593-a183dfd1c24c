:root {
  --vp-c-brand: #1890ff;
  --vp-c-brand-1: #1890ff;
  --vp-button-brand-border: #1890ff;
  --vp-button-brand-hover-border: #40a9ff;
  --vp-button-brand-hover-bg: #40a9ff;
  --vp-layout-max-width: 100%;
}

body {
  overflow-x: unset !important;
}
.content {
  padding: 0 !important;
}
.VPDoc.has-aside .content-container {
  max-width: unset !important;
}
.VPDocAside .content {
  padding: 0 32px !important;
}

.vp-doc table {
  margin: 0;
  overflow: unset;
  display: table;
}
.vp-doc p {
  margin: 0;
}
.vp-doc th,
.vp-doc td {
  border: 0;
}
.vp-doc tr {
  border-top: 0;
  background-color: inherit !important;
}

.vp-doc li + li {
  margin-top: unset;
}

.vp-doc ul,
.vp-doc ol {
  padding-left: 0;
  list-style: none;
  margin: 0;
}

.code-box {
  --vp-c-bg: #fff;
}
.dark .code-box {
  --vp-c-bg: #1b1b1f;
}

.vp-doc .code-box-demo a {
  /* color: inherit; */
  text-decoration: inherit;
  font-weight: normal;
}
.vp-doc p {
  line-height: inherit;
}
