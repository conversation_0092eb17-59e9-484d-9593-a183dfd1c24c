import { ref, getCurrentInstance } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { useMessage } from '/@/hooks/web/useMessage';
import { copyTextToClipboard } from '/@/hooks/web/useCopyToClipboard';
import { decodeHtml, removeHtmlTag } from '/@/utils/tool';
import { message } from 'ant-design-vue';
import { ERROR_COLOR } from '/@/setting';
import { MoreActionItem } from '/@/components/OtdMoreAction';
import { CommitEmitType } from './type';
import { OtdEmoticon } from '/@/components/OtdEmoticon';
export function useComment() {
  const { emit } = getCurrentInstance() as unknown as { emit: CommitEmitType };
  const { t } = useI18n();
  const { deleteConfirm } = useMessage();

  const commentAction = ref<MoreActionItem[]>([
    // 表情
    {
      id: 'emoticon',
      tip: t('common.emoticon'),
      icon: 'otd-icon-dianzan1',
      action: (data) => {
        let emoticon;
        let record = data;
        if (Array.isArray(data)) {
          record = data[0];
          emoticon = data[1];
        }
        handlerMoticon(record, emoticon ?? 'Like');
      },
      expand(data) {
        return <OtdEmoticon onSelect={(emoticon) => this.action!([data, emoticon])} />;
      },
      expandPlacement: 'bottom',
      expandTrigger: 'hover',
    },
    // 回复
    {
      id: 'reply',
      tip: t('common.reply'),
      icon: 'otd-icon-huifu',
      action: (data) => {
        emit('reply', data);
      },
    },
    // 编辑
    {
      id: 'edit',
      tip: t('common.editText'),
      icon: 'otd-icon-a-cateditsize24',
      action: (data) => {
        data.isEditDesc = true;
      },
    },
    // 删除
    {
      id: 'delete',
      name: t('common.delText'),
      color: ERROR_COLOR,
      icon: 'otd-icon-a-catdeletesize24',
      action: (data, list, index: number) => {
        deleteConfirm(() => {
          emit('delete', data, list, index);
        });
      },
    },
    // 复制
    {
      id: 3,
      name: t('common.tag.copy'),
      icon: 'otd-icon-fuzhi',
      action: (data) => {
        const str = removeHtmlTag(data.content);
        copyTextToClipboard(decodeHtml(str));
        message.success(t('common.tag.copySuccessTip'));
      },
    },
  ]);

  function handlerMoticon(data, emoticon) {
    emit('emoticon', { data, emoticon: emoticon ?? 'Like' });
  }

  // 取消编辑评论
  function handleCancelDesc({ clearable }, comment) {
    comment.isEditDesc = false;
    clearable();
  }
  // 确认提交评论
  function handleConfirmDesc({ value, resourceFiles, clearable }, comment) {
    emit('confirm', { value, resourceFiles, clearable }, comment);
  }

  function setEmoticon(emoticons) {
    return emoticons.map((item) => {
      const { emoji, userList } = item;
      const list = userList.map((item) => item.userName);
      return { emoji, userList, showUser: list.slice(0, 5), hideUser: list.slice(5) };
    });
  }

  return {
    commentAction,
    handlerMoticon,
    handleCancelDesc,
    handleConfirmDesc,
    setEmoticon,
  };
}
