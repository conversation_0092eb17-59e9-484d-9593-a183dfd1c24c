import { darkenColor, isPointInRegion } from '../tool';
import { CanvasDrawType, DirectionType, MouseActionEnum, SquareInfoType } from '../type';
import { Text } from './Text';
import { merge } from 'lodash-es';
import { Recordable } from '/#/global';

/**
 * 方形
 */
export class Square {
  private tool!: CanvasDrawType;
  private info: SquareInfoType = {
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    radius: 0,
    bgColor: '#fff',
    borderColor: '#ebebeb',
    isDraw: true,
    isDrap: false,
  };
  private mouseStatus = {
    enter: false,
    drap: false,
    resize: false,
    down: false,
    move: false,
  };
  private resizeRect: { [key in DirectionType]?: Square } = {};
  private graphs: Text[] = [];
  private canvasPadding: number[] = [0, 0, 0, 0];
  private lastMousePosition: { x: number; y: number } = { x: 0, y: 0 };
  private mouseOffset: { x: number; y: number } = { x: 0, y: 0 };
  constructor(tool: CanvasDrawType, info: SquareInfoType) {
    this.tool = tool;
    this.info = merge(this.info, info);
    const deleteFn = this.info?.onDelete;
    this.info.onDelete = () => {
      deleteFn?.(this.info);
      this.destroy();
    };
    this.addEventListener();
    if (this.info.isDraw) {
      this.draw();
    }
  }
  // 判断在区域内
  private judgeInArea(e: MouseEvent): Promise<MouseActionEnum> {
    const { x, y } = this.lastMousePosition;
    const offset = 5;
    return new Promise((resolve) => {
      if (this.isPointInRegion(e) && !this.mouseStatus.enter) {
        setTimeout(() => {
          this.mouseStatus.enter = true;
          resolve(MouseActionEnum.Enter);
        });
      } else if (
        this.isPointInRegion(e) &&
        (x - offset > e.offsetX || x + offset < e.offsetX || y - offset > e.offsetY || y + offset < e.offsetY)
      ) {
        this.lastMousePosition = { x: e.offsetX, y: e.offsetY };
        resolve(MouseActionEnum.Move);
      } else {
        resolve(MouseActionEnum.Leave);
      }
    });
  }
  // #region 鼠标事件处理
  // 点击事件
  private onClick = (e: MouseEvent) => {
    if (!(this.mouseStatus.resize || this.mouseStatus.move) && this.isPointInRegion(e)) {
      this.info?.onClick?.(this.info);
    }
  };
  // 鼠标移动事件
  private onMouseMove = (e: MouseEvent) => {
    this.judgeInArea(e).then((status) => {
      if (status === MouseActionEnum.Enter) {
        this.onMouseEnter();
        const { cursor, hoverBgColor, isResise } = this.info;
        this.onSetArea(() => {
          if (hoverBgColor) {
            this.draw({ hoverColor: hoverBgColor });
          }
          if (cursor) {
            this.tool.canvas.style.setProperty('cursor', cursor);
          }
          if (isResise) {
            this.drawResizeRect();
          }
        });
        this.info?.onMouseEnter?.(this.info);
      } else if (status === MouseActionEnum.Move) {
        this.info?.onMouseMove?.(e, { ...this.info, mouse: { ...this.mouseStatus } });
        if (this.info.isMove && this.mouseStatus.down && !this.mouseStatus.move && !this.mouseStatus.resize) {
          this.mouseStatus.move = true;
          this.mouseOffset.x = e.offsetX - this.info.x;
          this.mouseOffset.y = e.offsetY - this.info.y;
        }
      } else {
        this.onMouseLeave(e);
      }
    });
    if (this.mouseStatus.move) {
      this.moveRect(e);
    }
  };
  // 鼠标离开区域
  private onMouseLeave = (e: MouseEvent) => {
    const { enter, drap } = this.mouseStatus;
    if (!this.isPointInRegion(e) && (enter || drap)) {
      this.mouseStatus.enter = false;
      this.mouseStatus.drap = false;

      const { hoverBgColor, drapBgColor } = this.info;
      this.onSetArea(() => {
        if (hoverBgColor || drapBgColor) {
          this.draw();
        }
        if (this.info.cursor) {
          this.tool.canvas.style.setProperty('cursor', 'inherit');
        }
        Object.values(this.resizeRect)?.map((square) => square.destroy());
        this.info?.onMouseLeave?.(this.info);
        this.info?.onDrapLeave?.(this.info);
      });
      this.removeMouseListener();
    }
  };
  // 鼠标进入区域
  private onMouseEnter = () => {
    this.removeMouseListener();
    const { onClick, onMouseDown, isMove } = this.info;
    if (onClick) {
      this.tool.canvas.addEventListener('click', this.onClick);
    }
    if (onMouseDown || isMove) {
      this.tool.canvas.addEventListener('mousedown', this.onMouseDown);
    }
    this.tool.canvas.addEventListener('mouseleave', this.onMouseLeave);
  };
  // 鼠标按下
  private onMouseDown = (e: MouseEvent) => {
    if (this.isPointInRegion(e)) {
      this.mouseStatus.down = true;
      this.info?.onMouseDown?.(e, this.info);
      if (this.info.onMouseUp || this.info.isMove) {
        document.addEventListener('mouseup', this.onMouseUp);
      }
    }
  };
  // 鼠标抬起
  private onMouseUp = (e: MouseEvent) => {
    document.removeEventListener('mouseup', this.onMouseUp);
    this.info?.onMouseUp?.(e, this.info);
    this.mouseStatus.down = false;
    if (this.mouseStatus.move) {
      this.info?.onMoveEnd?.(e, this.info);
    }
    setTimeout(() => {
      this.resetMouseStatus();
    }, 50);
  };
  // 添加监听事件
  private addEventListener() {
    const { onMouseEnter, onMouseMove, onMouseLeave, cursor, hoverBgColor, isDrap } = this.info;
    if (onMouseEnter || onMouseMove || onMouseLeave || cursor || hoverBgColor) {
      this.tool.canvas.addEventListener('mousemove', this.onMouseMove);
    }
    if (isDrap) {
      this.registerDrapListener();
    }
  }
  // 移除鼠标事件
  private removeMouseListener() {
    this.tool.canvas.removeEventListener('click', this.onClick);
    this.tool.canvas.removeEventListener('mouseleave', this.onMouseLeave);
    this.tool.canvas.removeEventListener('mousedown', this.onMouseDown);
  }
  // #endregion

  // #region 拖拽事件
  // 拖拽释放
  private onDrop = (e: DragEvent) => {
    e.preventDefault(); // 阻止默认行为，避免打开拖拽的文件
    if (this.mouseStatus.drap) {
      const record = JSON.parse(e.dataTransfer?.getData('task-record') as string);
      this.info?.onDrop?.({ info: this.info, record });
      this.mouseStatus.enter = false;
      this.mouseStatus.drap = false;
    }
  };
  // 拖拽移动
  private onDragover = (e: DragEvent) => {
    e.preventDefault(); // 确保在dragover事件中调用preventDefault
    this.judgeInArea(e).then((status) => {
      if (status === MouseActionEnum.Enter) {
        this.onDragEnter();
        if (!this.mouseStatus.drap) {
          this.onSetArea(() => {
            this.mouseStatus.drap = true;
            this.draw({ hoverColor: this.info.drapBgColor });
            this.info?.onDrapEnter?.(this.info);
          });
        }
      } else {
        this.onMouseLeave(e);
      }
    });
  };
  // 拖拽进入
  private onDragEnter = () => {
    const { canvas } = this.tool;
    // 拖拽释放
    canvas.addEventListener('drop', this.onDrop);
    // 拖拽离开
    canvas.addEventListener('dragleave', (e) => {
      this.onMouseLeave(e);
    });
  };
  // 注册拖拽事件
  private registerDrapListener() {
    const { canvas } = this.tool;
    // 拖拽移动
    canvas.addEventListener('dragover', this.onDragover);
  }
  // 移除拖拽事件
  private removeDrapListener() {
    this.tool.canvas.removeEventListener('drop', this.onDrop);
    this.tool.canvas.removeEventListener('dragleave', this.onMouseLeave);
  }
  // #endregion

  // 设置可画区域
  private onSetArea(handler: () => void) {
    const { onSetArea } = this.info;
    if (onSetArea) {
      this.canvasPadding = onSetArea(handler);
    } else {
      handler();
    }
  }

  // #region 调整大小
  // 获取Resize信息
  private getResizeInfo(key: DirectionType) {
    const rectWidth = 6;
    const { canvas } = this.tool;
    const { x, y, width, height, bgColor, realRadius, minWidth = 0, onSetArea } = this.info;
    const [top, right, bottom, left] = realRadius ?? [0, 0, 0, 0];
    const DirectionAction: Record<DirectionType, (e: MouseEvent) => void> = {
      left: (e) => {
        if (e.offsetX >= x && this.info.width <= minWidth) return;
        const newX = Math.max(e.offsetX, this.canvasPadding[3] ?? 0);
        this.info.width = Math.max(x - newX + width, minWidth + 1);
        this.info.x = Math.min(newX, x + width - minWidth - 1);
      },
      right: (e) => {
        if (e.offsetX <= x + this.info.width && this.info.width <= minWidth) return;
        const maxWidth = canvas.width - Math.max(x, this.canvasPadding[3]);
        const newWidth = Math.min(e.offsetX - x, maxWidth);
        this.info.width = Math.max(minWidth, newWidth < 0 ? maxWidth : newWidth);
      },
      top: () => {},
      bottom: () => {},
    };
    let mouseMoveHandler: (e: MouseEvent) => void;
    const rectInfo: SquareInfoType = {
      width,
      height,
      x,
      y,
      bgColor: darkenColor(bgColor, 0.8),
      cursor: 'ew-resize',
      onSetArea,
      onMouseDown: (_, { data }) => {
        mouseMoveHandler = (e: MouseEvent) => {
          DirectionAction?.[data.key]?.(e);
          this.onSetArea(() => {
            this.info?.onResize?.(e, this.info);
            const { hoverBgColor } = this.info;
            this.draw({ hoverColor: hoverBgColor, isResize: true });
          });
        };
        this.mouseStatus.resize = true;
        this.mouseStatus.move = false;
        document.body.addEventListener('mousemove', mouseMoveHandler);
        document.body.style.setProperty('cursor', 'ew-resize');
      },
      onMouseUp: (e, { data }) => {
        this.info?.onResizeEnd?.(e, this.info, data?.key);
        setTimeout(() => {
          this.resetMouseStatus();
        }, 50);
        document.body.style.setProperty('cursor', 'inherit');
        document.body.removeEventListener('mousemove', mouseMoveHandler);
      },
    };
    const DirectionMap: Record<DirectionType, SquareInfoType> = {
      left: { ...rectInfo, width: rectWidth, radius: [top, 0, 0, left] },
      right: {
        ...rectInfo,
        width: rectWidth,
        x: x + width - rectWidth,
        radius: [0, right, bottom, 0],
      },
      top: { ...rectInfo, height: rectWidth, radius: [top, right, 0, 0] },
      bottom: {
        ...rectInfo,
        height: rectWidth,
        y: y + height - rectWidth,
        radius: [0, 0, bottom, left],
      },
    };

    return DirectionMap[key];
  }
  // 绘制调整大小方块
  private drawResizeRect() {
    this.info.resizeDirection?.map((key) => {
      const resizeRect = this.getResizeInfo(key);
      this.resizeRect[key] = new Square(this.tool, { ...resizeRect, data: { key } });
    });
  }

  private destroyResizeRect() {
    Object.values(this.resizeRect)?.map((square) => square.destroy());
    this.resizeRect = {};
  }
  // #endregion

  // #region 移动方块
  private moveRect(e: MouseEvent) {
    const { canvasPadding } = this;
    this.info.x = Math.max(canvasPadding[3], e.offsetX - this.mouseOffset.x);
    this.info.y = Math.max(canvasPadding[0] + 1, e.offsetY - this.mouseOffset.y);
    this.onSetArea(() => {
      this.info?.onMove?.(e, this.info);
      const { hoverBgColor } = this.info;
      this.draw({ hoverColor: hoverBgColor, isResize: true });
    });
  }
  // #endregion

  // #region 绘制图形
  // 绘制方形边框
  private drawRectBorder({ x, y, width, height, radius }) {
    const { ctx } = this.tool;
    // 绘制路径
    ctx.beginPath();
    // 左上角
    ctx.moveTo(x, y + radius[0]);
    ctx.arcTo(x, y, Math.max(x + radius[0], 0), y, radius[0]);

    // 右上角
    ctx.lineTo(x + width - radius[1], y);
    ctx.arcTo(x + width, y, x + width, Math.max(y + radius[1], 0), radius[1]);

    // 右下角
    ctx.lineTo(x + width, y + height - radius[2]);
    ctx.arcTo(x + width, y + height, Math.max(x + width - radius[2], 0), y + height, radius[2]);

    // 左下角
    ctx.lineTo(x + radius[3], y + height);
    ctx.arcTo(x, y + height, x, Math.max(y + height - radius[3]), radius[3]);

    // 闭合路径
    ctx.closePath();
  }
  // 绘制方形
  private drawRect(x, y, width, height, radius, hoverColor) {
    const { ctx } = this.tool;
    let { bgColor, onFill } = this.info;
    this.drawRectBorder({ x, y, width, height, radius });
    // 闭合路径
    ctx.closePath();
    if (onFill) {
      onFill();
    } else {
      // 填充背景颜色
      ctx.fillStyle = hoverColor ?? bgColor;
      ctx.fill();
    }
    ctx.save();
    ctx.restore();
  }

  // 设置方形参数
  private getDrawRect() {
    let { width, height, radius = 0 } = this.info;
    if (width <= 0 || height <= 0) return false;
    const minSize = Math.min(width, height);
    if ((radius as number) > minSize / 2) radius = Math.max(Math.round(minSize / 2), 0);
    if (typeof radius === 'number') {
      radius = [radius, radius, radius, radius];
    } else if (radius instanceof Array) {
      const radiusLength = {
        1: () => [radius[0], radius[0], radius[0], radius[0]],
        2: () => [radius[0], radius[1], radius[1], radius[0]],
        3: () => [radius[0], radius[1], radius[1], radius[2]],
        4: () => [radius[0], radius[1], radius[2], radius[3]],
      };
      radius = radiusLength[radius.length]();
    } else {
      throw new Error('Radius error');
    }
    this.info.realRadius = radius as number[];
    return radius;
  }

  // 绘制边框
  private drawBorder() {
    if (!this.info.borderWidth) return;
    const round = (num: number) => Math.round(num);
    const { x, y, width, height, borderColor = '', borderWidth } = this.info;
    const { ctx } = this.tool;
    const info: SquareInfoType = {
      x: round(x),
      y: round(y),
      width: round(width),
      height: round(height),
    };
    const endSeat = { x: info.x + info.width, y: info.y + info.height };
    ctx.save();
    ctx.translate(0.5, 0.5);
    const radius = this.getDrawRect();
    if (radius && !this.info.isCell) {
      this.drawRectBorder({ ...info, radius });
      ctx.lineWidth = borderWidth;
      ctx.strokeStyle = borderColor;
      ctx.stroke();
      ctx.restore();
      ctx.closePath();
    } else {
      ctx.beginPath();
      ctx.lineWidth = borderWidth;
      ctx.strokeStyle = borderColor;
      ctx.moveTo(endSeat.x, info.y); // 移动到单元格右上角点
      ctx.lineTo(endSeat.x, endSeat.y); // 绘制右边线
      ctx.lineTo(info.x, endSeat.y); // 绘制下边线
      ctx.stroke();
      ctx.restore();
      ctx.closePath();
    }
  }

  // 重置鼠标状态
  private resetMouseStatus() {
    this.mouseStatus.down = false;
    this.mouseStatus.resize = false;
    this.mouseStatus.move = false;
  }

  // 判断是否在区域内
  isPointInRegion(e: MouseEvent) {
    const { x, y, width, height } = this.info;
    return isPointInRegion([e.offsetX, e.offsetY], { start: [x, y], end: [x + width, y + height] });
  }

  // 绘制图形
  draw(data?) {
    const { hoverColor, isResize, isReset, borderWidth, borderColor } = data ?? {};
    this.destroyResizeRect();
    const result = this.getDrawRect();
    if (result) {
      let { x, y, width, height } = this.info;
      if (isReset) {
        this.info.borderWidth = borderWidth;
        this.info.borderColor = borderColor;
      }
      this.drawRect(x, y, width, height, result, hoverColor);
      this.drawBorder();
      this.add([], isResize);
    }
  }

  // 添加图形内容
  add(graphs: Text[] = [], isResize?: boolean) {
    if (graphs.length > 0) {
      this.graphs = graphs;
    }
    this.graphs.map((graph) => {
      const { x = 0, y = 0, width } = this.info;
      graph.setInfo({
        x,
        y,
        maxWidth: isResize ? graph.getInfo.maxWidth : width - 20,
      });
      graph.draw();
    });
  }
  // #endregion

  // 销毁方块
  destroy() {
    this.removeMouseListener();
    this.tool.canvas.removeEventListener('mousemove', this.onMouseMove);
    this.removeDrapListener();
    this.tool.canvas.removeEventListener('dragover', this.onDragover);
    this.graphs.map((item) => item?.destroy?.());
    this.destroyResizeRect();
  }
  setInfo(info: Recordable) {
    this.info = merge(this.info, info);
  }

  // 获取图形信息
  get getInfo() {
    return this.info;
  }
}
