<template>
  <div class="otd-history-card" :style="{ height: height }">
    <OtdInfiniteScroll
      ref="InfiniteScrollRef"
      v-model:value="historyData"
      :immediate-load="immediateLoad"
      :hide-load="({ noMoreData }) => !loadMore || (historyCardData.size <= 0 && noMoreData)"
      :load-more="loadMore!"
      custom-no-data
    >
      <template #default="{ noMoreData }">
        <slot name="header" />
        <template v-if="historyCardData.size <= 0 && noMoreData">
          <Empty class="otd-box-center" style="flex-direction: column; flex: 1" />
        </template>
        <template v-else>
          <div class="otd-history-card__time" v-for="[key, card] in historyCardData" :key="key">
            <div class="otd-history-card__time-header">
              <span class="otd-history-card__time-header__title">{{ formatDateToText(key) }}</span>
            </div>
            <div class="otd-history-card__time-body">
              <template v-for="history in card" :key="card.relatedId">
                <OtdHistoryCardItem :data="history" :fields="getFields" @click-item="handleClickItem" />
              </template>
            </div>
          </div>
        </template>
      </template>
    </OtdInfiniteScroll>
  </div>
</template>
<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { Empty } from 'ant-design-vue';
  import { getProps } from './props';
  import { useHistoryCard } from './useHistoryCard';
  import OtdHistoryCardItem from './components/OtdHistoryCardItem.vue';
  import { OtdHistoryCardDataType } from './type';
  import { formatDateToText } from '/@/tool';
  import { OtdInfiniteScroll } from '/@/components/OtdInfiniteScroll';

  const props = defineProps(getProps());
  const emit = defineEmits(['update:value', 'click-item']);
  const { getFields, handleClickItem } = useHistoryCard();
  const InfiniteScrollRef = ref();

  const historyData = computed({
    get: () => props.value,
    set: (value) => {
      emit('update:value', value);
    },
  });

  function getHistoryRecord(item) {
    const fields = unref(getFields);
    return {
      relatedId: item[fields.relatedId],
      taskType: item[fields.taskType],
      relatedTitle: item[fields.relatedTitle],
      list: [item],
      status: item[fields.status],
      relatedType: item[fields.relatedType],
      relatedName: item[fields.relatedName],
      i18NRelatedType: item[fields.i18NRelatedType],
      i18NSubRelatedType: item[fields.i18NSubRelatedType],
    };
  }

  const historyCardData = computed(() => {
    const historyTimeMap = new Map<string, OtdHistoryCardDataType[]>();
    const { creationTime, relatedId } = unref(getFields);
    unref(historyData).map((item) => {
      const time = item[creationTime].split('T')[0];
      const data = historyTimeMap.get(time);
      if (data) {
        const group = data![data.length - 1];
        if (group.relatedId === item[relatedId]) {
          group.list!.push(item);
        } else {
          data.push(getHistoryRecord(item));
        }
      } else {
        historyTimeMap.set(time, [getHistoryRecord(item)]);
      }
    });
    return historyTimeMap;
  });

  defineExpose({
    refresh: () => InfiniteScrollRef.value.refresh(),
  });
</script>
<style lang="less" scoped>
  .otd-history-card {
    display: flex;
    flex-direction: column;
    padding: 0 32px;
    row-gap: 16px;
    background-color: var(--otd-content-bg);
    height: 100%;
    overflow-y: auto;
    padding-bottom: 10px;
    .otd-infinite-scroll {
      width: calc(100% + 64px);
      margin: 0 -32px;
    }
    &__time {
      display: flex;
      flex-direction: column;
      align-items: center;
      &-header {
        position: sticky;
        top: -1px;
        width: 100%;
        padding-top: 4px;
        height: 40px;
        background-color: var(--otd-content-bg);
        z-index: 3;
        &::after,
        &__title {
          position: absolute;
          top: 4px;
          left: 50%;
          transform: translateX(-50%);
        }
        &::after {
          content: '';
          display: block;
          width: 100%;
          top: 17px;
          border-bottom: 1px solid var(--otd-border-gray);
        }
        &__title {
          font-weight: 500;
          background-color: var(--otd-basic-bg);
          border: 1px solid var(--otd-border-gray);
          border-radius: var(--otd-large-radius);
          padding: 4px 12px;
          font-size: 12px;
          z-index: 2;
          line-height: 16px;
        }
      }
      &-body {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        row-gap: 16px;
      }
    }
  }
</style>
