<template>
  <div
    class="otd-tiny"
    :class="{ [prefixCls]: true, 'is-reverse': isReverse, 'is-border': bordered, 'is-disabled': disabled }"
    :style="{ width: containerWidth }"
  >
    <textarea :id="tinymceId" ref="elRef" :style="{ visibility: 'hidden' }" v-if="!initOptions.inline"></textarea>
    <slot v-else></slot>
  </div>
</template>

<script lang="ts" setup name="OtdEditor">
  import type { Editor, RawEditorSettings } from 'tinymce';
  import tinymce from 'tinymce/tinymce';
  import 'tinymce/themes/silver';
  import 'tinymce/icons/default/icons';
  import 'tinymce/plugins/advlist';
  import 'tinymce/plugins/anchor';
  import 'tinymce/plugins/autolink';
  import 'tinymce/plugins/autosave';
  import 'tinymce/plugins/code';
  import 'tinymce/plugins/codesample';
  import 'tinymce/plugins/directionality';
  import 'tinymce/plugins/fullscreen';
  import 'tinymce/plugins/hr';
  import 'tinymce/plugins/insertdatetime';
  import 'tinymce/plugins/link';
  import 'tinymce/plugins/lists';
  import 'tinymce/plugins/image';
  import 'tinymce/plugins/media';
  import 'tinymce/plugins/nonbreaking';
  import 'tinymce/plugins/noneditable';
  import 'tinymce/plugins/pagebreak';
  import 'tinymce/plugins/paste';
  import 'tinymce/plugins/preview';
  import 'tinymce/plugins/print';
  import 'tinymce/plugins/save';
  import 'tinymce/plugins/searchreplace';
  import 'tinymce/plugins/spellchecker';
  import 'tinymce/plugins/tabfocus';
  import 'tinymce/plugins/table';
  import 'tinymce/plugins/template';
  import 'tinymce/plugins/textpattern';
  import 'tinymce/plugins/visualblocks';
  import 'tinymce/plugins/visualchars';
  import 'tinymce/plugins/wordcount';
  import 'tinymce/plugins/autoresize';
  import '@npkg/tinymce-plugins/attachment';
  import './plugins/send-content';
  import './plugins/confirm-content';
  import './plugins/mention';

  import {
    computed,
    nextTick,
    ref,
    unref,
    watch,
    onDeactivated,
    onBeforeUnmount,
    useAttrs,
    getCurrentInstance,
  } from 'vue';
  import { buildShortUUID } from '/@/utils/uuid';
  import { bindHandlers } from './helper';
  import { onMountedOrActivated } from '/@/hooks/core/onMountedOrActivated';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { isNumber } from '/@/utils/is';
  import { useLocale } from '/@/locales/useLocale';
  import { darkMode } from '/@/utils/theme';
  import { bindEvent } from './event';
  import { Recordable, Nullable } from '/#/global';
  import { getProps } from './props';

  const props = defineProps(getProps());
  const emit = defineEmits([
    'change',
    'update:modelValue',
    'inited',
    'init-error',
    'blur',
    'send',
    'confirm',
    'cancel',
  ]);
  const editorRef = ref<Nullable<Editor>>(null);
  const tinymceId = ref<string>(buildShortUUID('tiny-vue'));
  const elRef = ref<Nullable<HTMLElement>>(null);
  const instance = getCurrentInstance()!;

  const { getLocale } = useLocale();
  const { prefixCls } = useDesign('tinymce-container');
  const attrs = useAttrs();

  const tinymceContent = computed(() => props.modelValue);
  const containerWidth = computed(() => {
    const width = props.width;
    if (isNumber(width)) return `${width}px`;
    return width;
  });

  const skinName = computed(() => (!darkMode.mode ? 'oxide' : 'oxide-dark'));
  const langName = computed(() => getLocale.value ?? 'zh_CN');
  const publicPath = import.meta.env.VITE_PUBLIC_PATH || '/';
  const content_css = computed(() => publicPath + 'resource/tinymce/skins/ui/' + skinName.value + '/content.min.css');
  const initOptions = computed((): RawEditorSettings => {
    const { height, maxHeight, minHeight, options, placeholder, toolbar, plugins, isSimple, bottomMargin } = props;
    return {
      selector: `#${unref(tinymceId)}`,
      height: height === 'auto' ? undefined : height,
      autoresize_bottom_margin: bottomMargin,
      max_height: maxHeight,
      min_height: minHeight,
      toolbar,
      menubar: isSimple ? false : 'file edit insert view format table',
      plugins,
      placeholder,
      language_url: publicPath + 'resource/tinymce/langs/' + unref(langName) + '.js',
      language: unref(langName),
      branding: false,
      default_link_target: '_blank',
      link_title: false,
      object_resizing: false,
      skin: unref(skinName),
      skin_url: publicPath + 'resource/tinymce/skins/ui/' + unref(skinName),
      content_css: unref(content_css),
      readonly: props.disabled,
      ...options,
      setup: (editor: Editor) => {
        editorRef.value = editor;
        editor.on('init', (e) => initSetup(e));
        editor['VueInstance'] = instance;
        bindEvent({ editor, props, emit, attrs });
      },
    };
  });

  watch(
    () => props.disabled,
    () => {
      const editor = unref(editorRef);
      if (!editor) return;
      editor.setMode(props.disabled ? 'readonly' : 'design');
    },
  );

  watch(
    () => darkMode.mode,
    () => {
      destory();
      initEditor();
    },
  );

  onMountedOrActivated(() => {
    if (!initOptions.value.inline) tinymceId.value = buildShortUUID('tiny-vue');
    nextTick(() => {
      setTimeout(() => {
        initEditor();
      }, 30);
    });
  });

  onBeforeUnmount(() => {
    destory();
  });

  onDeactivated(() => {
    destory();
  });

  function destory() {
    tinymce?.remove?.(unref(initOptions).selector!);
  }

  function initEditor() {
    const el = unref(elRef);
    el && (el.style.visibility = '');
    tinymce
      .init(unref(initOptions))
      .then((editor) => emit('inited', editor))
      .catch((err) => emit('init-error', err));
  }

  function initSetup(e) {
    const editor = unref(editorRef);
    if (!editor) return;
    const value = props.modelValue ?? '';
    editor.setContent(value);
    bindModelHandlers(editor);
    bindHandlers(e, attrs, unref(editorRef));
  }

  function setValue(editor: Recordable, val: string, prevVal?: string) {
    if (
      editor &&
      typeof val === 'string' &&
      val !== prevVal &&
      val !== editor.getContent({ format: attrs.outputFormat })
    ) {
      editor.setContent(val);
    }
  }

  function bindModelHandlers(editor: any) {
    const modelEvents = attrs.modelEvents ? attrs.modelEvents : null;
    const normalizedEvents = Array.isArray(modelEvents) ? modelEvents.join(' ') : modelEvents;
    watch(
      () => props.modelValue,
      (val: string, prevVal: string) => setValue(editor, val, prevVal),
    );
    editor.on(normalizedEvents ? normalizedEvents : 'change keyup undo redo', () => {
      const content = editor.getContent({ format: attrs.outputFormat });
      emit('update:modelValue', content);
      emit('change', content);
    });
  }

  defineExpose({
    prefixCls,
    containerWidth,
    initOptions,
    tinymceContent,
    elRef,
    tinymceId,
    editorRef,
    getEditorId: () => tinymceId.value,
    getText: () => unref(editorRef)?.getContent({ format: 'text' }),
  });
</script>

<style lang="less" scoped></style>

<style lang="less">
  @prefix-cls: ~'@{namespace}-tiny';

  .@{prefix-cls} {
    position: relative;
    line-height: normal;

    textarea {
      z-index: -1;
      visibility: hidden;
    }
  }
</style>
