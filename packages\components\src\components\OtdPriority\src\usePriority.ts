import { ColorOptionType } from '../../OtdColor';
import { useI18n } from '/@/hooks/web/useI18n';

export enum TaskPriorityEnum {
  Low = 0,
  Normal = 1,
  High = 2,
  Urgent = 3,
}

export function usePriority() {
  const { Urgent, High, Normal, Low } = TaskPriorityEnum;
  const { t } = useI18n();
  // 优先级映射
  const PriorityDictionaryMap: Record<string, ColorOptionType> = {
    // 紧急
    [Urgent]: {
      label: t('common.priority.Urgent'),
      value: Urgent,
      color: '#cd201f',
    },
    // 高
    [High]: { label: t('common.priority.High'), value: High, color: '#FFCC00' },
    // 中
    [Normal]: {
      label: t('common.priority.Normal'),
      value: Normal,
      color: '#2db7f5',
    },
    // 低
    [Low]: { label: t('common.priority.Low'), value: Low, color: '#87d068' },
  };

  // 优先级数据
  const PriorityDictionary = [
    // 紧急
    PriorityDictionaryMap[Urgent],
    // 高
    PriorityDictionaryMap[High],
    // 中
    PriorityDictionaryMap[Normal],
    // 低
    PriorityDictionaryMap[Low],
  ];
  return {
    PriorityDictionaryMap,
    PriorityDictionary,
  };
}
