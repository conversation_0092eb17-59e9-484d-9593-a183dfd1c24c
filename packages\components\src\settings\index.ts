import type { LocaleSetting, ProjectConfigType } from '/#/config';
import type { Recordable } from '/#/global';
import type { SorterResult } from 'ant-design-vue/es/table/interface';
import { LocaleEnum, ThemeEnum } from '/@/enums/appEnum';
import { isDevMode } from '/@/utils/env';
import loadingGif from '/@/assets/images/loading.gif';

export const ProjectName = 'Otd';
export const prefixCls = 'otd';
export const loadingSrc = loadingGif;
export const uploadUrl = '/upload';

// aes encryption key

// Whether the system cache is encrypted using aes
export const enableStorageEncryption = !isDevMode();

// basic-table setting
export const tableSetting = {
  fetchSetting: {
    // The field name of the current page passed to the background
    pageField: 'pageIndex',
    // The number field name of each page displayed in the background
    sizeField: 'pageSize',
    // Field name of the form data returned by the interface
    listField: 'items',
    // Total number of tables returned by the interface field name
    totalField: 'totalCount',
  },
  // Number of pages that can be selected
  pageSizeOptions: ['10', '50', '80', '100'],
  // Default display quantity on one page
  defaultPageSize: 10,
  // Default Size
  defaultSize: 'middle',
  // Custom general sort function
  defaultSortFn: (sortInfo: SorterResult) => {
    const { field, order } = sortInfo;
    if (field && order) {
      return {
        // The sort field passed to the backend you
        field,
        // Sorting method passed to the background asc/desc
        order,
      };
    } else {
      return {};
    }
  },
  // Custom general filter function
  defaultFilterFn: (data: Partial<Recordable<string[]>>) => {
    return data;
  },
};

export const localeSetting: LocaleSetting = {
  showPicker: true,
  // Locale
  locale: LocaleEnum.ZH_CN,
  // Default locale
  fallback: LocaleEnum.ZH_CN,
  // available Locales
  availableLocales: [LocaleEnum.ZH_CN, LocaleEnum.EN_US],
};

export const projectSetting: ProjectConfigType = {
  grayMode: false,
  colorWeak: false,
  darkMode: ThemeEnum.LIGHT,
};
