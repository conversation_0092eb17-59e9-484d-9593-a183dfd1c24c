import 'virtual:svg-icons-register';
import type { Theme } from 'vitepress';
import DefaultTheme from 'vitepress/theme';
import Demo from './Demo.vue';
import '@otd/otd-ui/src/style/index.less';
// import "@otd/otd-ui/dist/style.css";
import OtdUI, { setThemeClor } from '@otd/otd-ui';
// import { setupGlobDirectives } from "../../../../../";
import './index.css';

let orignialSetItem = window.localStorage.setItem; // 原生localStorage.setItem方法
localStorage.setItem = function (key, newValue) {
  let setItemEvent = new Event('setItemEvent'); // 重写注册setItem
  (setItemEvent as any).key = key;
  (setItemEvent as any).newValue = newValue;
  window.dispatchEvent(setItemEvent); // 派发setItem
  setThemeClor(newValue === 'dark' ? false : true);
  orignialSetItem.apply(this, arguments); // 设置值
};

export default {
  ...DefaultTheme,
  enhanceApp({ app }) {
    app.use(OtdUI);
    app.component('Demo', Demo);
  },
} as Theme;
