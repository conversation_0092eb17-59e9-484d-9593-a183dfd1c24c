<template>
  <OtdTimeline :dataSource="dataSource" />
</template>
<script lang="ts" setup>
import { OtdTimeline } from '@otd/otd-ui';
import { reactive } from 'vue';

const dataSource = reactive(
  new Array(5).fill(0).map((_, i) => ({
    key: i,
    img: 'https://tse1-mm.cn.bing.net/th/id/OIP-C.x7DSOuj9V43e9FrpQ7FFAQHaF7?w=221&h=180&c=7&r=0&o=5&pid=1.7',
    date: '2024-02-09 12:34',
    name: '张三',
    text: '西湖区湖底公园1号',
  })),
);
</script>
<style lang="less" scoped></style>
