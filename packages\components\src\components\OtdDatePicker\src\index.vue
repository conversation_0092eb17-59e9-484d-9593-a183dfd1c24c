<template>
  <Popover
    :open="disabled ? false : undefined"
    trigger="click"
    placement="bottomLeft"
    overlayClassName="otd-date-picker__popover"
    :z-index="100"
    :arrow="false"
  >
    <div class="otd-data-bubble" data-bubble :class="{ 'placeholder-disabled': disabled }">
      <DateTrigger
        :dates="currentDateRange"
        :remind="remindRule"
        :repeat="repeatRule"
        :placeholder="placeholder"
        :isSimple="isSimple"
        :hideEndTime="hideEndTime"
        :hideStartTime="hideStartTime"
        :notTipColor="notTipColor"
      />
    </div>
    <template #content>
      <div class="otd-date-picker__overlay">
        <!-- 日期内容 -->
        <div class="otd-date-picker__overlay__header">
          <DateEditor
            v-model:value="currentDateRange[0]"
            v-model:index="dateIndex"
            :default-index="0"
            :placeholder="startPlaceholderText"
            @change="handleChange()"
          />
          <DateEditor
            v-model:value="currentDateRange[1]"
            v-model:index="dateIndex"
            :default-index="1"
            :placeholder="endPlaceholderText"
            @change="handleChange()"
          />
        </div>
        <div class="otd-date-picker__overlay__body">
          <!-- 快捷选择时间 -->
          <div class="otd-date-picker__overlay__quick-date">
            <OtdScrollbar class="quick-date-group">
              <li
                v-for="(item, index) in rangePresets"
                :key="index"
                class="quick-date-option placeholder-hover"
                @click="handleQuickDate(item)"
              >
                {{ item.label }}
              </li>
            </OtdScrollbar>
            <div class="otd-date-picker__overlay__picker" v-if="!hideRemind || !hideRepeat">
              <!-- 提醒 -->
              <OtdRemindPicker
                v-if="!hideRemind"
                v-model:value="remindRule"
                :dates="currentDateRange"
                @change="handleChange(false)"
              />
              <!-- 重复 -->
              <OtdRepeatPicker
                v-if="!hideRepeat"
                v-model:value="repeatRule"
                :dates="currentDateRange"
                @change="handleChange(false)"
              />
            </div>
          </div>
          <!-- 日期看板 -->
          <div class="otd-date-picker__overlay__panel">
            <OtdPickerPanel
              v-model:value="currentDateRange"
              :index="dateIndex"
              is-range
              @change="handleChangePanelRange"
            />
          </div>
        </div>
      </div>
    </template>
  </Popover>
</template>
<script lang="ts" setup>
  import { Popover } from 'ant-design-vue';
  import { useDatePicker } from './useDatePicker';
  import { OtdPickerPanel } from './components/OtdPickerPanel';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { OtdRemindPicker } from './components/OtdRemindPicker';
  import { OtdRepeatPicker } from './components/OtdRepeatPicker';
  import DateEditor from './components/DateEditor.vue';
  import DateTrigger from './components/DateTrigger.vue';
  import { computed, watchEffect } from 'vue';
  import { getEmits, getProps } from './props';
  import { useI18n } from '/@/hooks/web/useI18n';

  const props = defineProps(getProps());
  const emit = defineEmits(getEmits());
  const { t } = useI18n();
  const startPlaceholderText = computed(() => props.startPlaceholder ?? t('common.datePicker.startDate'));
  const endPlaceholderText = computed(() => props.endPlaceholder ?? t('common.datePicker.dueDate'));

  const {
    remindRule,
    repeatRule,
    rangePresets,
    currentDateRange,
    dateIndex,
    handleChange,
    handleChangePanelRange,
    handleQuickDate,
  } = useDatePicker();

  watchEffect(() => {
    currentDateRange.value = props.value ?? [undefined, undefined];
  });
</script>
<style lang="less" scoped>
  @prefix: ~'otd-date-picker';
  .@{prefix} {
    &__overlay {
      margin: -12px;
      &__header {
        border-bottom: 1px solid var(--otd-border-color);
        padding: 8px;
        display: flex;
        position: relative;
        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          height: 24px;
          width: 1px;
          background-color: var(--otd-border-color);
          transform: translate(-50%, -50%);
        }
      }
      &__body {
        display: flex;
      }
      &__quick-date {
        display: flex;
        flex-direction: column;
        row-gap: 10px;
        width: 150px;
        padding: 10px 8px 8px;
        border-right: 1px solid var(--otd-border-color);
        background-color: var(--otd-header-bg);
        max-height: 340px;
        .quick-date-group {
          display: flex;
          flex-direction: column;
          font-size: 12px;
          margin-bottom: 0;
          flex: 1;
          .quick-date-option {
            width: 100%;
            padding: 8px;
            transition: all 0.2s;
            display: flex;
            cursor: pointer;
            align-items: center;
            justify-content: space-between;
          }
        }
      }
      &__picker {
        display: flex;
        flex-direction: column;
        row-gap: 4px;
      }
      &__panel {
        width: 310px;
        padding: 12px 16px;
      }
    }
  }
</style>
