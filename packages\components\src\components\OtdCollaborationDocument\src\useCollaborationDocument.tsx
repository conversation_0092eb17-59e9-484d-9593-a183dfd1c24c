import type { ISetTaskDocLinkInputDetail, MoreActionItem } from '/@/components';
import type { CollaborationDocumentPropsType, CollaborationDocumentEmitType } from './type';
import type { TableColumnPropsType } from '/@/components/OtdTable/src/OtdTable';
import { Input } from 'ant-design-vue';
import { OtdMoreAction } from '/@/components/OtdMoreAction';
import { OtdEditCellItem } from '/@/components/OtdEditCell';
import { getCurrentInstance, nextTick, ref, unref } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { isHttp } from '/@/tool';
import { ERROR_COLOR } from '/@/setting';

export function useCollaborationDocument() {
  const { t } = useI18n();
  const { emit, props } = getCurrentInstance() as unknown as {
    props: CollaborationDocumentPropsType;
    emit: CollaborationDocumentEmitType;
  };
  // 文档数据
  const documentData = ref<ISetTaskDocLinkInputDetail[]>([]);

  // 表格操作按钮
  const operationActions: MoreActionItem[] = [
    {
      id: 'view',
      name: t('common.openText'),
      icon: 'otd-icon-view',
      action: ({ record }) => {
        window.open(isHttp(record.docLink) ? record.docLink : 'http://' + record.docLink);
      },
    },
    {
      id: 'delete',
      name: t('common.delText'),
      color: ERROR_COLOR,
      icon: 'otd-icon-a-catdeletesize24',
      isHide: () => props.disabled,
      action: (row) => {
        emit('remove', row);
      },
    },
  ];

  // 表格列
  const columns: TableColumnPropsType[] = [
    // 文档名称
    {
      title: t('common.collaborationDocument.collaborativeName'),
      key: 'name',
      width: '250px',
      customRender: ({ record, index }) => {
        const inputRef = ref();
        if (!record.id && !record.temporaryId) nextTick(() => inputRef.value?.focus());
        return (
          <OtdEditCellItem offset={[-10, -16]}>
            <Input
              ref={inputRef}
              disabled={props.disabled}
              title={record.name}
              placeholder={t('common.collaborationDocument.collaborativeDoc')}
              defaultValue={record.name}
              onBlur={({ target }) => handleBlur(record, 'name', target.value, index)}
              onPressEnter={({ target }) => target.blur()}
            />
          </OtdEditCellItem>
        );
      },
    },
    // 文档链接
    {
      title: t('common.collaborationDocument.collaborativeLink'),
      key: 'docLink',
      ellipsis: true,
      customRender: ({ record, index }) => {
        return (
          <OtdEditCellItem class="otd-table-action" offset={[-10, -16]}>
            <Input
              class="otd-text-input focus-border"
              title={record.docLink}
              disabled={props.disabled}
              placeholder={t('common.collaborationDocument.collaborativeLink')}
              defaultValue={record.docLink}
              allowClear
              onBlur={({ target }) => handleBlur(record, 'docLink', target.value, index)}
              onPressEnter={({ target }) => target.blur()}
            />
            {record.id ? (
              <OtdMoreAction
                data={{ record, index }}
                list={unref(documentData)}
                size="large"
                hide-expand-name
                destroy-popup-on-hide
                expand-number={2}
                actions={operationActions}
              />
            ) : undefined}
          </OtdEditCellItem>
        );
      },
    },
  ];

  const handleAdd = () => {
    if (props.disabled) return;
    unref(documentData).push({ docLink: '' });
  };

  const topActions: MoreActionItem[] = [
    {
      id: 1,
      icon: 'otd-icon-a-Property128',
      name: `${t('common.collaborationDocument.collaborativeDoc')}`,
      action: () => {
        handleAdd();
      },
    },
  ];

  const handleBlur = (record: ISetTaskDocLinkInputDetail, key, value, index: number) => {
    if (!(record.id || record.temporaryId) && !value) return unref(documentData).splice(index, 1);
    if (record[key] === value) return;
    record[key] = value;
    record.temporaryId = Date.now();
    const data = unref(documentData).filter((item) => item.name || item.docLink);
    emit('update:value', data);
    emit('change', data, record);
  };
  return {
    columns,
    documentData,
    handleAdd,
    topActions,
  };
}
