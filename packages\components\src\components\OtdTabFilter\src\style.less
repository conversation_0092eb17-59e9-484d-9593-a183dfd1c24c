@name: ~'otd-tab-filter';

.@{name} {
  &.@{name}__card {
    background-color: var(--otd-basic-bg);
    border-radius: var(--otd-border-radius);
    overflow: hidden;
  }
  :deep(.ant-form) {
    > .ant-row {
      gap: 6px;
    }
    .ant-form-item {
      margin-bottom: 0;
      .ant-form-item-label {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        > label {
          display: inline;
          line-height: 32px;
        }
      }
    }
  }
  .@{name}__tabs {
    display: flex;
    align-items: center;
    width: 100%;
    &-icon {
      margin-right: 4px;
    }
    .ant-tabs {
      width: 100%;
      :deep(.ant-tabs-nav-wrap) {
        flex: unset;
        .ant-tabs-nav-list {
          padding-bottom: 2px;
          .ant-tabs-tab-btn {
            display: flex;
            align-items: center;
          }
        }
      }
      :deep(.ant-tabs-extra-content) {
        flex-grow: 1;
      }
    }

    &-add {
      padding: 8px 10px 2px 0;
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .@{name}__filter-form {
    display: flex;
    align-items: center;
    padding: 8px 10px;
  }
  .@{name}__form {
    width: 100%;
  }
  .@{name}__filter {
    flex: 1;
    display: flex;
    &-right {
      justify-content: flex-end;
    }
    :deep(.ant-form-item-control-input-content) {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    &-item {
      :deep(.otd-data-bubble) {
        padding: 7px;
        margin: -7px;
      }

      &:hover {
        background-color: var(--otd-gray-hover);
      }

      :deep(.otdIconfont) {
        display: inline-flex;
        width: 16px;
        height: 16px;
        line-height: 16px;
        align-items: center;
        justify-content: center;
      }

      :deep(.@{name}__filter-item__text) {
        margin-left: 4px;
        margin-right: 2px;
        font-size: 12px;
      }
    }
  }
}
