.@{namespace}-layout {
  width: 100%;
  height: 100%;
  display: flex;

  &__menu {
    width: 80px;
    background-color: var(--otd-basic-bg);
    text-align: center;
    display: flex;
    flex-direction: column;

    &-logo {
      padding: 32px 0 36px 0;
    }
  }

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &__header {
      padding: 10px 14px;
      display: flex;
      align-items: center;
      width: 100%;
      height: 56px;
      background-color: var(--otd-basic-bg);

      &__menu {
        flex: 1;
        margin-right: min(4.4%, 50px);
        overflow: hidden;
        width: 100px;
      }
      &__config {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }

    &__body {
      flex: 1;
      background-color: var(--otd-content-bg);
      display: flex;
      flex-direction: column;
      padding: 6px 16px 16px 16px;
      overflow: hidden;
      &-container {
        flex: 1;
        overflow: auto;
        padding-top: 10px;
      }
    }
  }
}
