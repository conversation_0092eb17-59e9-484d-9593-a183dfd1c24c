<template>
  <div style="display: inline-block; width: 400px; height: 360px">
    {{ formatToDateTime(date) }}
    <OtdPickerPanel v-model:value="date" />
  </div>
  <div style="display: inline-block; width: 400px; height: 360px; margin-left: 50px">
    {{ dateRange?.map((item) => item && formatToDateTime(item)) }}
    <OtdPickerPanel v-model:value="dateRange" is-range />
  </div>
</template>
<script lang="ts" setup>
  import { NullableDateType } from 'ant-design-vue/es/vc-picker/interface';
  import dayjs, { Dayjs } from 'dayjs';
  import { OtdPickerPanel } from '@otd/otd-ui';
  import { formatToDateTime } from 'otd-ui/src/tool';
  import { ref } from 'vue';

  const date = ref();
  const dateRange = ref<[NullableDateType<Dayjs>, NullableDateType<Dayjs>]>([dayjs('2024-6-12'), dayjs('2024-6-16')]);
  // const dateRange = ref<[NullableDateType<Dayjs>, NullableDateType<Dayjs>]>([dayjs('2024-6-12'), null]);
  // const dateRange = ref();
</script>
<style lang="less" scoped></style>
