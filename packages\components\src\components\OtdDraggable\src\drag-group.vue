<template>
  <VueDraggable
    v-model="dragList"
    :force-fallback="true"
    :itemKey="itemKey"
    class="otd-drag-group"
    :class="groupClass"
    :data-index="index"
    ghost-class="drag-ghost"
    chosen-class="drag-chosen"
    animation="300"
    draggable=".drag-item"
    :handle="handle ? `.${handle}` : undefined"
    filter=".drag-filter"
    :preventOnFilter="false"
    v-bind="$attrs"
    :move="onMove"
    @start="onStart"
    @end="onEnd"
    @add="onAdd"
  >
    <template #item="data">
      <div class="drag-item" :class="[getDisabled(data.element) ? 'drag-filter' : '', itemClass || '']">
        <slot name="item" v-bind="data"></slot>
      </div>
    </template>
    <template #header>
      <slot name="header"></slot>
    </template>
    <template #footer>
      <slot name="footer"></slot>
    </template>
  </VueDraggable>
</template>
<script lang="ts" setup>
  import { ref, unref, watch } from 'vue';
  import VueDraggable from 'vuedraggable';
  const props = defineProps({
    itemKey: {
      type: String,
      default: 'id',
    },
    list: {
      type: Array,
      default: () => [],
    },
    itemClass: {
      type: String,
      default: undefined,
    },
    groupClass: {
      type: String,
      default: undefined,
    },
    index: {
      type: Number,
      default: undefined,
    },
    handle: {
      type: String,
      default: undefined,
    },
    disabledMove: {
      type: Function,
      default: undefined,
    },
  });

  const dragList = ref();
  watch(
    () => props.list,
    (value) => {
      dragList.value = value;
    },
    { deep: true, immediate: true },
  );

  const emit = defineEmits(['update:list', 'start', 'end']);

  function getDisabled(data) {
    return data.disabledMove || data.isAdd || data.isEdit;
  }

  // 开始移动
  function onStart(data) {
    emit('start', data);
  }
  // 结束移动
  function onEnd(data) {
    emit('update:list', unref(dragList));
    emit('end', data, { preIndex: props.index });
  }
  // 从一个数组拖拽到另外一个数组时触发的事件
  function onAdd() {
    emit('update:list', unref(dragList));
  }
  // 移动判断
  function onMove(e) {
    if (props.disabledMove) {
      return props.disabledMove(e);
    }
    //不允许停靠
    if (e.relatedContext.element?.disabledPark == true) return false;
    return true;
  }
</script>
<style lang="less" scoped>
  .otd-drag-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;
    padding-top: 1px;
    .drag-item {
      &.drag-ghost {
        background-color: var(--otd-create-background);
      }
      &.drag-chosen {
        background-color: var(--otd-basic-bg);
        pointer-events: auto;
        cursor: move;
      }
    }
  }
</style>
