<template>
  {{ valueIcon }}
  <OtdIconSelect v-model:value="valueIcon" />

  <OtdTaskType v-model:value="value" title="标题" help="111111" :options="TaskTypeOptions" @change="handleChange">
    <!-- <template #title>1111</template> -->
    <template #other>1111</template>
  </OtdTaskType>
  <OtdTaskType mode="select" v-model:value="value" title="标题" :options="TaskTypeOptions" />
  <OtdTaskType mode="icon" v-model:value="value" title="标题" :options="TaskTypeOptions" />
</template>
<script lang="ts" setup>
  import { OtdIconSelect, OtdTaskType, useTaskTypeDictionary } from '@otd/otd-ui';
  import { ref } from 'vue';

  const value = ref();
  const valueIcon = ref();
  const { TaskTypeOptions } = useTaskTypeDictionary();

  function handleChange(value) {
    console.log(value);

    valueIcon.value = value.icon;
  }
</script>
<style lang="less" scoped></style>
