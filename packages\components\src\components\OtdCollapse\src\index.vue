<template>
  <div class="otd-collapse" :style="{ '--space': `${space}px` }">
    <slot></slot>
  </div>
</template>
<script lang="ts" setup>
  import { computed, PropType, unref } from 'vue';
  import { setCollapseConfig } from './useCollapse';

  const props = defineProps({
    activeKey: {
      type: Array as PropType<(string | number)[]>,
    },
    space: {
      type: Number,
      default: 16,
    },
    accordion: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:activeKey']);
  const currentActive = computed({
    get: () => props.activeKey ?? [],
    set: (value) => emit('update:activeKey', value),
  });

  setCollapseConfig({
    activeKey: currentActive,
    setPanelActive: (value) => {
      if (props.accordion) return (currentActive.value = [value]);
      const actives = unref(currentActive);
      const index = actives.findIndex((key) => key === value);
      index >= 0 ? actives.splice(index, 1) : actives.push(value);
    },
  });
</script>
<style lang="less" scoped>
  .otd-collapse {
    display: flex;
    flex-direction: column;
    row-gap: var(--space);
    min-width: 100%;
  }
</style>
