## 日期选择

<demo src="../demo/DatePicker/basic.vue" title="日期选择"></demo>

## 属性

| 参数             | 说明               | 类型              | 可选值 | 默认值  | 版本 |
| ---------------- | ------------------ | ----------------- | ------ | ------- | ---- |
| value            | 日期范围           | [Dayjs, Dayjs]    | -      |         | 1.0  |
| remind           | 到期提醒           | String            | -      | -       | 1.0  |
| repeat           | 重复设置           | String            | -      | -       | 1.0  |
| placeholder      | 占位文本           | String            | -      | -       | 1.0  |
| startPlaceholder | 开始日期占位文本   | String            | -      | -       | 1.0  |
| endPlaceholder   | 截止日期占位文本   | String            | -      | -       | 1.0  |
| disabled         | 禁用               | Boolean           | -      | `false` | 1.0  |
| notTipColor      | 提示颜色           | [Boolean, Object] | -      | `false` | 1.0  |
| hideRemind       | 隐藏到期提醒       | Boolean           | -      | `false` | 1.0  |
| hideRepeat       | 隐藏重复设置       | Boolean           | -      | `false` | 1.0  |
| hideStartTime    | 隐藏开始时间文本   | Boolean           | -      | `false` | 1.0  |
| hideEndTime      | 隐藏结束始时间文本 | Boolean           | -      | `false` | 1.0  |
