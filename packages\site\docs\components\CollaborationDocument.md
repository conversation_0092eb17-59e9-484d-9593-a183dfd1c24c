# 协作文档

## 基础

<demo src="../demo/CollaborationDocument/basic.vue" title="基础"></demo>

## 属性

### <span id='options'>options</span>

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| tableColumn | 表格列配置 参考[表格](/components/Table.html)组件 | -- | -- | -- | 1.0 |
| allocation | 表格属性配置 参考[表格](/components/Table.html)组件 | {} | -- | -- | 1.0 |
| value(v-model) | 表格数据源 | [] | -- | -- | 1.0 |
| placeholder | 无数据时的 placeholder | string | -- | 添加 | 1.0 |
| disabled | 是否禁用 | Boolean | -- | false | 1.0 |
| remove | 删除事件 | ({record, index}) => {} | -- | -- | 1.0 |
| change | 改变事件 | (list, record) => {} | -- | -- | 1.0 |
