/**
 * Independent time operation tool to facilitate subsequent switch to dayjs
 */
import dayjs, { Dayjs } from 'dayjs';
import { NOTDATA } from '/@/settings/const';
import { Recordable } from '/#/global';
import { NullableDateType } from 'ant-design-vue/es/vc-picker/interface';
import { useI18n } from '/@/hooks/web/useI18n';

export const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
export const DATE_TIME = 'YYYY-MM-DD HH:mm';
export const DATE_FORMAT = 'YYYY-MM-DD';

export function formatToDateTime(
  date: dayjs.Dayjs | string | undefined = undefined,
  format = DATE_TIME_FORMAT,
): string {
  return dayjs(date)?.format(format);
}

export function formatToDate(date: dayjs.Dayjs | string | undefined = undefined, format = DATE_FORMAT): string {
  return dayjs(date).format(format);
}

export function formatToTime(date: dayjs.Dayjs | string | undefined = undefined, format = DATE_TIME): string {
  return date ? dayjs(date).format(format) : NOTDATA;
}

let dayDate: Dayjs | null = dayjs().add(1, 'day');
let DateTextMap = {};
function setDateTextMap() {
  if (dayDate?.isToday() && Object.values(DateTextMap).length > 0) return;
  const { t } = useI18n();
  dayDate = dayjs();
  const Today = dayDate.format('YYYY-MM-DD');
  const Yesterday = dayDate.add(-1, 'day').format('YYYY-MM-DD');
  const Tomorrow = dayDate.add(1, 'day').format('YYYY-MM-DD');
  DateTextMap = {
    [Yesterday]: t('common.datePicker.Yesterday'),
    [Today]: t('common.datePicker.Today'),
    [Tomorrow]: t('common.datePicker.Tomorrow'),
  };
}
export function formatDateToText(date?: dayjs.Dayjs | string) {
  let format = date as string;
  if (dayjs.isDayjs(date)) {
    format = date.format('YYYY-MM-DD');
  }
  setDateTextMap();
  return DateTextMap[format] ? DateTextMap[format] : format;
}

export const dateUtil = dayjs;

export function extractLevel<T = Recordable>(data, level: number, key: string = 'children', current = 1) {
  const result: T[] = [];
  data.map((item) => {
    const child = { ...item };
    if (current <= level) {
      if (child[key] && current < level) {
        child[key] = extractLevel(child[key], level, key, current + 1);
      } else if (child[key] && current === level) {
        child[key] = undefined;
      }
      result.push(child);
    }
  });
  return result.length > 0 ? result : undefined;
}

// 深度map
export function mapDeep(data: any[], handler: (item: Recordable) => Recordable | void, key: string = 'children') {
  data.map((item) => {
    handler(item);
    if (item[key]) {
      mapDeep(item[key], handler, key);
    }
  });
}

// 判断日期先后
export function judgeDateBefore(dates: [NullableDateType<Dayjs>, NullableDateType<Dayjs>]): typeof dates {
  if (!dates) return [null, null];
  const [start, end] = dates;
  if (!start || !end) {
    return [start, end];
  }
  if (start?.isAfter(end)) {
    return [end, start];
  }
  return [start, end];
}
// 判断时间否在大于 00:00;
export function isToTime(date: Dayjs) {
  const hours = date.hour();
  const minutes = date.minute();
  if (hours > 0 || minutes > 0) {
    return true;
  }
  return false;
}

/**
 * 转换时间为服务器时间，避免出现时区问题
 * @param date
 * @returns
 */
export function conversionServerTime(date: Dayjs) {
  return date ? date?.format('YYYY-MM-DDTHH:mm:ss') : undefined;
}

/**
 * 判断时间是否符合今年今日
 * @param time
 */
export function judgCurrentTime(time, format = {}) {
  const timeFormat = {
    year: 'MM/DD HH:mm:ss',
    today: 'HH:mm:ss',
    other: 'YYYY/MM/DD HH:mm:ss',
    ...format,
  };
  const now = dayjs(); // 获取当前时间
  const year = now.year(); // 获取当前年份
  const today = now.startOf('day'); // 获取今天的开始时间
  const date = dayjs(time);
  const isThisYearToday = date.isSame(today, 'day') && date.year() === year;
  if (isThisYearToday) {
    // 同一天
    return timeFormat.today ? date.format(timeFormat.today) : '';
  } else if (date.year() === year) {
    // 同一年
    return timeFormat.year ? date.format(timeFormat.year) : '';
  } else {
    return timeFormat.other ? date.format(timeFormat.other) : '';
  }
}

/**
 * 判断时间格式是否存在时分秒
 * @param dateString 时间格式时间(YYYY-MM-DD HH:mm:ss)
 */
export function hasTimeFormat(dateString: string) {
  const timeRegex = /\d{2}:\d{2}(:\d{2})?$/;
  return timeRegex.test(dateString.replace(/\s/g, ''));
}

/**
 * 获取指定时间的下一个整点时间，比如：13:50得到14:00,13:10得到13:30
 * @param date 指定时间
 * @returns
 */
export function getTopHourTime(date?: Date): Dayjs {
  const now = dayjs(date);
  const currentMinute = now.minute();

  let nextHour = now.hour();
  let nextMinute = 0;
  if (currentMinute > 30) {
    // 如果当前分钟数大于等于30，下一个整时为当前小时+1
    nextHour += 1;
  } else {
    // 否则，下一个整时为当前小时的30整
    nextMinute = 30;
  }
  // 设置下一个整时的分钟数为0
  return now.set('hour', nextHour).set('minute', nextMinute).set('second', 0);
}
