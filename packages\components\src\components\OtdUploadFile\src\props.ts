import { Mutable } from '@vueuse/core';
import { PropType } from 'vue';
import { TableColumnPropsType, UploadFileType } from '/@/components';

export const getProps = () => ({
  // 文件列表
  fileList: {
    type: Array as PropType<UploadFileType[]>,
    default: () => [],
  },
  placeholder: {
    type: String,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  // 表格列
  tableColumn: {
    type: Array as PropType<TableColumnPropsType[]>,
    default: [],
  },
  // 表格属性
  allocation: {
    type: Object as PropType<Record<string, any>>,
    default: {},
  },
  // 上传附件
  uploadFile: {
    type: Function as PropType<(data?) => Promise<any>>,
    default: () => {},
  },
  // 删除附件
  removeFile: {
    type: Function as PropType<(data?, list?) => Promise<any>>,
  },
  // 预览附件
  previewFile: {
    type: Function as PropType<(data?) => Promise<any>>,
  },
  // 附件属性
  uploadProps: {
    type: Object,
    default: {},
  },
  headerText: {
    type: String,
  },
});

const emit = ['change', 'remove', 'update:fileList'] as const;
export const getEmits = () => emit as Mutable<typeof emit>;
