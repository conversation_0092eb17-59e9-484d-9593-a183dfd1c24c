<template>
  <div class="otd-custom-filter">
    <Popover
      v-model:open="visible"
      :arrow="false"
      :align="PublicAlignConfig"
      trigger="click"
      :z-index="10"
      placement="bottomRight"
      overlayClassName="otd-popover hide-arrow"
    >
      <div class="otd-filter-placeholder placeholder-hover" @click="handleShow">
        <i class="otdIconfont" :class="[props.icon]"></i>
        <div class="placeholder-text otd-custom-filter__title">
          <span v-if="props.placeholder">{{ props.placeholder }}</span>
          <span v-show="filterContentValue.data[0]?.value"> / {{ filterContentValue.data.length }}</span>
        </div>
      </div>
      <template #content>
        <div class="otd-custom-filter__content">
          <!-- 筛选标题 -->
          <div class="otd-custom-filter__content__header">
            <h1 class="otd-custom-filter__content__header__title">{{ t('common.seniorFilter.advancedFilters') }}</h1>
            <div class="otd-custom-filter__content__header__action-box">
              <!-- 操作 -->
              <div class="ootd-custom-filter__content__header__action-box__action">
                <span class="placeholder-hover" @click="handleClearAll()">
                  {{ t('common.seniorFilter.clearAll') }}
                </span>
              </div>
              <!--   保存筛选结果    -->
              <SaveFilter
                v-if="props.isShowSaveFilter"
                v-bind="props"
                v-model:value="modelValue"
                ref="SaveFilterRef"
                @item-click="handleFilterItemClick"
              />
            </div>
          </div>
          <div class="otd-custom-filter__content__body">
            <!-- 筛选条件内容 -->
            <div class="otd-custom-filter__content__body__filter">
              <OtdScrollbar>
                <div
                  class="otd-custom-filter__content__body__filter__item"
                  v-for="(item, index) in filterContentValue.data"
                  :key="item.id"
                >
                  <div class="otd-custom-filter__content__body__filter__item__logic">
                    <span v-if="index === 0">Where</span>
                    <Select
                      v-else
                      class="otd-select"
                      v-model:value="filterContentValue.logic"
                      :options="logicOptions"
                    />
                  </div>
                  <div class="otd-custom-filter__content__body__filter__item__components">
                    <!-- 字段 -->
                    <Select
                      class="otd-select otd-custom-filter__content__body__filter__item__components__key"
                      v-model:value="item.field"
                      :placeholder="t('common.seniorFilter.field')"
                      :options="props.options"
                      optionFilterProp="label"
                      label-in-value
                      show-search
                      @select="handelSelectField(item)"
                    />
                    <!-- 条件 -->
                    <Select
                      v-if="item.field"
                      v-model:value="item.condition"
                      class="otd-select otd-custom-filter__content__body__filter__item__components__value"
                      :options="getOptions(item, 'conditions', defaultCondition)"
                      :placeholder="t('common.seniorFilter.condition')"
                      :notFoundContent="t('common.chooseText') + t('common.seniorFilter.field')"
                      optionFilterProp="label"
                      show-search
                      @select="handelSelectCondition(item)"
                    />
                    <!-- 值 -->
                    <div class="otd-custom-filter__content__body__filter__item__components__custom" v-if="item.field">
                      <GetComponent
                        :item="item"
                        :mode="getModeContent(item, getOptions(item, 'conditions', defaultCondition))"
                        @handler="handleChangeFilter(item)"
                      />
                    </div>
                  </div>
                  <!-- 删除 -->
                  <span
                    class="placeholder-hover otd-custom-filter__content__body__filter__del-icon"
                    @click="handelDeleteFilter(index, item)"
                  >
                    <i class="otdIconfont otd-icon-a-catdeletesize24"></i>
                  </span>
                </div>
              </OtdScrollbar>
            </div>
            <div class="otd-custom-filter__content__body__add otd-box-left">
              <!-- 新增筛选条件 -->
              <span class="placeholder-hover" @click="handleAddNewFilter">
                <i class="otdIconfont otd-icon-add-2"></i>
                {{ t('common.seniorFilter.addNewFilter') }}
              </span>
              <span class="placeholder-hover otd-primary-color" v-if="props.isShowSaveFilter" @click="handleSaveFilter">
                <SaveFilled />
                {{ t('common.seniorFilter.saveFilter') }}
              </span>
            </div>
          </div>
        </div>
      </template>
    </Popover>
  </div>
</template>
<script lang="tsx" setup>
  import { ref } from 'vue';
  import { Popover, Select } from 'ant-design-vue';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { getProps } from './props';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { SaveFilter } from './components/SaveFilter';
  import { useCustomFilter } from './useCustomFilter';
  import { PublicAlignConfig } from '/@/setting';
  import { SaveFilled } from '@ant-design/icons-vue';

  const props = defineProps(getProps());

  const {
    filterContentValue,
    defaultCondition,
    modelValue,
    SaveFilterRef,
    handleClearAll,
    handleAddNewFilter,
    handleSaveFilter,
    handelDeleteFilter,
    handelSelectField,
    GetComponent,
    handleChangeFilter,
    handelSelectCondition,
    handleFilterItemClick,
    getModeContent,
    getOptions,
  } = useCustomFilter();
  const { t } = useI18n();
  const visible = ref(false);
  const logicOptions = [{ label: 'And', value: 'and' }];

  // 打开筛选气泡
  function handleShow() {
    visible.value = true;
  }

  defineExpose({
    getDefaultValue: () => modelValue.value,
    resetFilter: () =>
      new Promise((resolve) => {
        handleClearAll(false);
        resolve(true);
      }),
  });
</script>
<style lang="less" scoped>
  .otd-custom-filter {
    &__title {
      margin-bottom: 0;
      font-size: 14px;
      :first-child {
        margin-left: 4px;
      }
    }
    .otd-filter-placeholder {
      line-height: 20px;
      &.ant-popover-open {
        background-color: var(--otd-gray3-hover);
      }
    }
    &__content {
      min-width: 600px;
      &__header {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &__title {
          margin-bottom: 0;
        }
        &__action-box {
          display: flex;
          align-items: center;
        }
      }
      &__body {
        &__filter {
          :deep(.scrollbar__wrap) {
            max-height: 300px;
            .scrollbar__view {
              display: flex;
              flex-direction: column;
              gap: 10px;
            }
          }
          &__item {
            display: flex;
            gap: 10px;
            align-items: center;
            &__logic {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 70px;
              .otd-select {
                width: 100%;
              }
            }
            &__components {
              min-width: 520px;
              display: flex;
              gap: 10px;
              &__key {
                width: 140px;
              }
              &__value {
                width: 120px;
              }
              &__custom {
                display: flex;
                align-items: center;
                flex: 1;
              }
            }
          }
          &__del-icon {
            display: flex;
            align-items: center;
            color: var(--otd-error-color);
            height: fit-content;
          }
        }
        &__add {
          margin-top: 10px;
          justify-content: space-between;
          i {
            font-size: 12px;
          }
        }
      }
    }
  }

  .otd-popover {
    padding-top: 0;
  }

  :deep(.placeholder-hover) {
    .otdIconfont {
      margin-right: 0;
    }
  }

  .w-240px {
    width: 240px;
  }
</style>
