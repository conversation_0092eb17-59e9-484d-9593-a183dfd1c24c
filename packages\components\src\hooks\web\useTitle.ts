import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
import { watch, unref, ref } from 'vue';
import { useTitle as usePageTitle } from '@vueuse/core';
import { RouteLocationNormalizedLoaded, useRouter } from 'vue-router';
import { REDIRECT_NAME } from '/@/setting';

/**
 * Listening to page changes and dynamically changing site titles
 */
export function useTitle(useI18n: () => { t: (data) => string }, appName?: string) {
  const { getGlobalProvide } = useGlobalConfig();
  const { appTitle } = getGlobalProvide();
  const title = appName || appTitle;
  const { t } = useI18n();
  const { currentRoute = ref({ path: '', name: '' } as RouteLocationNormalizedLoaded) } = useRouter() ?? {};

  const pageTitle = usePageTitle();

  watch(
    () => currentRoute.value.path,
    () => {
      const route = unref(currentRoute);

      if (route.name === REDIRECT_NAME) {
        return;
      }

      const tTitle = t(route?.meta?.title as string);
      if (tTitle || title) {
        pageTitle.value = tTitle ? ` ${tTitle} - ${title} ` : `${title}`;
      }
    },
    { immediate: true },
  );
}
