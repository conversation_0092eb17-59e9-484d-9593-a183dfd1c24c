## 队列

<demo src="../demo/LineUp/basic.vue" title="队列"></demo>

## 属性

| 参数         | 说明                   | 类型                                   | 可选值 | 默认值  | 版本 |
| ------------ | ---------------------- | -------------------------------------- | ------ | ------- | ---- |
| value        | 当前选中队列           | Array                                  | --     | --      | 1.0  |
| placeholder  | 占位提示               | String                                 | --     | --      | 1.0  |
| clearable    | 清除                   | Boolean                                | --     | `false` | 1.0  |
| beforeChange | 事件 change 之前的钩子 | (selected, data) => Promise\<boolean\> | --     | --      | 1.0  |

## 事件

| 事件名称   | 说明                       | 回调参数                 | 版本 |
| ---------- | -------------------------- | ------------------------ | ---- |
| change     | 选中 option 时，调用此函数 | (selected, data) => void | 1.0  |
| click-item | 点击选中项时回调           | (data) => void           | 1.0  |
