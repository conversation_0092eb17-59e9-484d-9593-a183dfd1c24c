import * as signalR from '@microsoft/signalr';
import { useMessage } from '/@/hooks/web/useMessage';
import { unref } from 'vue';
import { NotifyCountType, useNotifyStorage } from '/@/storage/notifyStorage';
import { MessageType } from '/@/components';
export type SignalRQueryType = {
  socketUrl?: string;
  token?: string;
  logout?: () => Promise<any>;
  handler?: (message: string, status: boolean) => void;
};
export const MessageKeyMap: Record<MessageType, keyof NotifyCountType> = {
  [MessageType.BroadCast]: 'broadcastCount',
  [MessageType.Common]: 'commonCount',
  [MessageType.Custom]: 'customCount',
};
let connection: signalR.HubConnection;

export function OtdSignalConfig(url: string, token: string, logout: SignalRQueryType['logout']) {
  const connect = new signalR.HubConnectionBuilder()
    .withUrl(url, {
      accessTokenFactory: () => token,
      skipNegotiation: true,
      transport: signalR.HttpTransportType.WebSockets,
    })
    .withAutomaticReconnect({
      nextRetryDelayInMilliseconds: (retryContext) => {
        // 重连规则：重连次数 < 3：间隔1s; 重试次数 < 10: 间隔3s; 重试次数 > 3000: 间隔30s
        const count = retryContext.previousRetryCount;
        if (count < 3) {
          // 重试次数 < 3: 间隔1s
          return 1000;
        } else if (count < 10) {
          // 重试次数 < 10: 间隔5s
          return 1000 * 5;
        } else {
          // 重试次数 > 10: 退出登录
          connect.stop();
          logout?.();
          return 1000 * 30;
        }
      },
    })
    .configureLogging(signalR.LogLevel.Debug)
    .build();

  return connect;
}

export function useSignalR({ socketUrl, token, logout }: SignalRQueryType = {}) {
  const { getNotifyCount, setNotifyCount } = useNotifyStorage();
  /**
   * 开始连接SignalR
   */
  async function startConnect() {
    if (!socketUrl || !token) return;
    try {
      connectionsignalR();
      await connection.start();
    } catch (err) {
      console.log(err);
      setTimeout(() => startConnect(), 5000);
    }
  }

  /**
   * 关闭SignalR连接
   */
  function closeConnect(): void {
    connection?.stop?.();
  }

  async function connectionsignalR() {
    if (!socketUrl || !token) return;
    connection = OtdSignalConfig(socketUrl, token, logout);
    // 接收普通文本消息
    connection.on('ReceiveTextMessageAsync', ReceiveTextMessageHandlerAsync);
    // 接收广播消息
    connection.on('ReceiveBroadCastMessageAsync', ReceiveBroadCastMessageHandlerAsync);
    // 接收自定义消息
    connection.on('ReceiveCustomMessageAsync', ReceiveCustomHandlerAsync);
  }

  function UnifiedProcessNotify(key) {
    return new Promise((resolve) => {
      setNotifyCount(key, unref(getNotifyCount)[key] + 1);
      const dom = document.querySelector('#notifyTip');
      dom?.classList.add('is-flash');
      resolve(null);
    });
  }

  /**
   * 接收文本消息
   * @param message 消息体
   */
  function ReceiveTextMessageHandlerAsync(message: any) {
    UnifiedProcessNotify(MessageKeyMap[MessageType.Common]).then(() => {
      const { notification } = useMessage();
      if (message.messageLevel == 10) {
        notification.warn({
          message: message.title,
          description: <div innerHTML={message.content}></div>,
        });
      } else if (message.messageLevel == 20) {
        notification.info({
          message: message.title,
          description: <div innerHTML={message.content}></div>,
        });
      } else if (message.messageLevel == 30) {
        notification.error({
          message: message.title,
          description: <div innerHTML={message.content}></div>,
        });
      } else {
        notification.info({
          message: message.title,
          description: <div innerHTML={message.content}></div>,
        });
      }
    });
  }

  /**
   * 接收广播消息
   * @param message 消息体
   */
  function ReceiveBroadCastMessageHandlerAsync(message: any) {
    UnifiedProcessNotify(MessageKeyMap[MessageType.BroadCast]).then(() => {
      const { notification } = useMessage();
      if (message.messageLevel == 10) {
        notification.warn({
          message: message.title,
          description: <div innerHTML={message.content}></div>,
        });
      } else if (message.messageLevel == 20) {
        notification.info({
          message: message.title,
          description: <div innerHTML={message.content}></div>,
        });
      } else if (message.messageLevel == 30) {
        notification.error({
          message: message.title,
          description: <div innerHTML={message.content}></div>,
        });
      } else {
        notification.info({
          message: message.title,
          description: <div innerHTML={message.content}></div>,
        });
      }
    });
  }

  /**
   * 接收自定义消息
   * @param message 消息体
   */
  function ReceiveCustomHandlerAsync(message) {
    UnifiedProcessNotify(MessageKeyMap[MessageType.Custom]).then(() => {
      console.log(message);
    });
  }

  return { startConnect, closeConnect };
}
