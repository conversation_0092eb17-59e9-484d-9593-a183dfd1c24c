<template>
  <div class="otd-data-bubble otd-user-search__trigger" :class="triggerClx" data-bubble>
    <div class="user-trigger__box" :class="['placeholder-hover', triggerClass]">
      <!-- 未选择 -->
      <template v-if="!isSelectUser">
        <Tooltip :title="placeholderText" :visible="bordered ? false : undefined">
          <div class="user-trigger__placeholder" :class="[`icon-${size}`]">
            <i class="otdIconfont" :class="icon" v-if="!isText"></i>
            <span class="user-trigger__placeholder-text" v-if="isShowText">
              {{ placeholderText }}
            </span>
          </div>
        </Tooltip>
      </template>
      <!-- 选中内容 -->
      <div class="otd-user-search__trigger__container" v-else>
        <Tooltip :title="userNameText || placeholderText">
          <div class="selected-group" :class="[`icon-${size}`]">
            <div class="selected-item" v-for="item in currentUser.show" :key="item.value" :title="item.label">
              <OtdAvatar :size="imgSize[size]" :url="item.img" v-if="!hideAvatar || isMultiple" />
              <span v-if="isShowText && (!isMultiple || currentUser.show.length === 1)">{{ item.label }}</span>
              <!-- 若是多选，但如果只有一个值，且允许显示，则显示label -->
            </div>
            <div class="selected-item more-select" v-if="currentUser.hide.length > 0">
              {{ currentUser.hide.length > 99 ? '99+' : `+${currentUser.hide.length}` }}
            </div>
          </div>
        </Tooltip>
        <Tooltip :title="t('common.delText')" v-if="clearable && !disabled && isSelectUser">
          <CloseCircleFilled class="otd-action-clearable" @click.stop="handleClear" />
        </Tooltip>
      </div>
      <i class="otdIconfont otd-icon-jt" v-if="showArrow" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { Tooltip } from 'ant-design-vue';
  import { computed, PropType } from 'vue';
  import { UserOptionItemType } from '../type';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getProps } from '../props';
  import { OtdAvatar } from '/@/components/OtdAvatar';
  import { CloseCircleFilled } from '@ant-design/icons-vue';

  const props = defineProps({
    selectUser: {
      type: Array as PropType<UserOptionItemType[]>,
      default: () => [],
    },
    ...getProps(),
  });

  const emit = defineEmits(['clear']);

  const { t } = useI18n();
  const isShowText = computed(() => {
    return props.showText || props.isText;
  });
  const isMultiple = computed(() => props.mode === 'multiple');

  const isSelectUser = computed(() => props.selectUser.length > 0);

  const triggerClx = computed(() => {
    return {
      'otd-text-clearable__label': props.isText,
      'otd-arrow__label': props.showArrow,
      'is-border': props.bordered,
      'placeholder-disabled': props.disabled,
      'otd-action-clearable__label': props.clearable,
    };
  });

  // 用户名字
  const userNameText = computed(() => props.selectUser.map((item) => item.label)?.join(' , '));

  const placeholderText = computed(() => props.placeholder || t('common.chooseText'));

  const currentUser = computed((): { show: UserOptionItemType[]; hide: UserOptionItemType[] } => {
    if (props.mode) {
      return {
        show: props.selectUser?.slice(0, props.maxShowUser) || [],
        hide: props.selectUser?.slice(props.maxShowUser) || [],
      };
    }

    return {
      show: props.selectUser,
      hide: [],
    };
  });

  const imgSize = { large: '35px', middle: '32px', small: '25px', mini: '22px', default: '28px' };

  function handleClear() {
    emit('clear');
  }
</script>
<style lang="less" scoped>
  @user-search: ~'@{namespace}-user-search';
  .@{user-search}__trigger {
    width: fit-content;
    display: flex;
    align-items: center;
    &.is-border {
      border: 1px solid var(--otd-border-gray);
      border-radius: var(--otd-default-radius);
      // width: 100%;
      height: 100%;
      cursor: pointer;
      &:hover,
      &.ant-popover-open {
        border-color: var(--otd-primary-main);
      }
      .user-trigger__box {
        background-color: transparent !important;
        flex-direction: row;
      }
    }

    &.otd-arrow__label {
      .user-trigger__box {
        padding: 0 10px 0 6px;
        border-radius: var(--otd-border-radius);
        .otd-icon-jt {
          transform-origin: center;
          transform: rotate(90deg);
          transition: transform 0.3s;
          font-size: 14px;
          text-shadow: 0 0 0 var(--otd-basic-text);
        }
      }
    }

    &.ant-popover-open {
      .user-trigger__box {
        background-color: var(--otd-gray3-hover);
        .otd-icon-jt {
          transform: rotate(-90deg);
        }
      }
      .is-ipt-style {
        border: 1px solid #2765f5 !important;
      }
    }
    .user-trigger {
      &__box {
        display: flex;
        align-items: center;
        border-radius: var(--otd-large-radius);
        padding: 2px;

        .not-data {
          margin: 0 auto;
          color: var(--otd-gray3-color);
        }
      }
      &__placeholder {
        display: flex;
        justify-content: center;
        column-gap: 6px;
        cursor: pointer;
        padding: 0 4px;
        &-text {
          white-space: nowrap;
          color: var(--otd-gray4-color);
        }
      }
    }
    .selected-group {
      display: inline-flex;
      vertical-align: middle;
      align-items: center;
      position: relative;
      .user-name {
        margin-left: 6px;
        display: inline-block;
        max-width: 160px;
        margin-right: 6px;
      }

      .selected-item {
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid var(--otd-white-background);
        border-radius: var(--otd-large-radius);
        // background-color: var(--otd-border-gray);
        & + .selected-item {
          margin-left: -8px;
        }
        > span {
          margin-left: 6px;
          padding-right: 4px;
          font-weight: 500;
        }
        &.more-select {
          background-color: #585858 !important;
          color: var(--otd-white-text);
          overflow: hidden;
          > img {
            border-radius: 50%;
          }
        }
      }
    }
  }
  .icon-large {
    line-height: 35px;
    min-width: 35px;
    font-size: 18px;
    i {
      font-size: 18px;
    }
    .more-select {
      width: 35px;
      height: 35px;
    }
  }
  .icon-middle {
    line-height: 32px;
    min-width: 32px;
    font-size: 16px;
    i {
      font-size: 18px;
    }
    .more-select {
      width: 32px;
      height: 32px;
    }
  }

  .icon-small {
    line-height: 25px;
    min-width: 25px;
    font-size: 14px;
    i {
      font-size: 12px;
    }
    .more-select {
      width: 25px;
      height: 25px;
    }
  }
  .icon-mini {
    line-height: 22px;
    min-width: 22px;
    font-size: 14px;
    i {
      font-size: 12px;
    }
    .more-select {
      width: 22px;
      height: 22px;
    }
  }

  .icon-default {
    line-height: 28px;
    min-width: 28px;
    font-size: 14px;
    i {
      font-size: 16px;
    }
    .more-select {
      width: 28px;
      height: 28px;
    }
  }

  .icon-medium {
    line-height: 26px;
    min-width: 26px;
    font-size: 14px;
    i {
      font-size: 16px;
    }
    .more-select {
      width: 26px;
      height: 26px;
    }
  }
</style>
