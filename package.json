{"name": "@otd/otd-ui", "version": "1.5.30", "description": "OTD组件库", "main": "./dist/src/index.js", "types": "./dist/src/index.d.ts", "scripts": {"build": "pnpm --filter otd-ui build", "start": "pnpm --filter otd-ui start", "docs:start": "pnpm run --filter site start", "docs:build": "pnpm run --filter site build", "pub": "pnpm build && pnpm otd:publish", "otd:publish": "pnpm publish --registry http://odin.otdmes.com.cn:60037/ --no-git-checks --filter @otd/otd-ui", "otd:unpublish": "npm unpublish --force @otd/otd-ui --registry http://odin.otdmes.com.cn:60037/", "test": "echo \"Error: no test specified\" && exit 1"}, "files": ["dist"], "keywords": [], "author": "limin", "license": "ISC", "devDependencies": {"@types/node": "^20.9.0", "@types/qrcode": "^1.5.5", "typescript": "^5.0.2", "vite": "^3.2.5", "vite-plugin-dts": "^3.8.3", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.4.23"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@icon-park/vue-next": "^1.4.2", "@microsoft/signalr": "^8.0.0", "@npkg/tinymce-plugins": "^0.0.7", "@vitejs/plugin-vue": "^3.0.3", "@vitejs/plugin-vue-jsx": "^2.1.1", "@vue/runtime-core": "^3.4.26", "@vue/shared": "^3.4.26", "@vueuse/core": "^10.9.0", "@zxcvbn-ts/core": "^3.0.4", "ant-design-vue": "^4.2.1", "cropperjs": "^1.5.13", "cross-env": "^7.0.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "driver.js": "^1.3.1", "echarts": "^5.5.0", "jszip": "^3.10.1", "less": "^4.2.0", "lodash-es": "^4.17.21", "qrcode": "^1.5.4", "resize-observer-polyfill": "^1.5.1", "rrule": "^2.8.1", "sortablejs": "^1.15.0", "tinymce": "5.10.3", "v-viewer": "3.0.11", "viewerjs": "^1.11.6", "vue-i18n": "^9.10.2", "vue-router": "^4.3.2", "vue-types": "^5.1.1", "vue3-colorpicker": "^2.2.2", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}}