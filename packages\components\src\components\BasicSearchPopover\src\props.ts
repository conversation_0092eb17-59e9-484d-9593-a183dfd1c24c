import { TooltipPlacement } from 'ant-design-vue/es/tooltip';
import { PropType } from 'vue';
import { SearchOptionType } from './type';
import { mutable } from '/@/utils/props';
import { QueryType, Recordable } from '/#/global';

export const getProps = <T = SearchOptionType>() => ({
  value: {
    type: [Object, Array] as PropType<T | T[]>,
  },
  clearable: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: [String, Boolean] as PropType<'multiple' | false>,
    default: false,
  },
  // 弹出位置
  placement: {
    type: String as PropType<TooltipPlacement>,
    default: 'bottomLeft',
  },
  // 搜索文本
  searchText: {
    type: String,
  },
  // 容器文本
  containerText: {
    type: String,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 远程搜索方法
  remoteMethod: {
    type: Function as PropType<(data?: QueryType) => Promise<SearchOptionType[]>>,
  },
  remoteQuery: {
    type: Object as PropType<Recordable>,
  },
  immediate: {
    type: Boolean,
    default: true,
  },
  width: {
    type: String,
    default: '240px',
  },
  beforeChange: {
    type: Function as PropType<(selected, data) => Promise<boolean>>,
  },
});
const emit = ['update:value', 'clear', 'change'] as const;
export const getEmits = () => mutable(emit);
