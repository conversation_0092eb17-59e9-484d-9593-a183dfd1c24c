import { PropType } from 'vue';
import { UserOptionItemType } from '/@/components/OtdUserSearch/src/type';
import { getProps as getTinyProps } from '../../props';
import { AtMentionEnum } from '../../plugins/mention/MentionPopover/useMentionPopover';
import { mutable } from '/@/tool';

export const getProps = () => ({
  value: {
    type: String,
  },
  detail: {
    type: Object,
    default: () => ({}),
  },
  defaultValue: {
    type: String,
    default: '',
  },
  leftToolbar: {
    type: String,
    default: '',
  },
  rightToolbar: {
    type: String,
    default: '',
  },
  getMentionsRequest: {
    type: Function as PropType<
      (id: string, filter: { query?: string; tab: AtMentionEnum; keyword?: string }) => Promise<UserOptionItemType[]>
    >,
  },
  ...getTinyProps(true),
});

const emit = [
  'update:modelValue',
  'update:value',
  'change',
  'uploadImage',
  'uploadAttachment',
  'blur',
  'defaultValue',
] as const;
export const getEmits = () => mutable(emit);
