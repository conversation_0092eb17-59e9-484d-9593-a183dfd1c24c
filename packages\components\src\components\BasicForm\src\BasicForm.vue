<template>
  <OtdEditCell
    v-bind="getBindValue"
    :class="getFormClass"
    ref="formEditRef"
    :model="formModel"
    :name="name"
    @keypress.enter="handleEnterPress"
  >
    <Row v-bind="getRow">
      <slot name="formHeader"></slot>
      <template v-for="schema in getSchema" :key="schema.field">
        <FormItem
          :tableAction="tableAction"
          :formActionType="formActionType"
          :schema="schema"
          :formProps="getProps"
          :allDefaultValues="defaultValueRef"
          :formModel="formModel"
          :setFormModel="setFormModel"
          :autoSubmitOnChange="getProps.autoSubmitOnChange"
          :stopInputSubmitOnChange="getProps.stopInputSubmitOnChange"
          @submit="handleSubmit"
        >
          <template #[item]="data" v-for="item in Object.keys($slots)">
            <slot :name="item" v-bind="data || {}"></slot>
          </template>
        </FormItem>
      </template>
      <slot></slot>

      <FormAction v-bind="getFormActionBindProps" @toggle-advanced="handleToggleAdvanced">
        <template #[item]="data" v-for="item in ['resetBefore', 'submitBefore', 'advanceBefore', 'advanceAfter']">
          <slot :name="item" v-bind="data || {}"></slot>
        </template>
      </FormAction>
      <slot name="formFooter"></slot>
    </Row>
  </OtdEditCell>
</template>
<script lang="ts" setup name="BasicForm">
  import type { FormActionType, BasicFormProps, FormSchema } from './types/form';
  import type { AdvanceState } from './types/hooks';
  import { ComponentInternalInstance, getCurrentInstance, Ref } from 'vue';

  import { reactive, ref, computed, unref, onMounted, watch, nextTick } from 'vue';
  import { Recordable, Nullable } from '/#/global';
  import { Row } from 'ant-design-vue';
  import FormItem from './components/FormItem.vue';
  import FormAction from './components/FormAction.vue';

  import { dateItemType } from './helper';
  import { dateUtil } from '/@/utils/dateUtil';

  import { deepMerge } from '/@/utils';

  import { useFormValues } from './hooks/useFormValues';
  import useAdvanced from './hooks/useAdvanced';
  import { useFormEvents } from './hooks/useFormEvents';
  import { createFormContext } from './hooks/useFormContext';
  import { useAutoFocus } from './hooks/useAutoFocus';
  import { useModalContext } from '/@/components/BasicModal';
  import { useDebounceFn } from '@vueuse/core';

  import { basicProps } from './props';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { cloneDeep } from 'lodash-es';
  import { OtdEditCell } from '/@/components/OtdEditCell';

  const props = defineProps(basicProps());
  const emit = defineEmits(['advanced-change', 'reset', 'submit', 'register', 'field-value-change']);
  const { attrs } = getCurrentInstance() as ComponentInternalInstance;

  const formModel = reactive<Recordable>({});
  const modalFn = useModalContext();

  const advanceState = reactive<AdvanceState>({
    isAdvanced: true,
    hideAdvanceBtn: false,
    isLoad: false,
    actionSpan: 6,
  });

  const defaultValueRef = ref<Recordable>({});
  const isInitedDefaultRef = ref(false);
  const propsRef = ref<Partial<BasicFormProps>>({});
  const schemaRef = ref<Nullable<FormSchema[]>>(null);
  const formEditRef = ref<Nullable<{ getForm: () => FormActionType }>>(null);
  const formElRef = computed<Nullable<FormActionType>>(() => formEditRef.value?.getForm() ?? null);

  const { prefixCls } = useDesign('basic-form');

  // Get the basic configuration of the form
  const getProps = computed((): BasicFormProps => {
    return Object.assign({}, props, unref(propsRef)) as BasicFormProps;
  });

  const getFormClass = computed(() => {
    return [
      prefixCls,
      {
        [`${prefixCls}--compact`]: unref(getProps).compact,
      },
    ];
  });

  // Get uniform row style and Row configuration for the entire form
  const getRow = computed((): Recordable => {
    const { baseRowStyle = {}, rowProps } = unref(getProps);
    return {
      style: baseRowStyle,
      ...rowProps,
    };
  });

  const getBindValue = computed(
    () =>
      ({
        ...attrs,
        ...props,
        ...unref(getProps),
      } as Recordable),
  );

  const getSchema = computed((): FormSchema[] => {
    const schemas: FormSchema[] = unref(schemaRef) || (unref(getProps).schemas as any);
    for (const schema of schemas) {
      const { defaultValue, component } = schema;
      // handle date type
      if (defaultValue && dateItemType.includes(component)) {
        if (!Array.isArray(defaultValue)) {
          schema.defaultValue = dateUtil(defaultValue);
        } else {
          const def: any[] = [];
          defaultValue.forEach((item) => {
            def.push(dateUtil(item));
          });
          schema.defaultValue = def;
        }
      }
    }
    if (unref(getProps).showAdvancedButton) {
      return cloneDeep(schemas.filter((schema) => schema.component !== 'Divider') as FormSchema[]);
    } else {
      return cloneDeep(schemas as FormSchema[]);
    }
  });

  const { handleToggleAdvanced } = useAdvanced({
    advanceState,
    emit,
    getProps,
    getSchema,
    formModel,
    defaultValueRef,
  });

  const { handleFormValues, initDefault } = useFormValues({
    getProps,
    defaultValueRef,
    getSchema,
    formModel,
  });

  useAutoFocus({
    getSchema,
    getProps,
    isInitedDefault: isInitedDefaultRef,
    formElRef: formElRef as Ref<FormActionType>,
  });

  const {
    handleSubmit,
    setFieldsValue,
    clearValidate,
    validate,
    validateFields,
    getFieldsValue,
    updateSchema,
    resetSchema,
    appendSchemaByField,
    removeSchemaByFiled,
    resetFields,
    scrollToField,
  } = useFormEvents({
    emit,
    getProps,
    formModel,
    getSchema,
    defaultValueRef,
    formElRef: formElRef as Ref<FormActionType>,
    schemaRef: schemaRef as Ref<FormSchema[]>,
    handleFormValues,
  });

  createFormContext({
    resetAction: resetFields,
    submitAction: handleSubmit,
  });

  watch(
    () => unref(getProps).model,
    () => {
      const { model } = unref(getProps);
      if (!model) return;
      setFieldsValue(model, getSchema.value);
    },
    {
      immediate: true,
    },
  );

  watch(
    () => unref(getProps).schemas,
    (schemas) => {
      resetSchema(schemas ?? []);
    },
  );

  watch(
    () => getSchema.value,
    (schema) => {
      nextTick(() => {
        //  Solve the problem of modal adaptive height calculation when the form is placed in the modal
        modalFn?.redoModalHeight?.();
      });
      if (unref(isInitedDefaultRef)) {
        return;
      }
      if (schema?.length) {
        initDefault();
        isInitedDefaultRef.value = true;
      }
    },
  );

  watch(
    () => formModel,
    useDebounceFn(() => {
      unref(getProps).submitOnChange && handleSubmit();
    }, 300),
    { deep: true },
  );

  async function setProps(formProps: Partial<BasicFormProps>): Promise<void> {
    propsRef.value = deepMerge(unref(propsRef) || {}, formProps);
  }

  function setFormModel(key: string, value: any) {
    formModel[key] = value;
    const { validateTrigger } = unref(getBindValue);
    if (!validateTrigger || validateTrigger === 'change') {
      validateFields([key]).catch((_) => {});
    }
    emit('field-value-change', key, value);
  }

  function handleEnterPress(e: KeyboardEvent) {
    const { autoSubmitOnEnter } = unref(getProps);
    if (!autoSubmitOnEnter) return;
    if (e.key === 'Enter' && e.target && e.target instanceof HTMLElement) {
      const target: HTMLElement = e.target as HTMLElement;
      if (target && target.tagName && target.tagName.toUpperCase() == 'INPUT') {
        handleSubmit();
      }
    }
  }

  const formActionType: FormActionType = {
    getFieldsValue,
    setFieldsValue: setFieldsValue as (values) => Promise<void>,
    resetFields,
    updateSchema,
    resetSchema,
    setProps,
    removeSchemaByFiled,
    appendSchemaByField,
    clearValidate,
    validateFields,
    validate,
    submit: handleSubmit,
    scrollToField: scrollToField,
  };

  onMounted(() => {
    initDefault();
    emit('register', formActionType);
  });
  const getFormActionBindProps = computed((): Recordable => ({ ...getProps.value, ...advanceState }));

  defineExpose({
    getBindValue,
    handleToggleAdvanced,
    handleEnterPress,
    formModel,
    defaultValueRef,
    advanceState,
    getRow,
    getProps,
    formElRef,
    getSchema,
    formActionType: formActionType as any,
    setFormModel,
    getFormClass,
    getFormActionBindProps: unref(getFormActionBindProps),
    ...formActionType,
  });
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-basic-form';

  .@{prefix-cls} {
    :deep(.ant-form-item) {
      margin-bottom: 20px;
      .ant-form-item-required {
        flex-direction: row-reverse;
        column-gap: 4px;
      }
      // &.suffix-item {
      //   .ant-form-item-children {
      //     display: flex;
      //   }

      //   .ant-form-item-control {
      //     margin-top: 4px;
      //   }

      //   .suffix {
      //     display: inline-flex;
      //     padding-left: 6px;
      //     margin-top: 1px;
      //     line-height: 1;
      //     align-items: center;
      //   }
      // }

      .ant-form-item-explain-error {
        display: flex;
        &::before {
          content: '\e66f';
          font-family: otdIconfont;
          color: var(--otd-error-color);
          margin-right: 4px;
          font-size: 16px;
        }
        color: var(--otd-icon-text);
        line-height: 20px;
      }
    }

    .ant-form-explain {
      font-size: 14px;
    }

    &--compact {
      .ant-form-item {
        margin-bottom: 8px !important;
      }
    }
  }
</style>
