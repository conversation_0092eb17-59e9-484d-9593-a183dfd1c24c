import { computed, reactive, unref } from 'vue';

export type NotifyCountType = {
  broadcastCount?: number;
  commonCount?: number;
  customCount?: number;
  count?: number;
};

const notifyCount = reactive<NotifyCountType>({
  broadcastCount: 0,
  commonCount: 0,
  customCount: 0,
  count: 0,
});
export function useNotifyStorage() {
  return {
    getNotifyCount: computed(() => unref(notifyCount)),
    getNotifyTotal: computed(() => {
      const { broadcastCount = 0, commonCount = 0, customCount = 0 } = unref(notifyCount);
      return broadcastCount + commonCount + customCount;
    }),
    setNotify(countConfig: NotifyCountType) {
      Object.assign(notifyCount, countConfig);
    },
    setNotifyCount(key: keyof NotifyCountType, count: number) {
      notifyCount[key] = count;
    },
  };
}
