<template>
  <OtdMoreAction
    :data="props.item"
    :list="props.list"
    size="large"
    :expand-number="2"
    :actions="Actions"
    hide-expand-name
  />
  <br />
  <OtdMoreAction
    :data="props.item"
    :list="props.list"
    size="medium"
    :expand-number="2"
    :actions="Actions"
    hide-expand-name
  />
  <br />
  <OtdMoreAction :data="props.item" :list="props.list" :expand-number="2" :actions="Actions" hide-expand-name />
  <br />
  <OtdMoreAction
    :data="props.item"
    :list="props.list"
    size="small"
    :expand-number="2"
    :actions="Actions"
    hide-expand-name
  />
  <br />
  <OtdMoreAction
    :data="props.item"
    :list="props.list"
    size="mini"
    :expand-number="2"
    :actions="Actions"
    hide-expand-name
  />
</template>
<script lang="tsx" setup>
  import { OtdMoreAction, MoreActionItem } from '@otd/otd-ui';
  import { defineProps } from 'vue';
  import { message } from 'ant-design-vue';

  const props = defineProps({
    item: {
      type: Object,
      default: {},
    },
    index: {
      type: Number,
      default: -1,
    },
    list: {
      type: Array,
      default: () => [],
    },
    hideAction: {
      type: Boolean,
      default: false,
    },
  });

  // 操作
  const Actions: MoreActionItem[] = [
    {
      id: 0,
      icon: 'otd-icon-a-cateditsize24',
      color: 'blue',
      name: '编辑',

      customRender: () => {
        return (
          <>
            <i class="otdIconfont otd-icon-a-cateditsize24"></i>
            <span class="add-icon__text">编辑</span>
          </>
        );
      },
      action: () => {
        message.info('编辑');
      },
    },
    {
      id: 1,
      icon: 'otd-icon-a-Property128',
      name: '新增',
      action: () => {
        message.info('新增');
      },
    },
    {
      id: 2,
      customRender: () => {
        return (
          <>
            <i class="otdIconfont otd-icon-a-cattagsize24"></i>
            <span>标签</span>
          </>
        );
      },
      // name: "标签",
      action: () => {
        message.info('标签');
      },
    },
    {
      id: 3,
      name: '删除',
      icon: 'otd-icon-a-catdeletesize24',
      color: 'red',
      action: () => {
        message.info('删除');
      },
    },
    {
      id: 4,
      name: '禁用',
      icon: 'otd-icon-a-catlocksize24',
      disabled: () => true,
    },
  ];
</script>
<style lang="less" scoped></style>
