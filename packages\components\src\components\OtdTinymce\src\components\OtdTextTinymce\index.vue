<template>
  <div class="otd-description-editor">
    <div
      v-show="!(isEdit || isToEdit) && !description"
      class="placeholder-hover"
      :class="{ 'is-disabled': disabled }"
      @click="handleEditDesc"
    >
      <span class="placeholder-text">
        {{ placeholderText }}
      </span>
    </div>
    <div
      class="otd-description-editor__text otd-tiny-description"
      v-html="description"
      @click="handleEditDesc"
      v-if="!(isEdit || isToEdit) && description"
    ></div>
    <OtdSignleTinymce
      ref="signleEditor"
      v-bind="getComProps"
      :placeholder="placeholderText"
      @blur="handleBlur"
      v-else-if="isEdit || isToEdit"
    />
  </div>
</template>
<script lang="ts" setup>
  import { computed, ref, unref, useAttrs } from 'vue';
  import tinymce from 'tinymce/tinymce';
  import OtdSignleTinymce from '../OtdSignleTinymce/index.vue';
  import { getProps } from '../../props';
  import { useI18n } from '/@/hooks/web/useI18n';

  const props = defineProps({
    description: {
      type: String,
      default: undefined,
    },
    isToEdit: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
    },
    ...getProps(true),
  });
  const { t } = useI18n();

  const attrs = useAttrs();
  const getComProps = computed(() => ({
    ...attrs,
    ...props,
  }));
  const placeholderText = computed(() => (props.placeholder as unknown as string) || t('common.inputText'));

  const emit = defineEmits(['blur']);
  const isEdit = ref(false);
  const signleEditor = ref();

  function handleEditDesc() {
    if (props.disabled) return false;
    isEdit.value = true;
    setTimeout(() => {
      const id = signleEditor.value.getEditorId();
      tinymce.get(id).focus();
    }, 300);
  }

  function handleBlur(data, notChange) {
    isEdit.value = false;
    if (!notChange) {
      emit('blur', data);
    }
  }

  defineExpose({
    getText: () => unref(signleEditor).getText(),
  });
</script>
<style lang="less" scoped>
  .otd-description-editor {
    white-space: initial;
  }
</style>
