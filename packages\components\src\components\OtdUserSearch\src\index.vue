<template>
  <BasicSearchPopover v-bind="getBindValue" v-model:value="modelValue" :remote-method="getRemoteMethod">
    <template #default="{ selectMap, clear }">
      <UserTrigger :select-user="Array.from(selectMap.values())" v-bind="$props" @clear="clear" />
    </template>
    <template #item="{ item }">
      <UserOptionItem :item="item" />
    </template>
  </BasicSearchPopover>
</template>
<script lang="ts" setup>
  import { useUserSearch } from './useUserSearch';
  import { getEmits, getProps } from './props';
  import UserOptionItem from './components/UserOptionItem.vue';
  import UserTrigger from './components/UserTrigger.vue';
  import { BasicSearchPopover } from '/@/components/BasicSearchPopover';
  import { computed, useAttrs } from 'vue';

  const props = defineProps(getProps());
  const emit = defineEmits(getEmits());
  const attrs = useAttrs();

  const { getRemoteMethod } = useUserSearch();
  const modelValue = computed({
    get: () => props.value,
    set: (value) => emit('update:value', value),
  });
  const getBindValue = computed(() => ({
    ...props,
    ...attrs,
  }));
</script>
<style lang="less" scoped>
  @import './style.less';
</style>
