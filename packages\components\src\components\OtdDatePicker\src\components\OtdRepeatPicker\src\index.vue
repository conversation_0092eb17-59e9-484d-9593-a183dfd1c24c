<template>
  <div class="otd-repeat-picker">
    <i class="otdIconfont otd-icon-caozuo-xunhuan1"></i>
    <Select
      class="otd-select otd-noborder-select"
      :placeholder="t('common.datePicker.RepeatSetup')"
      :bordered="false"
      :show-arrow="false"
      :dropdownStyle="{ minWidth: '200px' }"
      :options="pickerOptions"
      placement="topLeft"
      allow-clear
      :open="selectVisible"
      v-bind="$attrs"
      @select="handleSelect"
      @change="handleChange"
      @dropdown-visible-change="(open) => (selectVisible = open)"
    >
      <template #dropdownRender="data">
        <VNodes :vnodes="data.menuNode" />
        <button class="otd-noborder-select__custom" @click="handleSelectCustom">
          {{ t('common.repeatPicker.custom') }}
        </button>
      </template>
    </Select>
    <RepeatModal @register="resigerModal" @ok="handleConfirm" />
  </div>
</template>
<script lang="ts" setup>
  import type { DateValueType } from '/@/components/OtdDatePicker/src/type';
  import { useAttrs, watchEffect, type PropType } from 'vue';
  import { Select } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useRepeatPicker } from './useRepeatPicker';
  import RepeatModal from './RepeatModal.vue';
  import { VNodes } from '/@/components/OtdDatePicker/src/useDatePicker';
  import { RRule } from 'rrule';

  const { t } = useI18n();

  defineProps({
    dates: {
      type: Array as unknown as PropType<[DateValueType, DateValueType]>,
    },
  });
  defineEmits(['change']);

  const [
    resigerModal,
    { pickerOptions, selectVisible, handleSelectCustom, handleSelect, handleChange, handleConfirm, getRepeatText },
  ] = useRepeatPicker();
  const attrs = useAttrs();
  watchEffect(() => {
    if (!attrs.value && pickerOptions?.[4]) {
      pickerOptions?.splice(4, 1);
    }
    if (attrs.value && !pickerOptions.find((item) => item.value === attrs.value)) {
      pickerOptions[4] = {
        value: attrs.value as string,
        label: getRepeatText(new RRule(RRule.fromString(attrs.value as string).origOptions)),
      };
    }
  });
</script>
<style lang="less" scoped>
  .otd-repeat-picker {
    display: flex;
    align-items: center;
    column-gap: 2px;
    line-height: 1;
    .otd-noborder-select {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
