<template>
  <div class="otd-layout__horizontal-menu">
    <OtdMenu
      v-if="currentMenu.length > 0"
      v-model:selectedKeys="selectedKeys"
      v-bind="$attrs"
      :items="currentMenu"
      mode="horizontal"
      show-title
    />
  </div>
</template>
<script lang="ts" setup>
  import { computed, PropType } from 'vue';
  import { MenuType, OtdMenu } from '/@/components/OtdMenu';

  const props = defineProps({
    items: {
      type: Array as PropType<MenuType[]>,
      default: () => [],
    },
    selectedKeys: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    menuMap: {
      type: Object as PropType<Map<string, any>>,
      default: () => new Map(),
    },
  });
  const emit = defineEmits(['update:selectedKeys']);

  const currentMenu = computed(() => {
    const first = props.selectedKeys[0];
    return props.menuMap.get(first)?.children ?? [];
  });
  const selectedSet = new Set<string>(props.selectedKeys);
  const selectedKeys = computed({
    get() {
      return props.selectedKeys.slice(1);
    },
    set(value) {
      selectedSet.clear();
      selectedSet.add(props.selectedKeys[0]);
      value.map((item) => selectedSet.add(item));
      emit('update:selectedKeys', Array.from(selectedSet));
    },
  });
</script>
<style lang="less" scoped>
  .otd-layout__horizontal-menu {
    width: 100%;
    :deep(.ant-menu) {
      position: relative;
      &.ant-menu-horizontal {
        border-bottom-width: 0;
        line-height: 36px;
        .ant-menu-submenu {
          top: 0;
          margin-top: 0;
        }
      }
      .ant-menu-overflow-item {
        & + .ant-menu-overflow-item {
          margin-left: 10px;
        }
        font-size: 16px;
        padding: 0 8px;
        &::after {
          height: 4px;
          border-bottom-width: 0;
          inset-inline: 8px;
          border-radius: var(--otd-mini-radius);
        }
      }
      .ant-menu-submenu-selected,
      .ant-menu-item-selected {
        .otd-menu-item-content,
        .ant-menu-submenu-arrow {
          color: var(--otd-primary-color);
        }
      }
      .ant-menu-submenu-selected,
      .ant-menu-item-selected,
      .ant-menu-submenu:hover,
      .ant-menu-item:hover {
        &::after {
          background-color: var(--otd-primary-color);
          height: 4px;
        }
      }
      .ant-menu-submenu-title {
        padding-right: 14px;
      }
      .ant-menu-submenu-arrow {
        display: inline;
        right: 0;
        color: var(--otd-gray3-color);
        &::after,
        &::before {
          content: unset;
        }
        &::before {
          display: inline-block;
          font-family: otdIconfont;
          content: '\e64b';
          position: unset;
          transform: unset;
          background-color: transparent;
          font-style: normal;
          font-size: 12px;
          transform: scale(0.8) translateY(4px);
        }
        // right: -12px;
      }
      .otd-menu-item-content {
        color: var(--otd-gray3-color);
      }
      .ant-menu-overflow-item-rest {
        color: var(--otd-icon-text);
        position: absolute;
        right: 0;
        z-index: 2;
        margin-left: 0;
        padding-left: 20px;
        .ant-menu-submenu-title {
          padding-right: 0;
        }
        .anticon-ellipsis {
          &::before {
            display: inline-flex;
            font-family: otdIconfont !important;
            transform: rotate(90deg) translateX(-4px);
            content: '\e65d';
            font-weight: bold;
          }
          > svg {
            display: none;
          }
        }
        .ant-menu-submenu-arrow {
          display: none;
        }
        &::after {
          inset-inline-start: 22px;
        }
      }
    }
  }
</style>
