import { unref } from 'vue';
import { useRootSetting } from '/@/storage/projectConfigStorage';
import {
  updateColorWeak,
  updateDarkTheme,
  updateGrayMode,
} from '/@/components/OtdLayout/src/components/OtdProjectConfig/src/handler';

export function initConfig() {
  const setting = useRootSetting();
  const { getGrayMode, getColorWeak, getDarkMode } = setting;
  const grayMode = unref(getGrayMode),
    colorWeak = unref(getColorWeak),
    darkMode = unref(getDarkMode);

  grayMode && updateGrayMode(grayMode);
  colorWeak && updateColorWeak(colorWeak);
  darkMode && updateDarkTheme(darkMode);
}
