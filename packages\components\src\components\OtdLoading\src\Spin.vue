<template>
  <Spin :class="{ 'inline-spin': inline }" v-bind="$attrs">
    <template v-for="(_, name) in $slots" :slot="name">
      <slot :name="name" />
    </template>
    <template #indicator>
      <img :src="loadingSrc" :style="{ width: width ? width + 'px' : undefined, height: 'auto' }" />
    </template>
  </Spin>
</template>
<script lang="ts" setup>
  import { Spin } from 'ant-design-vue';
  import { loadingSrc } from '/@/settings';

  defineProps({
    width: {
      type: Number,
      default: undefined,
    },
    inline: {
      type: Boolean,
      default: false,
    },
  });
</script>
<style lang="less" scoped>
  .inline-spin {
    display: flex;
    align-items: center;
    column-gap: 6px;
  }
</style>
