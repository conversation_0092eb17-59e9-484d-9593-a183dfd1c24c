<template>
  <OtdVirtualScroll
    class="otd-icon-grid"
    :items="IconMapKeys"
    :item-height="34"
    :height="height"
    :max-height="maxHeight"
    :bench="4"
  >
    <template #default="{ item }">
      <div class="otd-icon-grid-box">
        <template v-for="record in item" :key="record.value">
          <div
            class="otd-icon-grid-box__item"
            :class="{ 'is-active': value === record.value }"
            @click="iconClick(record.value)"
          >
            <OtdIconPark :type="record.value" theme="filled" />
          </div>
        </template>
      </div>
    </template>
  </OtdVirtualScroll>
</template>
<script lang="ts" setup>
  import OtdIconPark from './IconPark.vue';

  import { OtdVirtualScroll } from '/@/components/OtdVirtualScroll';
  import { chunk } from 'lodash-es';
  import { computed, unref } from 'vue';
  import { IconsType } from '../../type';
  import { IconMapData } from './IconMap';

  const props = defineProps({
    value: {
      type: String,
    },
    keyword: {
      type: String,
      default: '',
    },
    height: {
      type: Number,
    },
    maxHeight: {
      type: Number,
    },
  });

  const emit = defineEmits(['icon-click']);

  const IconMapKeys = computed(() =>
    chunk(
      unref(IconMapData).filter(({ value }) => value.toLocaleLowerCase().includes(props.keyword.toLocaleLowerCase())),
      8,
    ),
  );

  function iconClick(icon: IconsType) {
    emit('icon-click', icon);
  }
</script>
<style lang="less" scoped>
  .otd-icon-grid {
    &-box {
      display: grid;
      width: fit-content;
      grid-template-columns: repeat(8, 1fr);
      gap: 2px;
      overflow: auto;
      padding: 0 6px;
      color: var(--otd-basic-text);
      &__item {
        width: fit-content;
        padding: 6px;
        border-radius: var(--otd-small-radius);
        cursor: pointer;
        font-size: 20px;
        line-height: 1;
        &.is-active,
        &:hover {
          background-color: var(--otd-gray3-hover);
        }
        &.is-active {
          color: var(--otd-menu-text);
        }
      }
    }
  }
</style>
