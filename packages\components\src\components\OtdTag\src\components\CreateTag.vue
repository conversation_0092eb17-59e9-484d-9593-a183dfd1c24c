<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div class="otd-tags__create">
    <div class="otd-tags__create-item">
      <div class="otd-tags-content otd-truncate mr-10px">
        {{ t('common.tag.create') }}
        <div class="flex flex-1 otd-truncate">
          <div class="otd-tags-content__item" :style="getTagNameStyle">
            <span class="otd-truncate">{{ data.tagName }}</span>
          </div>
        </div>
      </div>
      <OtdColorPicker :data="data" />
    </div>
    <div class="otd-tags__create-item">
      <div class="otd-tags__create-item__label">
        <i class="otdIconfont otd-icon-a-catlocksize24"></i>
        <span>{{ t('common.tag.private') }}</span>
      </div>
      <Switch
        v-model:checked="data.tagAuth"
        size="small"
        :unCheckedValue="TagAuthEnum.Public"
        :checkedValue="TagAuthEnum.Private"
      />
    </div>
    <Button size="small" class="mt-6px" @click="handleCreateTag">
      {{ t('common.tag.create') }}
    </Button>
  </div>
</template>
<script lang="ts" setup>
  import { computed, PropType } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { Switch, Button } from 'ant-design-vue';
  import { OtdColorPicker } from '/@/components/OtdColor';
  import { watch } from 'vue';
  import { TagAuthEnum, ICreateTagInput, getLightColor, colorNameToHex } from '../types';
  import { CSSProperties } from 'vue';

  const props = defineProps({
    data: {
      type: Object as PropType<ICreateTagInput>,
      default: () => ({}),
    },
    defaultValue: {
      type: Number as PropType<TagAuthEnum>,
      default: TagAuthEnum.Public,
    },
  });
  const getTagNameStyle = computed((): CSSProperties => {
    props.data.color = colorNameToHex(props.data.color);
    return {
      // 以选中色为文字色，背景变浅
      backgroundColor: getLightColor(props.data.color, 0.8),
      color: props.data.color,
      fontWeight: 500,
    };
  });
  const emit = defineEmits(['create']);

  const { t } = useI18n();

  // 创建标签
  function handleCreateTag() {
    emit('create', props.data);
  }

  watch(
    () => props.defaultValue,
    (value) => {
      if ((value as any) === -1) {
        // eslint-disable-next-line vue/no-mutating-props
        props.data.tagAuth = TagAuthEnum.Public;
      } else {
        // eslint-disable-next-line vue/no-mutating-props
        props.data.tagAuth = value;
      }
    },
    { immediate: true },
  );
</script>
<style lang="less" scoped>
  .otd-tags__create {
    // margin-top: 10px;
    text-align: right;
    padding: 10px;
    .otd-tags-content__item {
      padding-left: 8px;
      padding-right: 8px;
      color: var(--otd-white-text);
    }
    &-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 24px;
      height: 24px;
      &__label {
        display: flex;
        align-items: center;
        i {
          margin-top: 2px;
        }
      }
      & + & {
        margin-top: 6px;
      }
    }
  }
</style>
