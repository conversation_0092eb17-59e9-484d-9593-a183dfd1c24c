<template>
  <div class="otd-emoticon">
    <template v-for="item in getEmoticons" :key="item">
      <Tooltip :title="t(`common.emoticons.${item.name}`)">
        <div class="otd-emoticon-item">
          <!-- <BasicIcon :icon="`${item}|svg`" :size="30" /> -->
          <component :is="item.comp" @click="handleSelect(item.name)"></component>
        </div>
      </Tooltip>
    </template>
  </div>
</template>
<script lang="ts" setup>
  import { computed } from 'vue';
  // import { BasicIcon } from '/@/components/BasicIcon';
  import { Tooltip } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const emit = defineEmits(['select']);
  const emoticons = import.meta.globEager('./Emoticons/*.vue');
  // const emoticons = import.meta.globEager('../../../assets/images/emoticon/*.svg');

  const getEmoticons = computed(() => {
    return Object.entries(emoticons).map(([dir, comp]: [string, any]) => ({
      name: dir
        .split('/')
        .pop()!
        .match(/([\S\s]+)\.vue/)![1],
      comp: comp.default,
    }));
  });
  //const getEmoticons = Object.keys(emoticons).map(
  //   (item) =>
  //     item
  //       .split('/')
  //       .pop()!
  //       .match(/([\S\s]+)\.svg/)![1],
  // ),

  function handleSelect(item) {
    emit('select', item);
  }
</script>
<style lang="less" scoped>
  .otd-emoticon {
    display: grid;
    grid-template-columns: repeat(5, 30px);
    grid-gap: 6px;

    &-item {
      line-height: 1;
      cursor: pointer;
      &:hover {
        :deep(.otd-emoticon-icon) {
          transform: scale(1.2);
        }
      }
      :deep(.otd-emoticon-icon) {
        transition: 0.3s transform;
        outline: unset;
      }
    }
  }
</style>
