<template>
  <div>
    <!-- 日期 -->
    <h5>日期单选</h5>
    禁用<Switch v-model:checked="checked" />
    <br />
    <OtdDatePicker
      v-model:value="dateArr1"
      v-model:remind="rulePicker.remind"
      v-model:repeat="rulePicker.repeat"
      :disabled="checked"
      :notTipColor="{ start: false, end: false }"
      placeholder="请选择日期"
      @change="handleChange"
    />
    <OtdDatePicker
      v-model:value="dateArr1"
      v-model:remind="rulePicker.remind"
      v-model:repeat="rulePicker.repeat"
      :disabled="checked"
      :notTipColor="{ start: false, end: false }"
      hideStartTime
      is-simple
      @change="handleChange"
    />
  </div>
</template>
<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { OtdDatePicker, Switch } from '@otd/otd-ui';
  const dateArr1 = ref();
  const checked = ref<boolean>(false);
  const rulePicker = reactive({
    remind: undefined,
    repeat: undefined,
  });

  function handleChange(date, rules) {
    console.log(date, rules);
  }
</script>
<style lang="less" scoped></style>
