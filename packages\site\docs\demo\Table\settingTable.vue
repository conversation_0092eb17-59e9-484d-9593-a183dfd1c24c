<template>
  <div style="height: 600px">
    <OtdTable @register="register" </OtdTable>
  </div>
</template>
<script lang="tsx" setup>
  import { OtdTable, TableColumnPropsType, FormSchema, useTable } from '@otd/otd-ui';
  import { reactive } from 'vue';

  const searchFormSchema: FormSchema[] = [
    {
      field: 'filter',
      label: '关键字',
      component: 'Input',
    },
  ];
  const tableColumn: TableColumnPropsType[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      fixed: 'left',
      width: '100px',
      sorter: true,
      key: 'name',
      customRender: () => {
        return <div>123</div>;
      },
    },
    {
      title: '姓名',
      dataIndex: 'name2',
      fixed: 'left',
      width: '100px',
      key: 'name2',
      sorter: true,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: '1200px',
      align: 'center',
      key: 'age',
    },
    {
      title: '住址',
      // fixed: "right",
      dataIndex: 'address',
      width: '200px',
      key: 'address',
    },
    {
      title: '住址',
      fixed: 'right',
      dataIndex: 'address2',
      width: '200px',
      key: 'address2',
      defaultHidden: true,
    },
  ];

  function remoteRequest(params) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const dataSource = reactive(
          new Array(10).fill(0).map((_, i) => ({
            key: i,
            name: '胡彦斌',
            name2: '胡彦斌',
            age: 32,
            address: '西湖区湖底公园1号',
            address2: '西湖区湖底公园1号',
            children: new Array(1).fill(0).map((_, index) => ({
              key: `${i}-${index}`,
              name: '胡彦斌',
              age: 32,
              address: '西湖区湖底公园2号',
            })),
          })),
        );
        resolve({ items: dataSource, totalCount: 100 });
      }, 1000);
    });
  }

  const [register] = useTable({
    columns: tableColumn,
    canResize: true,
    showIndexColumn: true,
    indexColumnProps: {
      width: '100px',
    },
    showTableSetting: true,
    formConfig: {
      schemas: searchFormSchema,
    },
    remoteRequest: remoteRequest,
  });
</script>
<style lang="less" scoped></style>
