import { PropType } from 'vue';
import { mutable } from '/@/tool';
import { SearchOptionType } from '/@/components/BasicSearchPopover';

export const getProps = <T = SearchOptionType>() => ({
  value: {
    type: Array as PropType<T[]>,
  },
  placeholder: {
    type: String,
  },
  clearable: {
    type: Boolean,
    default: false,
  },
  beforeChange: {
    type: Function as PropType<(selected, data) => Promise<boolean>>,
  },
});

const emit = ['update:value', 'click-item', 'change'] as const;

export const getEmits = () => mutable(emit);
