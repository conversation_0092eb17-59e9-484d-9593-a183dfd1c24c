<template>
  <Popover
    v-model:open="getVisible"
    overlay-class-name="otd-popover otd-search-popover"
    trigger="click"
    :arrow="false"
    :placement="placement"
    :overlay-inner-style="{ width: width }"
    v-bind="$attrs"
    @open-change="handleOpenPopover"
  >
    <slot :selectMap="searchSelectMap" :clear="handleClear" :cancelItem="cancelItem"></slot>
    <template #content>
      <div class="otd-search-popover">
        <!-- 搜索容器 start -->
        <div class="otd-popover__search">
          <Input
            ref="InputRef"
            v-model:value="searchData.keyword"
            :bordered="false"
            :placeholder="getSearchText"
            allowClear
            @change="handleSearch"
          >
            <template #prefix> <i class="otdIconfont otd-icon-sousuo" /> </template>
          </Input>
        </div>
        <!-- 搜索容器 end -->
        <!-- 容器 start -->
        <div class="otd-popover__content" v-loading="searchData.fetching" :loading-tip="getSearchingText">
          <slot name="content" :keyword="searchData.keyword" :closePopover="closePopover">
            <OtdScrollbar>
              <!-- 已选择 -->
              <div class="otd-search-popover__content__selected" v-if="isSelect">
                <div class="otd-search-popover__content-title otd-selete-title">
                  {{ t('common.userSearch.selected') }}
                </div>
                <ul class="otd-search-popover__group">
                  <template v-for="([_, item], index) in searchSelectMap" :key="item.value">
                    <SearchOptionItem
                      :item="item"
                      :index="index"
                      :select-item="clearable ? cancelItem : undefined"
                      :cancel-item="cancelItem"
                      :show-cancel="clearable"
                    >
                      <slot name="item" :index="index" :item="item" />
                    </SearchOptionItem>
                  </template>
                </ul>
              </div>
              <!-- 建议 -->
              <div
                class="otd-search-popover__content__suggestion"
                v-if="searchData.data.length > 0 || searchData.fetching"
              >
                <div class="otd-search-popover__content-title otd-selete-title">
                  {{ getContainerText }}
                </div>
                <ul class="otd-search-popover__group">
                  <template v-for="(item, index) in searchData.data" :key="item.value">
                    <SearchOptionItem :index="index" :item="item" :select-item="selectItem">
                      <slot name="item" :index="index" :item="item" />
                    </SearchOptionItem>
                  </template>
                </ul>
              </div>
              <Empty v-else-if="!searchData.fetching" />
            </OtdScrollbar>
          </slot>
        </div>
        <!-- 容器 end -->
      </div>
    </template>
  </Popover>
</template>
<script lang="ts" setup>
  import { Empty, Input, Popover } from 'ant-design-vue';
  import { useSearchPopover } from './useSearchPopover';
  import { getEmits, getProps } from './props';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { computed, unref, watch } from 'vue';
  import SearchOptionItem from './components/SearchOptionItem.vue';

  const props = defineProps(getProps());
  defineEmits(getEmits());

  const { t } = useI18n();
  const getSearchText = computed(() => props.searchText || t('common.searchText'));
  const getSearchingText = computed(() => props.searchText || t('common.userSearch.searchingText'));
  const getContainerText = computed(() => props.containerText || t('common.userSearch.suggestion'));

  const {
    getVisible,
    searchData,
    searchSelectMap,
    isSelect,
    closePopover,
    handleSearch,
    selectItem,
    cancelItem,
    handleClear,
    handleOpenPopover,
  } = useSearchPopover();

  watch(
    () => props.value,
    (value) => {
      unref(searchSelectMap).clear();
      if (value) {
        if (Array.isArray(value)) {
          value?.map((item) => {
            unref(searchSelectMap).set(item.value!, item);
          });
        } else if (value.value) {
          unref(searchSelectMap).set(value.value, value);
        }
      }
    },
    { deep: true, immediate: true },
  );
</script>
<style lang="less" scoped>
  @import './style.less';
</style>
