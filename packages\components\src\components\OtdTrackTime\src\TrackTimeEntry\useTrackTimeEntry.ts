import dayjs from 'dayjs';
import { reactive, unref } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { getCurrentInstance } from 'vue';
import { ComponentInternalInstance } from 'vue';
import { useModalInner } from '/@/components/BasicModal';
import { ref } from 'vue';
import { Recordable } from '/#/global';
import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
import { TrackTimeRecordType } from '../type';
import { formatToDate } from '/@/tool';
import { UserOptionItemType } from '/@/components/OtdUserSearch';

export interface EntryFormType extends TrackTimeRecordType {
  user?: UserOptionItemType;
}

export enum CountEnum {
  Total,
  Every,
}

export function useTrackTimeEntry() {
  const { props, emit } = getCurrentInstance() as ComponentInternalInstance;
  const { t } = useI18n();

  const { getGlobalProvide } = useGlobalConfig();
  const { getUserInfo } = getGlobalProvide();

  const currentData = ref();
  const hideSplit = ref(false);

  const [register, { changeOkLoading, closeModal }] = useModalInner(
    ({ data, record }: { data: TrackTimeRecordType; record? }) => {
      hideSplit.value = !record || (record?.expandChildren ?? []).length > 0;
      const isSplit = record ? !record.expandChildren.some((item) => !item.workDate) : null;
      if (data.id) {
        currentData.value = data;
        entryForm.id = data.id;
        entryForm.user = { label: data.userName!, value: data.userId, img: data.userAvatar };
        const date = dayjs(data.workDate as string);
        entryForm.workDate = data.id ? date : [date, date];
        entryForm.workHour = data.workHour;
        entryForm.categoryCode = data.categoryCode ?? (props.defaultCategory as string);
        entryForm.remark = data.remark;
      }
      entryForm.isSplit = isSplit ?? !!data?.workDate;
      entryForm.hourType = data.hourType;
    },
  );

  // 工时计算类型
  const countType = ref<CountEnum>(CountEnum.Total);
  // 工时计算类型列表
  const countList = [
    { label: t('common.trackTime.total'), value: CountEnum.Total },
    { label: t('common.trackTime.everyDay'), value: CountEnum.Every },
  ];
  // 工时计算处理
  const countHandler = {
    [CountEnum.Total]: (data, index, days) => {
      const everyHour = Number(((data.workHour ?? 0) / (days + 1)).toFixed(2));
      let every = everyHour;
      if (index === days) every = (data.workHour ?? 0) - index * everyHour;
      return Math.round((every ?? 0) * 60 * 60 * 1000);
    },
    [CountEnum.Every]: (data) => {
      return Math.round((data.workHour ?? 0) * 60 * 60 * 1000);
    },
  };
  // 默认值
  const defaultForm: EntryFormType = {
    id: undefined,
    isSplit: true,
    user: {
      label: getUserInfo?.realName,
      value: getUserInfo?.userId,
      img: getUserInfo?.avatar,
    },
    workDate: getDefaultDate(props.defaultDate),
    workHour: undefined,
    categoryCode: props.defaultCategory as string,
    remark: undefined,
    hourType: undefined,
  };
  // 工时表单
  const entryForm = reactive<EntryFormType>(Object.assign({}, defaultForm));

  // 获取执行时间默认值
  function getDefaultDate(date, form?: EntryFormType): EntryFormType['workDate'] {
    if (form?.id) {
      return dayjs(form.workDate as string);
    }
    if (!date || !(date?.[0] || date?.[1])) {
      const today = dayjs();
      return [today, today];
    }
    if (date[0] && date[1]) {
      return [dayjs(date[0]), dayjs(date[1])];
    } else if (date[0] && !date[1]) {
      return [dayjs(date[0]), dayjs(date[0])];
    } else {
      return [dayjs(date[1]), dayjs(date[1])];
    }
  }

  // 处理日期结果
  function handleDateValue(data: EntryFormType) {
    if (data.id || !data.isSplit) {
      return {
        userId: data.user?.value,
        categoryCode: data.categoryCode,
        workDate: !data.isSplit ? undefined : formatToDate(data?.workDate as string),
        workTime: Math.round((data.workHour ?? 0) * 60 * 60 * 1000),
        remark: data.remark,
      };
    }
    const startDate = data?.workDate?.[0];
    const endDate = data?.workDate?.[1];
    if (!startDate) return [];
    const days = endDate?.diff(startDate, 'day') ?? 0;
    const result: Recordable[] = [];
    for (let i = 0; i <= days; i++) {
      result.push({
        userId: data.user?.value,
        categoryCode: data.categoryCode,
        workDate: formatToDate(startDate.add(i, 'day')),
        workTime: countHandler[countType.value](data, i, days),
        remark: data.remark,
      });
    }
    return result;
  }

  // 关闭后处理
  async function handleAfterClose() {
    Object.assign(entryForm, defaultForm);
    countType.value = CountEnum.Total;
  }

  // 确定事件
  function handleConfirm() {
    changeOkLoading(true);
    emit('submit', {
      value: { id: entryForm.id, data: handleDateValue(entryForm), type: entryForm.hourType },
      resolve: () => {
        changeOkLoading(false);
        closeModal();
        emit('reload', unref(currentData));
      },
    });
  }

  function handleChangeSplit() {
    countType.value = CountEnum.Total;
    entryForm.workDate = entryForm.isSplit ? getDefaultDate(props.defaultDate, entryForm) : undefined;
  }

  return [
    register,
    {
      defaultForm,
      hideSplit,
      entryForm,
      countType,
      countList,
      getDefaultDate,
      handleDateValue,
      handleAfterClose,
      handleConfirm,
      handleChangeSplit,
    },
  ] as const;
}
