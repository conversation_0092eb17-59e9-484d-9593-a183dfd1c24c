<template>
  多项
  <OtdShowClosed :value="value" :options="options" bordered @change="handelChange" />
  <br />
  单项
  <OtdShowClosed :value="value2" :options="options2" bordered @change="handelChange" />
</template>
<script lang="ts" setup>
  import { OtdShowClosed } from '@otd/otd-ui';
  import { reactive } from 'vue';

  const value = reactive({
    task: false,
    subtask: false,
  });
  const value2 = reactive({
    task: false,
  });

  const options = [
    { label: '任务', value: 'task' },
    { label: '子任务', value: 'subtask' },
  ];
  const options2 = [{ label: '任务', value: 'task' }];

  function handelChange(...data) {
    console.log(data);
  }
</script>
<style lang="less" scoped></style>
