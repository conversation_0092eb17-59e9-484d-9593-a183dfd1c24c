<template>
  <!-- ref="modelRef" -->
  <BasicModal
    :title="t('common.repeatPicker.custom')"
    :min-height="100"
    :height="300"
    :width="480"
    :can-fullscreen="false"
    :after-close="handleAfterClose"
    :cancelBubble="true"
    centered
    @register="register"
    @ok="handleOk"
  >
    <Form ref="formRef" :model="frequency">
      <div class="otd-repeat__form-grid">
        <FormItem class="otd-repeat__form-item" name="interval" :label="t('common.repeatPicker.frequency')">
          <Select v-model:value="frequency.interval" class="otd-repeat-select" :options="intervalOptions" />
        </FormItem>
        <FormItem class="otd-repeat__form-item" name="freq">
          <Select
            v-model:value="frequency.freq"
            class="otd-repeat-select"
            :options="freqOptions"
            @change="handleSelectFreq"
          />
        </FormItem>
      </div>
      <FormItem
        v-if="frequency.freq === MONTHLY"
        class="otd-repeat__form-item"
        :label="t('common.repeatPicker.recurrenceRule')"
        name="byType"
      >
        <Select
          v-model:value="frequency.byType"
          class="otd-repeat-select"
          :options="byTypeOptions"
          @change="handleSelectByType"
        />
      </FormItem>
      <div class="otd-repeat__form-grid" v-if="frequency.byType === 'week'">
        <FormItem class="otd-repeat__form-item" name="interval">
          <Select v-model:value="frequency.typeInterval" class="otd-repeat-select" :options="typeIntervalOptions" />
        </FormItem>
        <FormItem class="otd-repeat__form-item" name="freq">
          <Select v-model:value="frequency.byweekday" class="otd-repeat-select" :options="weekOptions" />
        </FormItem>
      </div>
    </Form>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { Form, FormItem, Select } from 'ant-design-vue';
  import { BasicModal, useModalInner } from '/@/components/BasicModal';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { reactive, ref, unref } from 'vue';
  import { FrequencyFormType } from '/@/components/OtdDatePicker/src/type';
  import { useRepeatOption, WeekdayStrs } from './useRepeatOption';
  import { Frequency, RRule, Weekday } from 'rrule';

  const emit = defineEmits(['register', 'ok']);
  const { t } = useI18n();
  const [register] = useModalInner(({ value }) => {
    if (!value) return;
    const { origOptions } = RRule.fromString(value);
    frequency.freq = origOptions.freq ?? freqOptions[0].value;
    frequency.interval = origOptions.interval ?? intervalOptions[0].value;
    handleSelectFreq();
    handleSelectByType();
    if (origOptions.byweekday) {
      frequency.byType = byTypeOptions[1].value;
      const [week] = origOptions.byweekday as Weekday[];
      frequency.byweekday = WeekdayStrs[week.weekday];
      frequency.typeInterval = week.n;
    }
  });

  const { intervalOptions, freqOptions, byTypeOptions, typeIntervalOptions, weekOptions } = useRepeatOption();

  const frequency = reactive<FrequencyFormType>({
    freq: freqOptions[0].value,
    interval: intervalOptions[0].value,
  });

  const formRef = ref();
  function handleAfterClose() {
    unref(formRef).resetFields();
    return Promise.resolve();
  }

  const { MONTHLY } = Frequency;
  function handleSelectFreq() {
    if (frequency.freq === MONTHLY) {
      frequency.byType = byTypeOptions[0].value;
    } else {
      frequency.byType = undefined;
    }
  }

  function handleSelectByType() {
    if (frequency.byType === 'week') {
      frequency.typeInterval = typeIntervalOptions[0].value;
      frequency.byweekday = weekOptions[1].value;
    } else {
      frequency.typeInterval = undefined;
      frequency.byweekday = undefined;
    }
  }

  function handleOk() {
    const { freq, interval, byweekday, typeInterval } = frequency;
    const repeatRule = new RRule({
      byweekday: byweekday ? (typeInterval ? RRule[byweekday].nth(typeInterval) : RRule[byweekday]) : undefined,
      freq,
      interval,
    });
    emit('ok', repeatRule);
  }
</script>
<style lang="less" scoped>
  .otd-repeat {
    &__form-grid {
      display: flex;
      column-gap: 10px;
      :deep(> .ant-form-item):first-of-type {
        flex: 1;
      }
    }
    &__form-item {
      margin-bottom: 12px;
      :deep(.ant-form-item-control-input-content) {
        justify-content: flex-end;
      }
    }
    &-select.ant-select {
      width: 120px !important;
    }
  }
</style>
