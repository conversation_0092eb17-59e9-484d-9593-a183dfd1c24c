import { merge } from 'lodash-es';
import { CanvasDrawType, TextConfigType } from '../type';

/**
 * 文本
 */
export class Text {
  private tool!: CanvasDrawType;
  private info: TextConfigType = {
    color: '#7c828d',
    x: 0,
    y: 0,
    font: 'Arial',
    fontSize: 14,
    isDraw: true,
    isIcon: false,
    // ctx.font = '14px Microsoft YaHei';
  };
  constructor(tool: CanvasDrawType, info: TextConfigType) {
    this.tool = tool;
    this.info = merge(this.info, info);
    if (this.info.isDraw) {
      this.draw();
    }
  }
  private getText() {
    const { ctx } = this.tool;
    const { fontSize, color = '', font = 'Arial' } = this.info;
    ctx.fillStyle = color;
    ctx.font = `${fontSize}px ${font}`;
    ctx.textBaseline = 'middle';
    return ctx;
  }

  // 绘制带省略号的文本
  private drawTextWithEllipsis({ text = '', maxWidth = 0, x, y }: TextConfigType) {
    const { ctx } = this.tool;
    // 测量文本的宽度
    const textWidth = ctx.measureText(text).width;

    // 如果文本宽度超过最大宽度，则进行截断
    if (textWidth > maxWidth) {
      // 计算省略号的宽度
      const ellipsisWidth = ctx.measureText('...').width;

      // 计算截断后的文本宽度
      const truncatedWidth = maxWidth - ellipsisWidth;

      // 截取文本，直到宽度符合要求
      let truncatedText = '';
      for (let i = 0; i < text.length; i++) {
        const truncatedTextWidth = ctx.measureText(truncatedText + text[i]).width;
        if (truncatedTextWidth <= truncatedWidth) {
          truncatedText += text[i];
        } else {
          break;
        }
      }

      // 添加省略号
      truncatedText += '...';

      // 绘制文本
      ctx.fillText(truncatedText, x, y);
    } else {
      // 文本宽度未超过最大宽度，直接绘制
      ctx.fillText(text, x, y);
    }
  }

  draw() {
    let { text = '', x, y, maxWidth, isIcon, borderColor = '', offset = [0, 0] } = this.info;
    const ctx = this.getText();
    if (isIcon) {
      const u16 = `&#x${text}`;
      text = eval(('("' + u16).replace('&#x', '\\u').replace(';', '') + '")');
    }
    x = x + offset[0];
    y = y + offset[1];
    if (borderColor) {
      ctx.strokeStyle = borderColor;
      ctx.strokeText(text, x, y);
    }
    if (maxWidth && maxWidth > 0) {
      this.drawTextWithEllipsis({ ...this.info, text, x, y });
    } else {
      ctx.fillText(text, x, y);
    }
  }

  get getInfo() {
    const ctx = this.getText();
    const { width, actualBoundingBoxDescent, actualBoundingBoxAscent } = ctx.measureText(
      this.info.text ?? '',
    );
    const height = actualBoundingBoxDescent + actualBoundingBoxAscent;
    return { ...this.info, width, height };
  }
  setInfo(info: TextConfigType) {
    this.info = merge(this.info, info);
  }

  // 销毁
  destroy() {}
}
