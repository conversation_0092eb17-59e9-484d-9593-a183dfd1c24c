import { ComponentInternalInstance, computed, getCurrentInstance, reactive, ref, unref } from 'vue';
import { TaskOptionItemType, TaskSearchPropsType } from './type';
import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';

export function useTaskSearch() {
  const { props } = getCurrentInstance() as ComponentInternalInstance & {
    props: TaskSearchPropsType;
  };
  const getVisible = ref(false);
  // 原始数据
  const taskData = reactive({
    keyword: '',
    data: [] as TaskOptionItemType[],
    fetching: false,
  });
  const { getGlobalProvide } = useGlobalConfig();
  const { getFetchTask } = getGlobalProvide();
  const requestMap = {
    getFetchTask: props.remoteMethod ?? getFetchTask,
  };

  const TaskSelectMap = ref(new Map<string, TaskOptionItemType>());
  const isSelectTask = computed(() => unref(TaskSelectMap).size > 0);

  return {
    taskData,
    requestMap,
    TaskSelectMap,
    isSelectTask,
    getVisible,
  };
}
