<template>
  <OtdMoreAction
      :data="props.item"
      :list="props.list"
      size="large"
      :actions="Actions"
      hide-expand-name
      destroy-popup-on-hide
  />
</template>
<script lang="tsx" setup>
import { OtdMoreAction, MoreActionItem } from "@otd/otd-ui";
import { ref, defineProps } from 'vue'


const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  index: {
    type: Number,
    default: -1,
  },
  list: {
    type: Array,
    default: () => [],
  },
  hideAction: {
    type: Boolean,
    default: false,
  }
});
const item = ref([{}])

// 操作
const Actions: MoreActionItem[] = [
  {
    id: 5,
    name: '扩展内容',
    icon: 'otd-icon-files',
    action: () => {},
    expand: <div>我是一段文字扩展内容</div>
  },
];
</script>
<style lang="less" scoped></style>
