<template>
  <div class="otd-layout">
    <LayoutFeatures v-if="!hiddenLayout" />
    <div class="otd-layout__menu" v-if="!hiddenLayout">
      <div class="otd-layout__menu-logo">
        <OtdAppLogo :icon-size="40" :logo-url="logoUrl" :goHome="layoutConfig.logoClick" />
      </div>
      <LayoutMenu
        v-model:selectedKeys="selectedKeys"
        :items="MenuData"
        :trigger-sub-menu-action="triggerSubMenuAction"
        @menu-click="handleLeftMenuClick"
      />
    </div>
    <div class="otd-layout__content">
      <!-- 头部 -->
      <div class="otd-layout__content__header" v-if="!hiddenLayout">
        <div class="otd-layout__content__header__menu">
          <LayoutHorizontalMenu
            v-model:selectedKeys="selectedKeys"
            :menu-map="MenuSourceMap"
            :items="props.menuItems"
            :trigger-sub-menu-action="triggerSubMenuAction"
            @menu-click="handleMenuClick"
          />
        </div>
        <div class="otd-layout__content__header__config">
          <slot name="config"></slot>
          <OtdExpandBreadcrumb v-model:expand="isShowBreadcrumb" />
          <OtdFullScreen />
          <OtdNotification />
          <div
            class="otd-layout-action"
            v-for="action in PinQuickAction"
            :key="action.id"
            @click.stop="action.action?.({ option: action })"
          >
            <Tooltip placement="bottom" :title="action.name">
              <IconComponent :data="action" />
            </Tooltip>
          </div>
          <OtdQuickActionMenu :custom-quick-actions="customQuickActions" v-if="showQuickAction" />
          <OtdUserDropdown
            :info="userInfo"
            :user-dropdown-action="userDropdownAction"
            :upload-avatar="layoutConfig.uploadAvatar"
          />
        </div>
      </div>
      <!-- 内容 -->
      <div class="otd-layout__content__body">
        <template v-if="!hiddenLayout">
          <div class="otd-layout__content__body-breadcrumb" v-show="isShowBreadcrumb">
            <OtdBreadcrumb :data="breadcrumbData" show-title show-app-title />
          </div>
        </template>
        <div class="otd-layout__content__body-container">
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
  import LayoutMenu from './components/LayoutMenu.vue';
  import LayoutFeatures from './components/LayoutFeatures.vue';
  import LayoutHorizontalMenu from './components/LayoutHorizontalMenu.vue';
  import { OtdUserDropdown } from './components/OtdUserDropdown';
  import { OtdNotification } from './components/OtdNotification';
  import { OtdFullScreen } from './components/OtdFullScreen';
  import { OtdExpandBreadcrumb } from './components/OtdExpandBreadcrumb';
  import { OtdAppLogo } from '/@/components/OtdApplication';
  import { OtdBreadcrumb } from '/@/components/OtdBreadcrumb';

  import { layoutProps } from './props';
  import { computed, onMounted, ref, unref } from 'vue';
  import { extractLevel, mapDeep } from '/@/utils/dateUtil';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
  import { useSignalR } from '/@/hooks/web/useSignalR';
  import { MenuType } from '/@/components/OtdMenu';
  import { useRoute } from 'vue-router';
  import { createLocalStorage } from '/@/utils/cache';
  import { Show_Breadcrumb } from '/@/setting';
  import { OtdQuickActionMenu } from './components/OtdQuickActionMenu';
  import { IconComponent, QuickActionMenu } from './components/OtdQuickActionMenu/src/useQuickActionMenu';
  import { Tooltip } from 'ant-design-vue';

  const props = defineProps(layoutProps());
  const emit = defineEmits(['update:selectedKeys', 'menu-click']);

  let route;
  if (!props.hiddenLayout) {
    route = useRoute();
  }
  const breadcrumbData = computed(() => {
    const { matched = [] } = route ?? {};
    return matched
      .filter((item) => !item.meta.hideBreadcrumb)
      .map((item) => {
        return {
          value: item.path,
          label: item.meta.title as string,
        };
      });
  });

  // 全局配置
  const ls = createLocalStorage();
  const { setGlobalProvide, getGlobalProvide } = useGlobalConfig();
  const layoutConfig = getGlobalProvide();
  setGlobalProvide({ ...layoutConfig, userInfo: props.userInfo });

  const { startConnect } = useSignalR({
    socketUrl: layoutConfig.webSocketUrl?.(),
    token: props.userInfo?.token,
    logout: layoutConfig.logout,
  });
  const PinQuickAction = computed(() => Array.from(QuickActionMenu.values()).filter((item) => item.pin));
  onMounted(() => {
    if (!props.hiddenLayout) {
      startConnect();
    }
  });

  const selectedKeys = computed({
    get() {
      return props.selectedKeys;
    },
    set(value) {
      emit('update:selectedKeys', value);
    },
  });
  const isShowBreadcrumb = ref(ls.get(Show_Breadcrumb) ?? false);
  const MenuSourceMap = computed(() => {
    const dataMap = new Map();
    mapDeep(props.menuItems, (item) => {
      dataMap.set(item.path, item);
    });
    return dataMap;
  });

  const MenuData = computed(() => extractLevel<MenuType>(props.menuItems, 2));

  function handleLeftMenuClick(data) {
    const item = unref(MenuSourceMap).get(data.key);
    if (item?.children?.[0]) {
      selectedKeys.value = [...data.keyPath, item.children[0].path];
    } else {
      selectedKeys.value = [...data.keyPath];
    }
    handleMenuClick(data);
  }

  function handleMenuClick(data) {
    emit('menu-click', data);
  }
</script>
<style lang="less" scoped>
  @import './style.less';
</style>
