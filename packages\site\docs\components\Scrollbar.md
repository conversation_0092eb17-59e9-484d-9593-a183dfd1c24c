# 滚动盒子

<p style="font-size:26px">代码演示</p>

## 基础表格

<demo src="../demo/Scrollbar/basic.vue" title="基础滚动盒子"></demo>

## 属性

| 参数      | 说明                                                    | 类型          | 可选值 | 默认值  | 版本 |
| --------- | ------------------------------------------------------- | ------------- | ------ | ------- | ---- |
| native    | 是否使用原生滚动条                                      | Boolean       |        | `false` | 1.0  |
| wrapClass | 滚动区域 自定义 class                                   | String, Array |        |         | 1.0  |
| wrapStyle | 滚动区域 样式                                           | String, Array |        |         | 1.0  |
| viewClass | 滚动视图 自定义 class                                   | String, Array |        |         | 1.0  |
| viewStyle | 滚动视图 样式                                           | String, Array |        |         | 1.0  |
| noresize  | 如果 container 尺寸不会发生变化，最好设置它可以优化性能 | Boolean       |        |         | 1.0  |
| tag       | 滚动盒子标签                                            | String        |        | div     | 1.0  |
