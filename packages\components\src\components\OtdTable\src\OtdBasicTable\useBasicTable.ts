import { TriggerEventHandler } from 'ant-design-vue/es/vc-table/interface';
import { provide, inject } from 'vue';

export interface BodyContextProps<RecordType = any> {
  onTriggerExpand: TriggerEventHandler<RecordType>;
}
const ExpandKey = Symbol('Expand');
export const useProvideBody = (data: BodyContextProps) => {
  provide(ExpandKey, data);
};
export const useInjectBody = () => {
  return inject<BodyContextProps>(ExpandKey);
};

export function useBasicTable() {
  return {};
}
