import { StatusColorEnum } from './useStatus';

export type OtdStatusOptionType<T = string | number> = {
  id: string | number;
  title: string;
  color?: StatusColorEnum;
  sort?: number;
  isProgress?: (data: { current: T }) => number;
  actions?: OtdStatusActionType[];
  stepList?: [];
};

export type OtdStatusActionType = {
  id: string | number;
  label: string;
  type?: 'primary' | 'default';
  action: (data: { item: OtdStatusActionType; status?: OtdStatusOptionType }) => void;
};
