<template>
  <component :is="emoticon" :size="size" />
</template>
<script lang="ts" setup>
  import { asyncComputed } from '@vueuse/core';

  const props = defineProps({
    icon: {
      type: String,
    },
    size: {
      type: Number,
    },
  });

  const emoticon = asyncComputed(async () => (await import(`./Emoticons/${props.icon}.vue`)).default);
</script>
<style lang="less" scoped>
  .otd-emoticon-icon {
  }
</style>
