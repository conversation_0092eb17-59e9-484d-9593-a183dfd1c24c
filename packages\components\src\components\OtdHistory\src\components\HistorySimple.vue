<template>
  <OtdScrollbar class="otd-history-simple">
    <div class="otd-history-simple-item" v-for="(item, index) in data" :key="index">
      <div class="otd-history-simple__content">
        <OtdAvatar :url="getAvatar(item[getFields.creatorId])" size="26px"></OtdAvatar>
        <Popover trigger="click" @open-change="(value) => openUser(value, item)" :overlay-style="{ maxWidth: '300px' }">
          <template #title>
            <slot name="userTitle" :data="item" :index="index"> </slot>
          </template>
          <template #content :data="item" :index="index">
            <slot name="userContent"> </slot>
          </template>
          <span class="otd-text-link">{{ item[getFields.creatorName] }}</span>
        </Popover>
        <span class="otd-history-simple__content-action">{{ item[getFields.content] }}</span>
        <Popover trigger="click" @open-change="(val) => openContent(val, item)" :overlay-style="{ maxWidth: '300px' }">
          <template #title>
            <slot name="contentTitle" :data="item" :index="index"></slot>
          </template>
          <template #content>
            <slot name="contentContent" :data="item" :index="index"></slot>
          </template>
          <span class="otd-text-link">{{ item[getFields.content] }}</span>
        </Popover>
      </div>
      <span class="otd-history-simple__date">{{ formatToTime(item.creationTime) }}</span>
    </div>
  </OtdScrollbar>
</template>
<script lang="ts" setup>
  import { DefaultFields, getEmits, getProps } from '../props';
  import { Popover } from 'ant-design-vue';
  import { OtdAvatar } from '/@/components/OtdAvatar';
  import { computed } from 'vue';
  import { formatToTime } from '/@/tool';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';

  const props = defineProps(getProps());
  const emit = defineEmits(getEmits());

  const { getGlobalProvide } = useGlobalConfig();
  const { getAvatar } = getGlobalProvide();
  const getFields = computed(() => ({ ...DefaultFields, ...props.fields }));

  function openUser(val, item) {
    emit('openUser', val, item);
  }
  function openContent(val, item) {
    emit('openContent', val, item);
  }
</script>
<style lang="less" scoped>
  .otd-history-simple {
    overflow: auto;
    height: 100%;
    &-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    &__content {
      display: flex;
      .otd-avatar {
        margin-right: 8px;
      }
      &-action {
        margin: 0 8px;
      }
    }
    &__date {
      color: var(--otd-gray3-color);
    }
  }
</style>
