import { DefaultOptionType } from 'ant-design-vue/es/select';
import { TriggerType } from 'ant-design-vue/es/tooltip/abstractTooltipProps';
import { TooltipPlacement } from 'ant-design-vue/lib/tooltip';
import { VNode, Component } from 'vue';
// copy from element-plus

import type { CSSProperties, Plugin } from 'vue';

type OptionalKeys<T extends Record<string, unknown>> = {
  [K in keyof T]: T extends Record<K, T[K]> ? never : K;
}[keyof T];

type RequiredKeys<T extends Record<string, unknown>> = Exclude<keyof T, OptionalKeys<T>>;

type MonoArgEmitter<T, Keys extends keyof T> = <K extends Keys>(evt: K, arg?: T[K]) => void;

type BiArgEmitter<T, Keys extends keyof T> = <K extends Keys>(evt: K, arg: T[K]) => void;

export type EventEmitter<T extends Record<string, unknown>> = MonoArgEmitter<T, OptionalKeys<T>> &
  BiArgEmitter<T, RequiredKeys<T>>;

export type AnyFunction<T> = (...args: any[]) => T;

export type PartialReturnType<T extends (...args: unknown[]) => unknown> = Partial<ReturnType<T>>;

export type SFCWithInstall<T> = T & Plugin;

export type Nullable<T> = T | null;

export type RefElement = Nullable<HTMLElement>;

export type CustomizedHTMLElement<T> = HTMLElement & T;

export type Indexable<T> = {
  [key: string]: T;
};

export type Hash<T> = Indexable<T>;

// export type TimeoutHandle = ReturnType<typeof global.setTimeout>;

export type ComponentSize = 'large' | 'medium' | 'small' | 'mini';

export type ActionTypeItem = 'btn' | 'icon';

export type StyleValue = string | CSSProperties | Array<StyleValue>;

export type Mutable<T> = { -readonly [P in keyof T]: T[P] };

export type Override<P, S> = Omit<P, keyof S> & S;

export type ActionItem = {
  id: string | number;
  tip?: string;
  name?: string | ((data) => string);
  color?: string;
  icon?: string | ((data) => VNode);
  iconSize?: string;
  // 权限标识
  auth?: string;
  // 展开区域
  expand?: VNode | ((data) => VNode);
  expandPlacement?: TooltipPlacement;
  expandTrigger?: TriggerType;
  loading?: boolean;
  // 固定
  pin?: boolean;
  // 定死
  fixed?: boolean | string;
  // 自定义渲染
  customRender?: (data: { data: any; item: ActionItem }) => VNode;
  // 按钮隐藏
  isHide?: (data, action) => boolean;
  // 点击事件
  action?: (data?, list?: any[], index?: number) => void | Promise<any>;
  // 角标
  badge?: () => number;
  // 禁用
  disabled?: (data: any) => boolean;
};

export type FilterItem = {
  id: string | number;
  type?: 'input' | undefined;
  label: string;
  value: string;
  rangeKey?: [string, string];
  mergeCondition?: boolean;
  conditions?: DefaultOptionType[];
  valueOptions?: DefaultOptionType[];
  valueComponent?: Component;
  valueProps?: any;
  valueApi?: Function;
  valueHandler?: (data, index?: number) => any;
};

export type SizeType = 'large' | 'default' | 'small';

export enum FolderEnum {
  Directory = 0,
  Resource = 1,
}

export enum ResourceCategoryEnum {
  Template = 0,
  Form = 1,
  FormFill = 2,
  Document = 3,
}
