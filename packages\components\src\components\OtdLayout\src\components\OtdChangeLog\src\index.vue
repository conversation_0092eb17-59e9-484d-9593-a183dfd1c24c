<template>
  <MenuItem class="otd-change-log-picker" @click.stop="openLogDocument">
    <template #icon>
      <i class="otdIconfont otd-icon-gengxinrizhi"></i>
    </template>
    <span>{{ t('layout.header.changelog') }}</span>
  </MenuItem>
</template>
<script lang="ts" setup name="OtdFullScreen">
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
  import { MenuItem } from 'ant-design-vue';

  const { t } = useI18n();
  const { getGlobalProvide } = useGlobalConfig();
  const { changeLog } = getGlobalProvide();
  function openLogDocument() {
    changeLog && window.open(changeLog);
  }
</script>
