import { useI18n } from '/@/hooks/web/useI18n';
import { TaskTypeOptionType } from '/@/components';

export enum TaskTypeEnum {
  Task = 1,
  Project = 2,
  Custom = 99,
}

export function useTaskTypeDictionary() {
  const { Task, Project } = TaskTypeEnum;
  const { t } = useI18n();
  const TaskTypeDictionary: Map<TaskTypeEnum, TaskTypeOptionType> = new Map([
    [Task, { value: Task, icon: 'Task', label: t('common.task') }],
    [Project, { value: Project, icon: 'FileDateOne', label: t('common.project') }],
  ]);
  const TaskTypeOptions = Array.from(TaskTypeDictionary.values());

  function getTaskTypeIcon(taskType) {
    return TaskTypeDictionary.get(Number(taskType))?.icon ?? 'Task';
  }
  function getTaskTypeName(taskType) {
    return TaskTypeDictionary.get(Number(taskType))?.label ?? t('common.task');
  }
  return {
    TaskTypeOptions,
    getTaskTypeIcon,
    getTaskTypeName,
  };
}
