<template>
  <div style="height: 300px">
    <OtdComment
      :list="commentList"
      :loading="loading"
      :get-mentions-request="getMentions"
      @emoticon="handleEmoticon"
      @delete="handleDelete"
      @confirm="handleConfirm"
      @reply="handleReply"
    />
  </div>
</template>
<script lang="ts" setup>
  import { OtdComment } from '@otd/otd-ui';
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';

  const commentList = ref([
    {
      atUsers: [],
      commentUser: '吴孟达',
      commentUserAvatar: 'https://picsum.photos/200/300',
      content: `<p>这是评论带图片
        <img src="https://tse4-mm.cn.bing.net/th/id/OIP-C.GC_ugX-TzPVR26SSxI1kZwHaE9" alt="" width="175" height="165" />
        <img src="https://tse2-mm.cn.bing.net/th/id/OIP-C.7sAjIeoQYWnXV_QnuYs1jQHaEK" alt="" width="175" height="165" />
      </p>`,
      creationTime: '2024-03-08T09:04:29.322236',
      creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
      extraProperties: {},
      id: 1,
      taskItemId: '3a0f358a-4993-7b4f-7e89-872127943f29',
      emojiReplies: ['Done', 'Ok', 'Like', 'FingerHeart', 'Flower'].map((item) => ({
        emoji: item,
        userList: [
          {
            emojiReplyId: '3a134581-644d-fb19-c052-fc2639d0e005',
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            userName: 'limin',
            avatar: 'https://picsum.photos/200/300',
          },
          {
            emojiReplyId: '3a134581-644d-fb19-c052-fc2639d0e005',
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            userName: 'limin',
            avatar: 'https://picsum.photos/200/300',
          },
          {
            emojiReplyId: '3a134581-644d-fb19-c052-fc2639d0e005',
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            userName: 'limin',
            avatar: 'https://picsum.photos/200/300',
          },
          {
            emojiReplyId: '3a134581-644d-fb19-c052-fc2639d0e005',
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            userName: 'limin',
            avatar: 'https://picsum.photos/200/300',
          },
          {
            emojiReplyId: '3a134581-644d-fb19-c052-fc2639d0e005',
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            userName: 'limin',
            avatar: 'https://picsum.photos/200/300',
          },
          {
            emojiReplyId: '3a134581-644d-fb19-c052-fc2639d0e005',
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            userName: 'limin',
            avatar: 'https://picsum.photos/200/300',
          },
          {
            emojiReplyId: '3a134581-644d-fb19-c052-fc2639d0e005',
            userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            userName: 'limin',
            avatar: 'https://picsum.photos/200/300',
          },
        ],
      })),
    },
    {
      atUsers: [],
      commentUser: '周星驰',
      commentUserAvatar: 'https://picsum.photos/200/300',
      content: '<p>第一条评论222</p>',
      creationTime: '2024-03-08T09:04:29.322236',
      creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
      extraProperties: {},
      id: 2,
      taskItemId: '3a0f358a-4993-7b4f-7e89-872127943f29',
    },
  ]);

  const loading = ref(true);
  setTimeout(() => {
    loading.value = false;
  }, 2000);
  function handleDelete(data, list, index) {
    console.log(data, list, index);

    message.info(`删除评论ID:【${data.id}】`);
    list.splice(index, 1);
  }
  function handleConfirm({ value, resourceFiles, clearable }, comment) {
    comment.isEditDesc = false;
    comment.content = value;
    clearable();
    console.log('确认', { value, resourceFiles, clearable }, comment);
  }
  function handleReply(data) {
    message.info(`点击了回复:【${data.commentUser}】`);
    console.log('回复', data);
  }
  function handleEmoticon(data) {
    message.info(`点击了表情:【${data.emoticon}】`);
    console.log('点击了表情', data);
  }
  function getMentions(_, filter): Promise<Record<string, any>[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(
          [
            {
              tenantId: null,
              userName: '1154268',
              name: '朱成',
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '3302b5f9d11a491fa83cb4b9bbbdd122',
              entityVersion: 3,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-09-26T16:00:17.748393',
              lastModifierId: null,
              creationTime: '2023-09-26T15:55:00.44181',
              creatorId: null,
              id: '1475ae74-6669-3a7b-d97c-3a0de2a8d17c',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '1022032',
              name: '陈永琴',
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: 'c5070e7110b4487a8aa6dd14e483ea09',
              entityVersion: 10,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-12-15T16:59:03.323436',
              lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              creationTime: '2023-09-26T15:54:58.504459',
              creatorId: null,
              id: '245875ae-10f9-f520-cdca-3a0de2a8c9e8',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: 'admin',
              name: 'limin',
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: '',
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '550d7f675fa24ee7af9c95ce15fee7e3',
              entityVersion: 230,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2024-05-15T13:19:31.998398',
              lastModifierId: null,
              creationTime: '2023-01-13T10:54:10.282165',
              creatorId: null,
              id: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: '3a0f074b-4dd5-3a27-c287-6949611e7fd9',
              },
            },
            {
              tenantId: null,
              userName: '<EMAIL>',
              name: 'adw2',
              surname: null,
              email: '112@testcom',
              emailConfirmed: false,
              phoneNumber: '',
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '282ac1fe314449f386f2f3426a119294',
              entityVersion: 8,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2024-05-07T19:22:35.044619',
              lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              creationTime: '2023-10-23T13:33:30.672791',
              creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              id: '3a0e6d32-fa4d-f50d-bc46-7b4d9ad67446',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '204369',
              name: 'rikka',
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: 'ae097271aa9d456498f27e1c7aaad6db',
              entityVersion: 3,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-12-06T12:57:28.9468',
              lastModifierId: null,
              creationTime: '2023-12-06T12:57:28.891637',
              creatorId: null,
              id: '3a0f4fa9-cddb-f885-2ecc-2b0b466686e0',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
          ]
            .map((user) => ({
              label: user.name,
              value: user.id,
              img: user?.extraProperties?.Avatar,
            }))
            .filter((item) => item.label.toLowerCase().includes(filter.query.toLowerCase())),
        );
      });
    });
  }
</script>
<style lang="less" scoped></style>
