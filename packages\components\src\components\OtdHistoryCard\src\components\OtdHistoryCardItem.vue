<template>
  <div class="otd-history-card-item">
    <div class="otd-history-card-item__header otd-truncate">
      <div class="otd-history-card-item__header-relate otd-truncate" v-if="isShowRelate" :title="data?.relatedName">
        {{ data?.i18NRelatedType }} /
        <span v-if="data?.i18NSubRelatedType">{{ data?.i18NSubRelatedType }} /</span>
        {{ data?.relatedName }}
      </div>
      <span class="otd-history-card-item__header-title" :title="data?.relatedTitle" @click="handleClickItem">
        <OtdStatusTrigger :icon-type="getTaskTypeIcon(data?.taskType)" :value="data?.status" is-text hide-text />
        <span class="otd-truncate">{{ data?.relatedTitle }}</span>
      </span>
    </div>
    <div class="otd-history-card-item__body">
      <div class="otd-history-card-item__body-record" v-for="(history, index) in data?.list" :key="index">
        <span class="otd-box-left">
          <span class="otd-primary-color">{{ history[fields.creatorName!] }}&nbsp;</span>
          <HistoryContent :data="history" :fields="fields" :historyOptions="DefaultHistoryOptions" />
        </span>
        <span>{{ formatToTime(history[fields.creationTime!]) }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed, PropType } from 'vue';
  import { OtdHistoryCardDataType, OtdHistoryCardFieldsType } from '../type';
  import { HistoryContent, useHistoryTimeline } from '/@/components/OtdHistory/src/components/useHistoryTimeline';
  import { formatToTime } from '/@/tool';
  import { OtdStatusTrigger } from '/@/components/OtdStatus';
  import { RelateEnum } from '../props';
  import { useTaskTypeDictionary } from '/@/hooks/dictionary';

  const props = defineProps({
    data: {
      type: Object as PropType<OtdHistoryCardDataType>,
    },
    fields: {
      type: Object as PropType<Partial<OtdHistoryCardFieldsType>>,
      default: () => ({}),
    },
  });
  const emit = defineEmits(['click-item']);

  const { DefaultHistoryOptions } = useHistoryTimeline();
  const { getTaskTypeIcon } = useTaskTypeDictionary();
  const isShowRelate = computed(() => props.data?.relatedType && props.data?.relatedType !== RelateEnum.Manually);

  function handleClickItem() {
    emit('click-item', props.data);
  }
</script>
<style lang="less" scoped>
  .otd-history-card-item {
    box-shadow: var(--otd-box-shadow);
    border: 1px solid var(--otd-border-color);
    border-radius: var(--otd-default-radius);
    max-width: 720px;
    width: 100%;
    background-color: var(--otd-basic-bg);
    &__header {
      border-bottom: 1px solid var(--otd-border-color);
      padding: 10px 20px;
      position: sticky;
      top: 38px;
      background-color: var(--otd-basic-bg);
      z-index: 2;
      &-relate {
        font-size: 12px;
        color: var(--otd-gray3-color);
        margin-bottom: 4px;
        line-height: 20px;
      }
      &-title {
        font-weight: 500px;
        font-size: 17px;
        display: flex;
        align-items: center;
        column-gap: 6px;
        cursor: pointer;
      }
    }
    &__body {
      display: flex;
      flex-direction: column;
      row-gap: 16px;
      padding: 16px 20px;
      &-record {
        display: flex;
        justify-content: space-between;
        font-size: 13px;
        line-height: 24px;
        column-gap: 4px;
        :deep(.otd-time-line-content) {
          flex: 1;
        }
        > span:first-of-type {
          flex: 1;
          align-items: flex-start;
        }
      }
    }
  }
</style>
