<template>
  <div class="otd-app-logo anticon" :class="{ 'is-light': isLight }" @click="goHome">
    <div
      class="otd-app-logo__img"
      :style="{
        width: iconSize + 'px',
        height: iconSize + 'px',
        '--logo': logoUrl ? `url(${customUrl})` : undefined,
      }"
    ></div>
    <div class="otd-app-logo__title" v-show="title">
      {{ title }}
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { PropType } from 'vue';
  import { computedAsync } from '@vueuse/core';

  const props = defineProps({
    goHome: { type: Function as PropType<() => void> },
    isLight: { type: Boolean, default: false },
    title: { type: String },
    logoUrl: { type: String },
    iconSize: { type: Number, default: 32 },
  });

  const customUrl = computedAsync(async () => {
    if (!props.logoUrl) return;
    const data = await import(props.logoUrl);
    return data.default;
  });
</script>
<style lang="less" scoped>
  @logo: ~'@{namespace}-app-logo';

  .@{logo} {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &__title {
      font-size: 18px;
      font-weight: bold;
      transition: all 0.5s;
      line-height: normal;
      margin-left: 0.5rem;
    }
    &__img {
      --logo: url('/@/assets/images/logo.svg');
      background-image: var(--logo);
      background-repeat: no-repeat;
      background-size: 100%;
    }
  }

  .is-light,
  [data-theme='dark'],
  .dark {
    .otd-app-logo__img {
      --logo: url('/@/assets/images/logo_white.svg');
    }
  }
</style>
