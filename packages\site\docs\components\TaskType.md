## 任务类型

<demo src="../demo/TaskType/basic.vue" title="任务类型"></demo>

## 属性

| 参数           | 说明     | 类型                 | 可选值          | 默认值 | 版本 |
| -------------- | -------- | -------------------- | --------------- | ------ | ---- |
| value(v-model) | 值       | String, Number       | --              | --     | 1.0  |
| options        | 下拉选项 | TaskTypeOptionType[] | --              | --     | 1.0  |
| title          | 标题     | String               | --              | --     | 1.0  |
| defaultValue   | 默认值   | String               | --              | --     | 1.0  |
| help           | 帮助文本 | String               | --              | --     | 1.0  |
| placeholder    | 占位     | String               | --              | --     | 1.0  |
| disabled       | 禁用     | Boolean              | --              | --     | 1.0  |
| mode           | 模式     | String               | `select`,`icon` | --     | 1.0  |

## Slots 插槽

| 参数  | 说明           | 版本 |
| ----- | -------------- | ---- |
| other | 弹出层右侧内容 | 1.0  |

## 事件

| 事件名 | 说明     | 类型                               | 版本 |
| ------ | -------- | ---------------------------------- | ---- |
| change | 选择事件 | (value:TaskTypeOptionType) => void | 1.0  |
