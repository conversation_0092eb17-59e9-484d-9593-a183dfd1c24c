<template>
  <BasicModal
    :title="title"
    :ok-text="okText"
    :can-fullscreen="false"
    :after-close="cancelForm"
    :min-height="120"
    @ok="submitForm"
    @register="registerModal"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { computed, unref } from 'vue';
  import { BasicForm } from '/@/components/BasicForm';
  import { BasicModal } from '/@/components/BasicModal';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useCreateFolder } from './useCreateFolder';
  import { getProps } from '../../../props';

  defineProps(getProps());
  defineEmits(['reload', 'register']);

  const { t } = useI18n();
  const title = computed(() => {
    return unref(isEdit) ? t('common.folder.updateFolder') : t('common.folder.newFolder');
  });
  const okText = computed(() => {
    return unref(isEdit) ? t('common.saveText') : t('common.create');
  });

  const { isEdit, registerForm, cancelForm, submitForm, registerModal } = useCreateFolder();
</script>
<style lang="less" scoped></style>
