import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
import { DrawOptionType, PosterPropsType } from './type';

export enum DrawContentEnum {
  text = 'text',
  image = 'image',
}

//圆角矩形
CanvasRenderingContext2D.prototype.roundRect = function (x: number, y: number, w: number, h: number, r: number = 0) {
  const MinSize = Math.min(w, h);
  if (r > MinSize / 2) r = MinSize / 2;
  // 开始绘制
  this.beginPath();
  this.moveTo(x + r, y);
  this.arcTo(x + w, y, x + w, y + h, r);
  this.arcTo(x + w, y + h, x, y + h, r);
  this.arcTo(x, y + h, x, y, r);
  this.arcTo(x, y, x + w, y, r);
  this.closePath();
  return this;
};

export function useDrawPoster() {
  const { props } = getCurrentInstance() as ComponentInternalInstance & { props: PosterPropsType };
  const { ratio, drawOptions } = props;
  const canvasRef = ref<HTMLCanvasElement>();
  const loading = ref(false);
  let ctx: CanvasRenderingContext2D;

  function drawPosterInit() {
    const canvas = canvasRef.value!;
    canvas.width = getScaleSize(canvas?.clientWidth);
    canvas.height = getScaleSize(canvas?.clientHeight);
    ctx = canvas.getContext('2d')!;
    drawPosterContent();
  }

  function getScaleSize(value?: number, defaultValue = 0) {
    return value ? value * ratio : defaultValue;
  }

  async function getContent(data: DrawOptionType) {
    const { content } = data;
    if (typeof content === 'function') {
      return await content();
    } else {
      return content;
    }
  }

  const DrawContentMap = {
    [DrawContentEnum.text]: async (data: DrawOptionType<DrawContentEnum.text>) => {
      const { coordinate } = data;
      const { color, fontSize = 14, fontFamily = 'Arial' } = data.style ?? {};
      if (data.style) {
        color && (ctx.fillStyle = color);
      }
      ctx.font = `${getScaleSize(fontSize, 12)}px ${fontFamily}`;
      const [x, y] = coordinate.map((value) => getScaleSize(value));
      ctx.fillText(await getContent(data), x, y);
      return true;
    },
    [DrawContentEnum.image]: (data: DrawOptionType<DrawContentEnum.image>) =>
      new Promise(async (resolve) => {
        const { coordinate, style } = data;
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = function () {
          const [x, y] = coordinate.map((value) => getScaleSize(value));
          const width = getScaleSize(style?.width!, 10);
          const height = getScaleSize(style?.height!, 10);
          const borderRadius = getScaleSize(data.style?.borderRadius);
          ctx.roundRect(x, y, width, height, borderRadius);
          ctx.clip();
          ctx.drawImage(img, x, y, width, height);
          ctx.restore();
          ctx.save();
          resolve(true);
        };
        img.src = await getContent(data);
      }),
  };

  async function drawPosterContent() {
    ctx.reset();
    loading.value = true;
    for (let i = 0; i < (drawOptions?.length ?? 0); i++) {
      const item = drawOptions![i];
      await DrawContentMap[item.type](item);
    }
    loading.value = false;
  }

  function toDataURL() {
    return canvasRef.value?.toDataURL('image/png');
  }
  return {
    loading,
    canvasRef,
    drawPosterInit,
    drawPosterContent,
    toDataURL,
  };
}
