import { PropType } from 'vue';
import { FolderRequestType } from '../../OtdFolderMenu';
import { mutable } from '/@/tool';
import { Recordable } from '/#/global';

export const getProps = () => ({
  value: {
    type: String,
    default: undefined,
  },
  draggable: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: 'Text',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  getTaskList: {
    type: Function as PropType<(data?) => Promise<Recordable[]>>,
    default: undefined,
  },
  folderRequest: {
    type: Object as PropType<FolderRequestType>,
    default: () => undefined,
  },
});
const emit = ['confirm', 'dragEnd'] as const;
export const getEmits = () => mutable(emit);
