import { TableColumnType, TablePaginationConfig } from 'ant-design-vue';
import { ExtractPropTypes, VNode } from 'vue';
import { EmitType, Recordable } from '/#/global';
import { getEmits, tableProps as otdTableProps } from './props';
import { TableRowSelection } from 'ant-design-vue/es/table/interface';
import { tableProps, ColumnType } from 'ant-design-vue/es/table';
import { RenderedCell } from 'ant-design-vue/es/vc-table/interface';

export type TableSummaryType = Recordable<string | VNode> | Recordable<TableSummaryPropsType>;

export type TableSummaryPropsType = {
  content: string | VNode;
  colSpan?: number;
};

export interface TableColumnPropsType extends TableColumnType {
  defaultHidden?: boolean;
  hideSetting?: boolean;
  disabledSetting?: boolean;
  flag?: string;
  contentHandler?: (data) => any;
  headerRender?: (data) => VNode;
  customRender?: (opt: {
    value: any;
    text: any;
    record: Recordable;
    index: number;
    renderIndex: number;
    groupData: Recordable;
    column: ColumnType<Recordable>;
  }) => any | RenderedCell<Recordable>;
  isMain?: boolean;
  bordered?: boolean;
}

export type TableColumnEmitType = EmitType<ReturnType<typeof getEmits>[number]>;

export type TableInstancePropsType = ExtractPropTypes<ReturnType<typeof otdTableProps>>;
export type TablePropsType = ExtractPropTypes<ReturnType<typeof otdTableProps> & ReturnType<typeof tableProps>>;

export interface GetColumnsParams {
  ignoreIndex?: boolean;
  ignoreAction?: boolean;
  sort?: boolean;
}

export interface FetchParams {
  searchInfo?: Recordable;
  page?: number;
  sortInfo?: Recordable;
  filterInfo?: Recordable;
}

export interface TableActionType {
  reload: (opt?: FetchParams) => Promise<any>;
  getSelectRows: <T = Recordable>() => T[];
  clearSelectedRowKeys: () => void;
  getSelectRowKeys: () => string[];
  deleteSelectRowByKey: (key: string) => void;
  getPagination: () => TablePaginationConfig | false;
  setPagination: (info: Partial<TablePaginationConfig>) => void;
  setTableData: <T = Recordable>(values: T[]) => void;
  getDataSource: <T = Recordable>() => T[];
  getLoading: () => boolean;
  setLoading: (loading: boolean) => void;
  setProps: (props: Partial<TablePropsType>) => void;
  getTableProps: () => TablePropsType;
  setSelectedRowKeys: (rowKeys: string[] | number[]) => void;
  setSelectedRows: (rows: Recordable[]) => void;
  getRowSelection: () => TableRowSelection<Recordable>;
  setShowPagination: (show: boolean) => Promise<void>;
  getShowPagination: () => boolean;
  getShowColumns: () => TableColumnPropsType[];
  updateTableDataRecord: (rowKey: string | number, record: Recordable) => Recordable | void;
  deleteTableDataRecord: (rowKey: string | number | string[] | number[]) => void;
  insertTableDataRecord: (record: Recordable, index?: number) => Recordable | void;
  findTableDataRecord: (rowKey: string | number) => Recordable | void;
  expandAll: () => void;
  expandRows: (keys: string[]) => void;
  collapseAll: () => void;
  scrollTo: (pos: string) => void; // pos: id | "top" | "bottom"
}
