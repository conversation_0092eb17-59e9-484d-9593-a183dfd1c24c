export default {
  okText: 'OK',
  image: 'Image',
  more: 'more',
  to: 'To',
  from: 'From',
  comment: 'Comment',
  addContent: 'add {content}',
  emoticon: 'Emoticon',
  closeText: 'Close ',
  clearText: 'Clear',
  cancelText: 'Cancel',
  none: 'None',
  inputText: 'Please enter ',
  chooseText: 'Please choose ',
  queryText: 'Search',
  saveText: 'Save',
  resetText: 'Reset',
  delText: 'Delete',
  deledText: 'Deleted ',
  handoverText: 'Handover ',
  addDate: 'Add date',
  addTime: 'Add time',
  download: 'Download',
  editText: 'Edit',
  editedText: 'Edited ',
  searchText: 'Search',
  teamworkChecklist: 'List',
  archivedInventory: 'Archived list',
  predecessorTask: 'Pre-task',
  autoCompletion: 'Auto-Completion',
  verificationDepartment: 'Verification department',
  loadingText: 'Loading...',
  loadErrorText: 'Loading failed, click to retry',
  noMoreDataText: 'No more',
  taskList: 'Task List',
  taskTemplate: 'Template',
  openText: 'Open ',
  resDepartment: 'Responsible department',
  viewText: 'View',
  reply: 'Reply',
  redo: 'Refresh',
  send: 'Send',
  color: 'Color',
  back: 'Back',
  archive: 'Archive',
  cancelArchive: 'Cancel Archive',
  groupTitle: 'Group title',
  deleteListTip: 'Delete list "{name}"?',
  archiveTip: 'Archive list "{name}"?',
  archiveTipContent:
    'Archiving does not change the status of the task, and archived lists can be viewed in the collapsed list',
  account: 'Account',
  email: 'Email',
  phone: 'Phone',
  information: 'Information',
  notify: 'Notify',
  markRead: 'Mark read',
  readAll: 'Read all',
  operationSuccess: 'Operation Success',
  operationFail: 'Operation failed',
  personCount: '{count} {text}',
  loading: 'Loading...',
  add: 'Add',
  added: 'Added ',
  tip: 'Tip',
  askDelete: 'Are you sure you want to delete',
  askCancel: 'Are you sure you want to cancel',
  askEdit: 'Are you sure you want to edit',
  name: 'Name',
  title: 'Title',
  description: 'Description',
  create: 'Create ',
  removeAttachmentTip: 'Are you sure you want to remove attachment "{name}"?',
  user: 'User',
  task: 'Task',
  project: 'Project',
  time: 'Time',
  hours: 'Hours',
  detail: 'Detail',
  entry: ' Entry',
  pin: 'Pin',
  action: {
    start: 'Start',
    stop: 'Stop',
    complete: 'Complete',
    reject: 'Reject',
    confirm: 'Confirm',
    cancel: 'Cancel',
  },
  status: {
    status: 'Status',
    notStarted: 'Not Started',
    verifying: 'Verifying',
    onHold: 'On Hold',
    ongoing: 'Ongoing',
    completed: 'Completed',
    rejected: 'Rejected',
    cancelled: 'Cancelled',
  },
  is: 'Is',
  filter: 'Filter',
  value: 'value',
  verify: 'Please Fill In',
  countdown: {
    normalText: 'Get SMS code',
    sendText: 'Reacquire in {0}s',
  },
  form: {
    putAway: 'Put away',
    unfold: 'Unfold',
    maxTip: 'The number of characters should be less than {0}',
    apiSelectNotFound: 'Wait for data loading to complete...',
  },
  modal: {
    cancelText: 'Cancel',
    okText: 'Confirm',
    close: 'Close',
    maximize: 'Maximize',
    restore: 'Restore',
  },
  table: {
    settingDens: 'Density',
    settingDensDefault: 'Default',
    settingDensMiddle: 'Middle',
    settingDensSmall: 'Compact',
    settingColumn: 'Column settings',
    settingColumnShow: 'Column display',
    settingIndexColumnShow: 'Index Column',
    settingSelectColumnShow: 'Selection Column',
    settingFixedLeft: 'Fixed Left',
    settingFixedRight: 'Fixed Right',
    settingFullScreen: 'Full Screen',
    index: 'Index',
    total: 'total of {total}',
  },
  subtask: {
    subtask: 'Subtask',
    syncEndTime: 'Sync end time',
    viewDetails: 'View details',
    taskName: 'Task Name',
    resPerson: 'Assingee',
    verifier: 'Verifier',
    watcher: 'Watcher',
    creator: 'Creator',
    startTime: 'Start Time',
    endTime: 'End Time',
    addSubtask: 'Add subtask',
    createSubtaskTip: 'Add subtasks using task templates',
    AIcreateSubtaskTip: 'Task break down using AI',
    aiRebuild: 'Re-Generate',
  },
  upload: {
    save: 'Save',
    upload: 'Upload',
    imgUpload: 'ImageUpload',
    uploaded: 'Uploaded',

    operating: 'Operating',
    del: 'Delete',
    download: 'download',
    saveWarn: 'Please wait for the file to upload and save!',
    saveError: 'There is no file successfully uploaded and cannot be saved!',

    preview: 'Preview',
    choose: 'Select the file',

    accept: 'Support {0} format',
    acceptUpload: 'Only upload files in {0} format',
    maxSize: 'A single file does not exceed {0}MB ',
    maxSizeMultiple: 'Only upload files up to {0}MB!',
    maxNumber: 'Only upload up to {0} files',

    legend: 'Legend',
    fileName: 'File name',
    fileSize: 'File size',
    fileStatue: 'File status',

    startUpload: 'Start upload',
    uploadSuccess: 'Upload successfully',
    uploadError: 'Upload failed',
    uploading: 'Uploading',
    uploadWait: 'Please wait for the file upload to finish',
    reUploadFailed: 'Re-upload failed files',
    modalTitle: 'Avatar upload',
    cutMessageWarning: 'Please select the correct image',
  },
  tag: {
    tag: 'Tags',
    delText: 'Delete',
    copy: 'Copy',
    rename: 'Rename',
    renameTag: 'Rename tag',
    changeColor: 'Change color',
    public: 'Public',
    private: 'Private',
    all: 'All',
    tagCreateTip: 'Search or create tags',
    create: 'Create',
    copySuccessTip: 'Copy successfully',
    setPublic: 'Make public',
    setPrivate: 'Make  private',
  },
  priority: {
    priority: 'Priority',
    Urgent: 'Urgent',
    High: 'High',
    Normal: 'Normal',
    Low: 'Low',
  },
  userSearch: {
    searchingText: 'Searching...',
    selected: 'Selected',
    suggestion: 'Suggestion',
  },
  collaborationDocument: {
    collaborativeDoc: 'Collaborative document',
    collaborativeName: 'Document Name',
    collaborativeLink: 'Document Link',
  },
  uploadFile: {
    attachment: 'Attachment',
    addAttachment: 'Add Attachment',
    attachmentCount: '{count} attachments',
    fileName: 'File Name',
    fileSize: 'File Size',
    uploadTime: 'Upload Time',
    uploadUser: 'Upload User',
  },
  folder: {
    moveTo: 'Move to',
    askMoveTo: 'Are you sure you want to move to ',
    newFolder: 'New Folder',
    removeFolder: 'Remove Folder',
    updateFolder: 'Update Folder',
  },
  board: {
    createTask: 'Create Task',
    createGroup: 'Create Group',
    deleteGroupTip: 'When you delete a group, the tasks within the group return to the default group',
  },
  ai: {
    summaryTask: 'Summarize this task',
  },
  trackTime: {
    WorkHourCategory: 'Work Hour Category',
    splitWorkHour: 'Split Work Hour',
    TotalDuration: 'Total Duration',
    trackTime: 'Track Time',
    plannedTrackingTime: 'Planned Hour',
    actualTrackingTime: 'Actual Hour',
    planned: 'Planned',
    actual: 'Actual',
    WorkHourExecutor: 'Work Hour Executor',
    ExecutionTime: 'Execution Time',
    WorkHours: 'Work Hours',
    WorkHoursTotal: 'Work Hours Total',
    WorkProgress: 'Work Progress',
    WorkHourType: 'Work Hour Type',
    WorkHoursDetails: 'Work Hours Details',
    ProgressOverview: 'Progress overview',
    HoursRecord: 'Hours Record',
    total: 'Total',
    everyDay: 'Every day',
  },
  seniorFilter: {
    clearAll: 'Clear All',
    field: 'Field',
    condition: 'Condition',
    addNewFilter: 'Add New Filter',
    advancedFilters: 'Advanced Filters',
    saveTheFilterResults: 'Save active filters',
    saveFilter: 'Save filters',
    editTheFilterResults: 'Edit active filters',
    filterName: 'Filter Criteria Name',
    savedFilters: 'Saved filters',
    verifyFilterName: '{verify}Filter Criteria Name',
  },
  emoticons: {
    Done: 'Done',
    FingerHeart: 'Finger heart',
    Like: 'Like',
    Ok: 'OK',
    Flower: 'Flower',
  },
  datePicker: {
    startDate: 'Start date',
    dueDate: 'Due date',
    Today: 'Today',
    Yesterday: 'Yesterday',
    Later: 'Later',
    Tomorrow: 'Tomorrow',
    ThisWeekend: 'This weekend',
    NextWeek: 'Next week',
    NextWeekend: 'Next weekend',
    ThisMonth: 'This Month',
    LastMonth: 'Last Month',
    ThisYear: 'This Year',
    LastYeat: 'Last Year',
    ThisWeek: 'This Week',
    LastWeek: 'Last Week',
    '2Weeks': '2 weeks',
    '4Weeks': '4 weeks',
    DueReminder: 'Due Reminder',
    RepeatSetup: 'Repeat Setup',
    placeholder: 'Please select dates',
  },
  history: {
    createTask: 'Create Task',
    deleteTask: 'Delete Task',
    assignPeople: 'Assign people',
    endTime: 'End Time',
    taskDescription: 'Task Description',
    taskProgress: 'Task Progress',
    uploadAttachment: 'Attachment',
    attachment: 'Attachment',
    collaborativeList: 'Collaborative list',
  },
  repeatPicker: {
    custom: 'Custom',
    frequency: 'Frequency',
    recurrenceRule: 'Recurrence Rule',
    every: 'Every',
    day: 'day',
    days: 'days',
    weeks: 'weeks',
    months: 'months',
    years: 'years',
    byDay: 'By day',
    byWeek: 'By week',
    Sunday: 'Sunday',
    Monday: 'Monday',
    Tuesday: 'Tuesday',
    Wednesday: 'Wednesday',
    Thursday: 'Thursday',
    Friday: 'Friday',
    Saturday: 'Saturday',
    '1st': '1st',
    '2nd': '2nd',
    '3rd': '3rd',
    numth: '{n}th',
  },
  RemindPicker: {
    beforeDueDay: '{n} {unit} before',
    onTheDay: 'On day of event at {time}',
    custom: 'Custom',
    reminderRule: 'Reminder Rule',
  },
  SynchronizeAccounts: {
    userManagement_name: 'Name',
    userManagement_email: 'Email',
    userManagement_phone: 'Phone',
    userManagement_department: 'Department',
    userManagement_synced: 'Synced',
    userManagement_inducts: 'Import',
    userManagement_inductsSuccess: 'Import Success,The user is synced',
    userManagement_nameMatching: 'Name Match',
    userManagement_nameSynced: 'Synced',
    userManagement_phoneClash: 'Phone Clash',
    userManagement_phoneRepeat: 'Phone Repeat',
    userManagement_emailRepeat: 'Email Repeat',
    userManagement_emailClash: 'Email Clash',
    userManagement_remove: 'Remove',
    userManagement_okRemover: 'Are you sure you want to remove',
    userManagement_binding: 'Binding Relationships',
    userManagement_binding1: 'Will Not Be Bound',
    userManagement_binding2: 'And',
    userManagement_binding3: 'Establish a Binding Relationship',
    userManagement_binding4: 'Organizational Users Of WeCom',
    userManagement_binding5: 'WeCom Users Who Synchronized This Time',
    synchronize_accounts: 'Synchronize Accounts',
  },
  lineUp: {
    addTip: 'Add your most important tasks here.',
    addTaskTip: 'Add to LineUp',
    recent: 'Recent',
  },
  showClosed: {
    title: 'Show Closed',
    closed: 'Closed',
  },
  poster: {
    generating: 'Generating...',
  },
  tour: {
    previous: 'Previous',
    next: 'Next',
  },
  taskType: {
    title: 'Task Type',
  },
};
