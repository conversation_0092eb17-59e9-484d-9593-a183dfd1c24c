<template>
  <div :class="prefixCls">
    <slot name="insertFooter"></slot>
    <Button size="large" v-bind="cancelButtonProps" @click="handleCancel" v-if="showCancelBtn">
      {{ t(cancelText) }}
    </Button>
    <slot name="centerFooter"></slot>
    <Button
      :type="okType"
      size="large"
      @click="handleOk"
      :loading="confirmLoading"
      v-bind="okButtonProps"
      v-if="showOkBtn"
    >
      {{ t(okText) }}
    </Button>
    <slot name="appendFooter"></slot>
  </div>
</template>
<script lang="ts" setup name="BasicModalFooter">
  import { Button } from 'ant-design-vue';
  import { basicProps } from '../props';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useDesign } from '/@/hooks/web/useDesign';

  defineProps(basicProps());
  const emit = defineEmits(['ok', 'cancel']);

  const { t } = useI18n();
  const { prefixCls } = useDesign('basic-modal-footer');
  function handleOk(e: Event) {
    emit('ok', e);
  }

  function handleCancel(e: Event) {
    emit('cancel', e);
  }
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-basic-modal-footer';
  .@{prefix-cls} {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .ant-btn {
      &.ant-btn-lg {
        font-size: 14px;
        height: 36px;
      }
      & + .ant-btn:not(.ant-dropdown-trigger) {
        margin-inline-start: 12px;
      }
    }
  }
</style>
