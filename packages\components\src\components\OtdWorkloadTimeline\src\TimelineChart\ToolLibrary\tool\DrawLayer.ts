import { Recordable } from '/#/global';

export class DrawLayer {
  // 绘制队列
  private drawQueue: Recordable<(() => void)[]> = {};
  constructor() {}
  // 设置绘制队列
  addQueue(task: () => void, zIndex = 0) {
    if (this.drawQueue[zIndex]) {
      this.drawQueue[zIndex].push(task);
    } else {
      this.drawQueue[zIndex] = [task];
    }
  }
  // 执行队列
  draw() {
    const sort = Object.keys(this.drawQueue).sort((a, b) => Number(a) - Number(b));
    sort.map((key) => {
      this.drawQueue[key].map((task) => task());
    });
    this.drawQueue = {};
  }
}
