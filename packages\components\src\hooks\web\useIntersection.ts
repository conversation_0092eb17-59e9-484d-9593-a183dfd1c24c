import { useEventListener } from '/@/hooks/event/useEventListener';

export function useIntersection(
  dom: Element,
  config: { load?: (dom: Element) => void; unload?: (dom: Element) => void } = {},
) {
  // 视口距离
  const space = 150;
  // 创建可视窗口监听器
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          (entry.target as HTMLElement).dataset.show = 'true';
          config.load?.(entry.target);
        } else {
          // 检测超出视口距离
          const boundingClientRect = entry.target.getBoundingClientRect();
          const { y: viewStart, height } = dom.getBoundingClientRect();
          const viewEnd = viewStart + height;

          if (boundingClientRect.top > viewStart + space || boundingClientRect.top < viewEnd - space) {
            (entry.target as HTMLElement).dataset.show = 'false';
            config.unload?.(entry.target);
          }
        }
      });
    },
    {
      root: dom, // 视窗元素
      rootMargin: `${space}px`, // Start loading when the element is 150px from the viewport
      threshold: 0.1, // Trigger when 10% of the element is in view
    },
  );

  return observer;
}

export type ScrollIntersectionType = {
  top: number;
  dom: HTMLElement | null;
  init(): void;
  observe(dom: HTMLElement): void;
};
export function useScrollIntersection(root: HTMLElement, rowHeight = 51): () => ScrollIntersectionType {
  const rowsPerPage = 15; // 渲染行数
  const buffer = 3; // 预渲染行数

  function handler(_this) {
    if (_this.dom.clientHeight <= 0) {
      _this.dom.dataset.start = 0;
      _this.dom.dataset.end = 0;
      return;
    }

    const scrollTop = root.scrollTop - _this.top;
    const startRow = Math.floor(scrollTop / rowHeight) - buffer;
    const endRow = startRow + rowsPerPage + buffer;
    _this.dom.dataset.start = startRow.toString();
    _this.dom.dataset.end = endRow.toString();
  }
  return () => ({
    top: 0,
    dom: null,
    init() {
      const domInfo = this.dom!.getBoundingClientRect();
      const rooInfo = root.getBoundingClientRect();
      this.top = domInfo.y - rooInfo.y + root.scrollTop;
      handler(this);
    },
    observe(dom: HTMLElement) {
      this.dom = dom;
      this.init();
      useEventListener({
        el: root,
        name: 'scroll',
        listener: () => handler(this),
        wait: 0,
      });
    },
  });
}
