<template>
  <BasicModal
    :z-index="1020"
    :title="modalTitle"
    :can-fullscreen="false"
    :mask-closable="false"
    :after-close="handleAfterClose"
    :ok-button-props="{ disabled: (entryForm.workHour ?? 0) <= 0 }"
    :min-height="10"
    @register="register"
    @ok="handleConfirm"
  >
    <Form class="otd-track-time-entry" :model="entryForm" layout="vertical">
      <div class="otd-track-time-entry__header">
        <!-- 工时执行者 -->
        <FormItem class="w-160px" :label="t('common.trackTime.WorkHourExecutor')">
          <div class="otd-box-left">
            <OtdUserSearch
              v-model:value="entryForm.user"
              :placeholder="t('common.trackTime.WorkHourExecutor')"
              show-text
              :clearable="isPlanedModal"
            />
          </div>
        </FormItem>
        <!-- 执行时间 -->
        <FormItem
          class="otd-track-time-entry__header-date"
          :label="t('common.trackTime.ExecutionTime')"
          v-if="entryForm.isSplit"
        >
          <GetComponent class="header-date-full" v-model:value="entryForm.workDate" :allow-clear="false" />
        </FormItem>
      </div>
      <!-- 工时数 -->
      <FormItem>
        <template #label>
          <div class="otd-box-left" style="column-gap: 10px">
            <span>{{ t('common.trackTime.WorkHours') }}</span>
            <Checkbox
              v-model:checked="entryForm.isSplit"
              @change="handleChangeSplit"
              v-if="isPlanedModal && !hideSplit"
            >
              {{ t('common.trackTime.splitWorkHour') }}
            </Checkbox>
          </div>
        </template>
        <InputNumber
          class="w-full"
          v-model:value="entryForm.workHour"
          :placeholder="t('common.trackTime.WorkHours')"
          :precision="1"
          :min="0"
          :step="0.5"
          :addon-after="t('common.hours')"
          size="large"
        >
          <template #addonBefore v-if="entryForm.isSplit">
            <Select v-model:value="countType" :options="countList" />
          </template>
        </InputNumber>
      </FormItem>
      <!-- 工时类别 -->
      <FormItem :label="t('common.trackTime.WorkHourCategory')" v-if="(categoryOptions ?? []).length > 1">
        <Select
          class="otd-track-time-select"
          v-model:value="entryForm.categoryCode"
          :options="categoryOptions"
          :placeholder="t('common.trackTime.WorkHourCategory')"
          size="large"
          show-search
        ></Select>
      </FormItem>
      <!-- 工作进度 -->
      <FormItem :label="t('common.trackTime.WorkProgress')" v-if="!isPlanedModal">
        <Textarea
          class="w-full"
          v-model:value="entryForm.remark"
          :auto-size="{ minRows: 5, maxRows: 5 }"
          :placeholder="t('common.trackTime.WorkProgress')"
          :maxlength="1000"
          show-count
        />
      </FormItem>
    </Form>
  </BasicModal>
</template>
<script lang="tsx" setup>
  import { watchEffect, onUnmounted, computed, PropType, unref } from 'vue';
  import { Form, FormItem, Input, InputNumber, RangePicker, DatePicker, Select, Checkbox } from 'ant-design-vue';
  import { BasicModal } from '/@/components/BasicModal';
  import { useTrackTimeEntry } from './useTrackTimeEntry';
  import { HourTypeEnum } from '../props';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { OtdUserSearch } from '/@/components/OtdUserSearch';
  import { LabelValueOptions } from '/#/global';

  const Textarea = Input.TextArea;

  const props = defineProps({
    defaultDate: {
      type: Array as unknown as PropType<[string | undefined, string | undefined]>,
      default: undefined,
    },
    isPlaned: {
      type: Boolean,
      default: false,
    },
    categoryOptions: {
      type: Array as PropType<LabelValueOptions>,
    },
    defaultCategory: {
      type: String,
    },
  });

  const emit = defineEmits(['submit', 'register', 'reload']);
  const { t } = useI18n();

  const isPlanedModal = computed(() => {
    if (entryForm.hourType !== undefined) {
      return entryForm.hourType === HourTypeEnum.Plan;
    }
    return props.isPlaned;
  });

  const modalTitle = computed(() => {
    return (
      (unref(isPlanedModal) ? t('common.trackTime.plannedTrackingTime') : t('common.trackTime.actualTrackingTime')) +
      t('common.entry')
    );
  });

  const [
    register,
    {
      defaultForm,
      hideSplit,
      entryForm,
      countType,
      countList,
      getDefaultDate,
      handleAfterClose,
      handleConfirm,
      handleChangeSplit,
    },
  ] = useTrackTimeEntry();

  const GetComponent = (_props) => {
    return entryForm.id ? <DatePicker {..._props} /> : <RangePicker {..._props} />;
  };

  const unWatchDefaultDate = watchEffect(() => {
    defaultForm.workDate = getDefaultDate(props.defaultDate, entryForm);
    entryForm.workDate = getDefaultDate(props.defaultDate, entryForm);
  });

  onUnmounted(() => {
    unWatchDefaultDate();
  });
</script>
<style lang="less" scoped>
  .otd-track-time-entry {
    &__header {
      display: flex;
      column-gap: 10px;
      :deep(.ant-form-item) {
        &:first-of-type {
          width: 160px;
        }
      }
      &-date {
        flex: 1;
        .header-date-full {
          width: 100%;
        }
      }
    }
    :deep(.ant-input-number-group-addon) {
      .ant-select {
        width: 100px;
        .ant-select-selector {
          border-width: 0 !important;
          box-shadow: unset !important;
        }
      }
    }

    &.ant-form {
      .ant-form-item {
        margin-bottom: 16px;
      }
      :deep(> .ant-form-item) {
        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }
    .otd-track-time-select {
      :deep(.ant-select-selector) {
        font-size: 14px;
      }
    }
  }
</style>
