import { computed, ref, unref } from "vue";
import { LockInfo } from "/#/utils";
import { LOCK_INFO_KEY } from "/@/enums/cacheEnum";
import { Persistent } from "/@/utils/cache/persistent";
import { Nullable } from "/#/global";

const lockInfo = ref<Nullable<LockInfo>>(Persistent.getLocal(LOCK_INFO_KEY));

export function useLockStorage() {
  return {
    getLockInfo: computed(() => unref(lockInfo)),
    setLockInfo(info: LockInfo) {
      lockInfo.value = Object.assign({}, unref(lockInfo), info);
      Persistent.setLocal(LOCK_INFO_KEY, unref(lockInfo), true);
    },
    resetLockInfo() {
      Persistent.removeLocal(LOCK_INFO_KEY, true);
      lockInfo.value = null;
    },
    // Unlock
    unLock(password?: string) {
      if (unref(lockInfo)?.pwd === password) {
        this.resetLockInfo();
        return true;
      } else {
        return false;
      }
    },
  };
}
