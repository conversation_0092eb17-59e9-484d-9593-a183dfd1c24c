import { PropType } from 'vue';
import { ISetTaskDocLinkInputDetail } from './type';
import { TableColumnPropsType } from '/@/components/OtdTable';
import { mutable } from '/@/utils/props';

export const getProps = () => ({
  value: {
    type: Array as PropType<ISetTaskDocLinkInputDetail[]>,
    default: undefined,
  },
  headerText: {
    type: String,
  },
  tableColumn: {
    type: Array as PropType<TableColumnPropsType[]>,
    default: [],
  },
  allocation: {
    type: Object as PropType<Record<string, any>>,
    default: {},
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
  },
});
const emit = ['update:value', 'change', 'remove'] as const;
export const getEmits = () => mutable(emit);
