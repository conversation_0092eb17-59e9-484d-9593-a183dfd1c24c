import { TaskPriorityEnum } from '../../OtdPriority';
import { UserOptionItemType } from '../../OtdUserSearch';
import { getProps, getEmits } from './props';
import { ExtractPropTypes } from 'vue';
import { EmitType, Recordable } from '/#/global';

export interface ISetSubtaskInputDetail extends Recordable {
  id?: string;
  title?: string;
  responsibleUser?: UserOptionItemType | null;
  date?: any;
  status?: number;
  priority?: TaskPriorityEnum | null;
}
export type SubtaskPropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
export type SubtaskEmitsType = EmitType<ReturnType<typeof getEmits>[number]>;
