<template>
  <BasicDropdown
    placement="bottom"
    :trigger="['click']"
    :dropMenuList="localeList"
    :selectedKeys="selectedKeys"
    @menu-event="handleMenuEvent"
    @click.stop
  >
    <span class="otd-language-picker" :class="{ 'is-light': isLight, 'otd-layout-action': isLarge }">
      <i class="otdIconfont otd-icon-material-symbols_language"></i>
      <span v-if="showText" class="ml-1">{{ getLocaleText }}</span>
    </span>
  </BasicDropdown>
</template>
<script lang="ts" setup>
  import type { LocaleType } from '/#/config';
  import { ref, watchEffect, unref, computed } from 'vue';
  import { BasicDropdown, DropMenu } from '/@/components/BasicDropdown';
  import { useLocale } from '/@/locales/useLocale';
  import { localeList } from '/@/dictionary/locale';

  const props = defineProps({
    /**
     * 显示文本
     */
    showText: { type: Boolean, default: false },
    /**
     * 刷新页面
     */
    reload: { type: Boolean, default: false },
    isLight: {
      type: Boolean,
    },
    isLarge: {
      type: Boolean,
      default: true,
    },
  });

  const selectedKeys = ref<string[]>([]);

  const { changeLocale, getLocale } = useLocale();

  const getLocaleText = computed(() => {
    const key = selectedKeys.value[0];
    if (!key) {
      return '';
    }
    return localeList.find((item) => item.event === key)?.text;
  });

  watchEffect(() => {
    selectedKeys.value = [unref(getLocale)];
  });

  async function toggleLocale(lang: LocaleType | string) {
    await changeLocale(lang as LocaleType);
    selectedKeys.value = [lang as string];
    props.reload && location.reload();
  }

  function handleMenuEvent(menu: DropMenu) {
    if (unref(getLocale) === menu.event) {
      return;
    }
    toggleLocale(menu.event as string);
  }
</script>

<style lang="less">
  .otd-language-picker {
    display: flex;
    column-gap: 8px;
    align-items: center;
    &:not(.otd-layout-action) {
      .otdIconfont {
        font-size: 18px;
      }
    }
    &.is-light {
      color: var(--otd-white-text);
    }
  }
</style>
