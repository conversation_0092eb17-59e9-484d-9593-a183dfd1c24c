import AntdTheme from 'ant-design-vue/es/theme';
import { SeedToken } from 'ant-design-vue/es/theme/internal';
import { reactive } from 'vue';
import { PRIMARY_COLOR } from '/@/setting';

/**
 *
 * @param dark booleam 是否是黑夜模式
 * @param colorPrimary string 主题色
 * @returns
 */
export const darkMode = reactive({
  mode: false,
});
export function setThemeClor(dark = false, colorPrimary: string = PRIMARY_COLOR) {
  darkMode.mode = dark;
  const _AntdTheme: typeof AntdTheme = (AntdTheme as any)?.default ?? AntdTheme;

  const token: SeedToken = {
    ..._AntdTheme.defaultConfig.token,
    colorPrimary,
    colorError: '#e34d59',
    colorSuccess: '#27ae60',
    colorWarning: '#ed7b2f',
  };

  const modifyVars = (dark ? _AntdTheme.darkAlgorithm : _AntdTheme.defaultAlgorithm)(token);
  return modifyVars;
}
