<template>
  <div class="otd-workload-timeline" v-loading="loading">
    <canvas id="otd-timeline-canvas" draggable="false"></canvas>
  </div>
</template>
<script lang="ts" setup>
  import { unref, watch } from 'vue';
  import { useWorkloadTimeline } from './useWorkloadTimeline';
  import { useRootSetting } from '/@/storage/projectConfigStorage';
  import { getEmits, getProps } from './props';

  const props = defineProps(getProps());
  defineEmits(getEmits());

  const { getDarkMode } = useRootSetting();

  const { timeline, loading, getTimelineData } = useWorkloadTimeline();

  watch(
    () => props.dateRange,
    (value) => {
      getTimelineData(value);
    },
    { deep: true },
  );

  watch(
    () => unref(getDarkMode),
    (value) => unref(timeline)?.setTheme(value as any),
  );

  defineExpose({
    update: (record?) => {
      const time = unref(timeline);
      if (record && time) {
        const userId = record.responsibleUserId;
        const previousUserId = record.previousResponsibleUserId ?? userId;
        const currentTimeline = time?.getTimelineMap.get(record.id);
        const { timelineData } = time.getConfig;
        const { row = 0, column = 0 } = currentTimeline?.getInfo ?? {};
        if (currentTimeline && previousUserId === userId) {
          timelineData[row].taskItems[column] = record;
        } else {
          const data = timelineData.find((item) => item.userId === userId);
          if (currentTimeline) {
            timelineData[row].taskItems.splice(column, 1);
          }
          if (data) {
            data.taskItems ? data.taskItems.push(record) : (data.taskItems = [record]);
          }
        }
      }
      time?.update();
    },
    selectTimeline: (record) => {
      unref(timeline)?.selectTimeline?.(record);
    },
  });
</script>
<style lang="less" scoped>
  .otd-workload-timeline {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    border: 1px solid var(--otd-border-gray);
    border-radius: var(--otd-default-radius);
  }
</style>
