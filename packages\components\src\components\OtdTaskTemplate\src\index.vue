<template>
  <div>
    <OtdMoreAction :expand-number="1" :actions="topActions" action-type="icon" />
    <TemplateDialog
      ref="templateRef"
      :folderRequest="folderRequest"
      :getTaskList="getTaskList"
      :draggable="draggable"
      :title="t('common.taskTemplate')"
      @drag-end="dragEnd"
      @confirm="handleConfirm"
    />
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { getProps, getEmits } from './props';
  import { type MoreActionItem } from '/@/components';
  import { OtdMoreAction } from '/@/components/OtdMoreAction';
  import TemplateDialog from './components/templateDialog.vue';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const props = defineProps(getProps());
  const emit = defineEmits(getEmits());

  const topActions: MoreActionItem[] = [
    {
      id: 'add',
      name: props.placeholder,
      icon: 'otd-icon-add-2',
      action: () => {
        handleSelectTemplate();
      },
      disabled: () => {
        return props.disabled;
      },
    },
  ];

  const templateRef = ref();
  function handleSelectTemplate() {
    if (props.disabled) return false;
    templateRef.value.openDialog();
  }
  function handleConfirm(...arg) {
    emit('confirm', ...arg);
  }

  function dragEnd(val) {
    emit('dragEnd', val);
  }
</script>
<style lang="less" scoped></style>
