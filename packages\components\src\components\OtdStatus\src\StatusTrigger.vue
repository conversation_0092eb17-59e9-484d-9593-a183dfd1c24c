<template>
  <div
    class="otd-status"
    :class="[`otd-status__${currentStatus?.color}`, disabled && 'otd-status-disabled', isText && 'otd-status-text']"
  >
    <OtdIconPark :type="iconType" :key="iconType" />
    <!-- <div class="otd-status-dot-outer" /> -->

    <div class="otd-status-text" v-if="!hideText">
      <span>{{ currentStatus?.title }}</span>
      <i
        v-if="arrow && !disabled"
        class="otdIconfont otd-icon-a-Statusdefault"
        :class="rotate ? 'iconRotate' : 'iconReset'"
      ></i>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed, unref } from 'vue';
  import { getProps } from './props';
  import { OtdStatusOptionType } from './type';
  import { TaskStatusEnum, useStatus } from './useStatus';
  import { OtdIconPark } from '/@/components/BasicIcon';

  const props = defineProps({
    ...getProps(),
    rotate: { type: Boolean, default: false },
    isText: { type: Boolean, default: false },
    hideText: { type: Boolean, default: false },
  });

  const { defaultOptions } = useStatus();

  const getOptions = computed<OtdStatusOptionType<TaskStatusEnum>[]>(() => {
    return props.options ?? defaultOptions;
  });
  const currentStatus = computed(() => {
    return unref(getOptions).find((item) => item.id === props.value);
  });
</script>
<style lang="less" scoped>
  .otd-status {
    cursor: pointer;
    line-height: 1;
    width: fit-content;
    padding: 6px 10px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    user-select: none;
    --color: var(--otd-status-UnStarted-color);
    &.otd-status-disabled {
      cursor: not-allowed;
    }

    .otd-status-dot-outer {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2px;
      border: 2px solid var(--color);
      &::after {
        content: '';
        width: 100%;
        height: 100%;
        background-color: var(--color);
        border-radius: 50%;
        display: block;
      }
    }
    .otd-status-text {
      margin-left: 5px;
      font-size: 14px;
      color: var(--color);
      .otdIconfont {
        font-size: 12px;
        display: inline-block;
        transition: transform 0.5s;
        transform-origin: center 5px;
        margin-left: 4px;
      }
      .iconRotate {
        transform: rotate(180deg);
        -webkit-transform: rotate(-180deg);
      }
      .iconReset {
        transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
      }
    }
    .otd-svg-icon,
    .i-icon {
      color: var(--color);
    }
    &&__grey {
      // 灰色 未开始，已取消
      background-color: var(--otd-status-UnStarted-bg);
      --color: var(--otd-status-UnStarted-color);
    }
    &&__blue {
      // 蓝色 进行中
      background-color: var(--otd-status-OnGoing-bg);
      --color: var(--otd-status-OnGoing-color);
    }
    &&__orange {
      // 橘色 暂停

      background-color: var(--otd-status-Stopped-bg);
      --color: var(--otd-status-Stopped-color);
    }
    &&__red {
      // 红色 已退回
      background-color: var(--otd-status-Reject-bg);
      --color: var(--otd-status-Reject-color);
    }
    &&__green {
      // 绿色 已完成
      background-color: var(--otd-status-Close-bg);
      --color: var(--otd-status-Close-color);
    }
    &&__pink {
      // 粉色 待确认
      background-color: var(--otd-status-Done-bg);
      --color: var(--otd-status-Done-color);
    }
    &&.otd-status-text {
      background-color: transparent;
      padding: 0;
    }
  }
</style>
