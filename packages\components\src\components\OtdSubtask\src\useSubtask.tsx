import type { ISetSubtaskInputDetail, MoreActionItem } from '/@/components';
import type { SubtaskPropsType, SubtaskEmitsType } from './type';
import type { TableColumnPropsType } from '/@/components/OtdTable/src/OtdTable';
import { Input } from 'ant-design-vue';
import { OtdMoreAction } from '/@/components/OtdMoreAction';
import { OtdPriority } from '/@/components/OtdPriority';
import { OtdUserSearch } from '/@/components/OtdUserSearch';
import { OtdEditCellItem } from '/@/components/OtdEditCell';
import { OtdDatePicker } from '/@/components/OtdDatePicker';
import { OtdStatus, TaskStatusEnum } from '/@/components/OtdStatus';
import { computed, getCurrentInstance, nextTick, reactive, ref, unref } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { ERROR_COLOR } from '/@/setting';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';

export function useSubtask() {
  const { emit, props } = getCurrentInstance() as unknown as {
    props: SubtaskPropsType;
    emit: SubtaskEmitsType;
  };
  const { deleteConfirm } = useMessage();
  const { t } = useI18n();
  const { getGlobalProvide } = useGlobalConfig();
  const { getUserInfo } = getGlobalProvide();
  const aiTaskList = ref<ISetSubtaskInputDetail[]>();
  const aiTaskListLoading = ref<boolean>(false);
  const aiConfirmLoading = ref<boolean>(false);
  const createTaskConfig = reactive({
    isAdd: false,
    index: -1,
  });
  const subtaskData = computed({
    get() {
      return props.value ?? [];
    },
    set(value) {
      emit('update:value', value);
    },
  });

  // 表格操作按钮
  const operationActions: MoreActionItem[] = [
    // 同步截止时间
    {
      id: 'copy',
      name: `${t('common.subtask.syncEndTime')}`,
      icon: 'otd-icon-caozuo-xunhuan1',
      isHide: (data) => props.disabled || data.status === TaskStatusEnum.Close,
      action: (data) => {
        if (!data.planDone && !data.taskDates?.[1]) return;
        unref(subtaskData).map((task) => {
          if (task.status !== TaskStatusEnum.Close && task.id !== data.id) {
            const [start] = task.taskDates ?? [];
            const [, end] = data.taskDates;
            task.taskDates = [start, end];
            emit('sync', task);
          }
        });
      },
    },
    // 删除
    {
      id: 'delete',
      name: `${t('common.delText')}`,
      color: ERROR_COLOR,
      icon: 'otd-icon-a-catdeletesize24',
      isHide: (data) => props.disabled || data.status === TaskStatusEnum.Close,
      action: (data, list, index) => {
        deleteConfirm(() => {
          emit('delete', { data, index, list });
        });
      },
    },
    // 详情
    {
      id: 'detail',
      name: `${t('common.subtask.viewDetails')}`,
      icon: 'otd-icon-jt2',
      action: (data) => {
        emit('to-detail', data);
      },
    },
  ];

  function getDisabled(record) {
    return props.disabled || record.status === TaskStatusEnum.Close;
  }

  // 表格列
  const columns: TableColumnPropsType[] = [
    // 任务名称
    {
      title: `${t('common.subtask.taskName')}`,
      width: '410px',
      customCell: (record) => ({ colSpan: !record.isAdd ? 1 : 5 }),
      customRender({ record, index }) {
        const inputRef = ref();
        if (!record.id) nextTick(() => inputRef.value?.focus());
        return (
          <OtdEditCellItem offset={[-10, -16]} class={`otd-table-action ${record.isAdd && 'is-add-task'}`}>
            <Input
              ref={inputRef}
              disabled={getDisabled(record)}
              title={record.title}
              placeholder={t('common.subtask.taskName')}
              bordered={!!record.id}
              defaultValue={record.title}
              onBlur={({ target }) => handleJudgeEdit(target, record, 'title')}
              onPressEnter={({ target }) => target.blur({ target })}
            />
            <OtdMoreAction
              data={record}
              list={unref(subtaskData)}
              index={index}
              size="large"
              hide-expand-name
              destroy-popup-on-hide
              expand-number={4}
              actions={operationActions}
            />
          </OtdEditCellItem>
        );
      },
    },
    // 优先级
    {
      title: `${t('common.priority.priority')}`,
      width: '120px',
      customCell: (record) => ({ colSpan: !record.isAdd ? 1 : 0 }),
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem class="otd-table-action" offset={[-10, -16]}>
            <OtdPriority
              v-model:value={record.priority}
              tip-label={t('common.priority.priority')}
              disabled={getDisabled(record)}
              onChange={() => handleEdit(record, 'priority')}
              onClear={() => handleEdit(record, 'priority')}
            />
          </OtdEditCellItem>
        );
      },
    },
    // 开始时间，结束时间
    {
      title: `${t('common.time')}`,
      width: '280px',
      customCell: (record) => ({ colSpan: !record.isAdd ? 1 : 0 }),
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem class="otd-table-action" offset={[-10, -16]}>
            <OtdDatePicker
              disabled={getDisabled(record)}
              v-model:value={record.taskDates}
              hide-remind
              hide-repeat
              not-tip-color={{
                start: record.status !== TaskStatusEnum.UnStarted,
                end: record.status === TaskStatusEnum.Close,
              }}
              onChange={() => handleEdit(record, 'planSpanTime')}
            />
          </OtdEditCellItem>
        );
      },
    },

    // 负责人
    {
      title: `${t('common.subtask.resPerson')}`,
      width: '180px',
      customCell: (record) => ({ colSpan: !record.isAdd ? 1 : 0 }),
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem class="otd-table-action" offset={[-10, -16]}>
            <OtdUserSearch
              showText
              v-model:value={record.responsibleUser}
              disabled={getDisabled(record)}
              onChange={() => handleEdit(record, 'responsibleUserId')}
            />
          </OtdEditCellItem>
        );
      },
    },
    // 状态
    {
      title: `${t('common.status.status')}`,
      width: '150px',
      customCell: (record) => ({ colSpan: !record.isAdd ? 1 : 0 }),
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem offset={[-10, -16]}>
            <OtdStatus
              disabled={getDisabled(record)}
              v-model:value={record.status}
              before-select={(value) => handleEdit(record, 'status', value)}
            />
          </OtdEditCellItem>
        );
      },
    },
  ];
  // 表头操作
  const topActions: MoreActionItem[] = [
    // 添加子任务
    {
      id: 0,
      icon: 'otd-icon-a-Property128',
      name: t('common.subtask.addSubtask'),
      color: 'var(--otd-basic-text)',
      action: () => handleCreateTask(),
      disabled: () => unref(isCanAddTask),
    },
    // AI任务分解
    {
      id: 2,
      icon: 'otd-icon-a-Property1AI',
      name: t('common.subtask.AIcreateSubtaskTip'),
      color: 'var(--otd-basic-text)',
      action: () => {
        if (unref(aiConfirmLoading)) return;
        getAiSubtaskList();
      },
      disabled: () => unref(isCanAddTask),
    },
  ];

  const isCanAddTask = computed(() => props.disabled || createTaskConfig.isAdd);
  const handleCreateTask = () => {
    if (unref(isCanAddTask)) return;
    //没有添加成功的没有id，此时不让再添加
    let subtask = {
      title: '',
      status: TaskStatusEnum.UnStarted,
      responsibleUserId: getUserInfo!.userId,
      parentTaskId: props.detail?.id,
      isAdd: true,
    };

    subtaskData.value = [...(props.value ?? []), subtask];
    createTaskConfig.isAdd = true;
    createTaskConfig.index = subtaskData.value.length;
  };

  function handleCancelCreate() {
    const task = subtaskData.value[createTaskConfig.index];
    if (task.isAdd) {
      subtaskData.value.splice(createTaskConfig.index, 1);
    }
    createTaskConfig.isAdd = false;
    createTaskConfig.index = -1;
  }

  function getAiSubtaskList() {
    aiTaskListLoading.value = true;
    let params = {
      title: props.detail.title,
      description: props.detail.description
        ? props.detail.description
            .replace(/[\'\"\\\/\b\f\n\r\t]/g, '')
            .replace(/<\/?.+?\/?>/g, '')
            .replace(/&nbsp;/gi, '')
        : '',
    };
    props.generateSubtask?.(params).then((res) => {
      aiTaskListLoading.value = false;
      aiTaskList.value = res;
    });
  }

  function cancelAiSubtask() {
    aiTaskListLoading.value = false;
    aiConfirmLoading.value = false;
    aiTaskList.value = undefined;
  }
  function confirmAiSubtask() {
    const tasks = aiTaskList.value?.map(({ title }, index) => ({
      id: index.toString(),
      title,
      status: TaskStatusEnum.UnStarted,
      responsibleUserId: getUserInfo!.userId,
    }));
    aiConfirmLoading.value = true;
    emit('confirm-ai', tasks, props.detail, { resolve: cancelAiSubtask });
  }

  function handleJudgeEdit(target, record, key: string) {
    const { value } = target;
    if ((value && value === record[key]) || !value) return (target.value = record[key]);
    record[key] = value ?? record[key];
    record.isAdd = false;
    createTaskConfig.isAdd = false;
    return props.updateSubtask?.(record, key, target, true);
  }

  function handleEdit(record, key: string, value?) {
    return props.updateSubtask?.(record, key, value);
  }

  return {
    subtaskData,
    columns,
    createTaskConfig,
    handleCreateTask,
    handleCancelCreate,
    topActions,
    aiTaskList,
    aiTaskListLoading,
    aiConfirmLoading,
    cancelAiSubtask,
    getAiSubtaskList,
    confirmAiSubtask,
  };
}
