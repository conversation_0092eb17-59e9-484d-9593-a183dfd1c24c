import { useI18n } from "/@/hooks/web/useI18n";

/** 消息类型 */
export enum MessageType {
  BroadCast = 10,
  Common = 20,
  Custom = 30,
}

/** 消息等级 */
export enum MessageLevel {
  Warning = 10,
  Information = 20,
  Error = 30,
}

export interface NotifyListItem {
  id: string;
  avatar: string;
  // 通知的标题内容
  title: string;
  // 是否在标题上显示删除线
  titleDelete?: boolean;
  datetime: string;
  type: string;
  read?: boolean;
  description: string;
  clickClose?: boolean;
  extra?: string;
  color?: string;
}

export interface PagingNotificationListOutput {
  id?: string;
  /** 消息标题 */
  title?: string | undefined;
  /** 消息内容 */
  content?: string | undefined;
  messageType?: MessageType;
  readonly messageTypeDescription?: string | undefined;
  messageLevel?: MessageLevel;
  readonly messageLevelDescription?: string | undefined;
  /** 发送人 */
  senderId?: string;
  /** 创建时间 */
  creationTime?: string;
  /** 是否已读 */
  read?: boolean;
}

export interface NotifyTabItem {
  key: MessageType;
  name: string;
  list?: PagingNotificationListOutput[];
  total?: number;
  unreadlist?: NotifyListItem[];
}
const { t } = useI18n();
export const tabListData: NotifyTabItem[] = [
  {
    key: MessageType.Common,
    name: t("common.information"),
    list: [],
    total: 0,
  },
  {
    key: MessageType.BroadCast,
    name: t("common.notify"),
    list: [],
    total: 0,
  },
];
