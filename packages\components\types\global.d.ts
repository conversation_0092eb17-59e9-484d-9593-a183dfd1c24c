import { VNodeChild } from 'vue';

declare type OptionItemType = {
  id?: string;
  label?: string;
  value?: any;
  name?: string;
  img?: string;
  color?: string;
  private?: boolean;
  email?: string;
};

declare type Recordable<T = any> = Record<string, T>;

declare type Nullable<T> = T | null;
declare type Undefinedable<T> = T | undefined;

declare type QueryType = { keyword?: string; [K in string]: any };

declare interface Fn<T = any, R = T> {
  (...arg: T[]): R;
}

declare type EmitType<T = string> = (event: T, ...args: any[]) => void;
declare type TimeoutHandle = ReturnType<typeof setTimeout>;
declare type ComponentRef<T extends HTMLElement = HTMLDivElement> = ComponentElRef<T> | null;
declare type TargetContext = '_self' | '_blank';
declare type ElRef<T extends HTMLElement = HTMLDivElement> = Nullable<T>;
declare interface PromiseFn<T = any, R = T> {
  (...arg: T[]): Promise<R>;
}
declare type VueNode = VNodeChild | JSX.Element;
declare type RefType<T> = T | null;

declare type LabelValueOptions = {
  label: string;
  value: any;
  [key: string]: string | number | boolean;
}[];

declare interface ChangeEvent extends Event {
  target: HTMLInputElement;
}
declare type IntervalHandle = ReturnType<typeof setInterval>;

export namespace JSX {
  // tslint:disable no-empty-interface
  type Element = VNode;
  // tslint:disable no-empty-interface
  type ElementClass = ComponentRenderProxy;
  interface ElementAttributesProperty {
    $props: any;
  }
  interface IntrinsicElements {
    [elem: string]: any;
  }
  interface IntrinsicAttributes {
    [elem: string]: any;
  }
}
