<template>
  <div class="otd-folder-menu">
    <div class="otd-folder-menu__title" v-if="title">
      <div class="title-content otd-truncate" :title="title">{{ title }}</div>
      <i
        class="otdIconfont otd-icon-add-2 otd-layout-action"
        :title="t('common.newFolder')"
        @click.stop="handleCreateFolder()"
        v-if="!hideAction"
      ></i>
    </div>
    <OtdScrollbar class="flex-1 pr-10px">
      <!-- 目录列表 start -->
      <OtdFolderList
        ref="folderRef"
        v-model:expanded="expandedKeys"
        v-model:selected="selectedKeys"
        :data="data || treeData"
        :loadData="loadData || onLoadData"
        :actions="folderActions"
        :hideAction="hideAction"
        :folder-click="folderClick"
        :not-root="notRoot"
        @select="handleSelect"
        @expand="handleExpand"
      />
      <!-- 目录列表 end -->
    </OtdScrollbar>
    <!-- 创建-更新文件夹 -->
    <OtdCreateFolder :folder-request="folderRequest" @reload="handleRefrush" @register="registerCreateFolder" />
    <!-- 移动文件夹 -->
    <OtdMoveFolder :folder-request="folderRequest" @reload="handleRefrush(undefined)" @register="registerMoveFolder" />
  </div>
</template>
<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { OtdFolderList } from './components/OtdFolderList';
  import { OtdCreateFolder } from './components/OtdCreateFolder';
  import { OtdMoveFolder } from './components/OtdMoveFolder';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useFolder } from './useFolder';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { getProps } from './props';

  const { t } = useI18n();
  const props = defineProps(getProps());
  const emits = defineEmits(['update:expanded', 'update:selected', 'select', 'expand', 'reload']);
  const expandedKeys = ref<string[]>([]);
  const selectedKeys = ref<string[]>([]);
  watch(
    () => props.expanded,
    (value) => {
      expandedKeys.value = (value as string[]) || [];
    },
    { deep: true, immediate: true },
  );
  watch(
    () => props.selected,
    (value) => {
      selectedKeys.value = value as string[];
    },
    { deep: true, immediate: true },
  );

  const folderActions = computed(() => operationList.concat(props.actions));

  const {
    registerCreateFolder,
    registerMoveFolder,
    operationList,
    handleCreateFolder,
    handleRefrush,
    treeData,
    onLoadData,
  } = useFolder();

  handleRefrush(undefined);

  // 处理选择
  function handleSelect(node) {
    emits('update:selected', selectedKeys.value);
    emits('select', node);
  }
  // 展开收起事件
  function handleExpand(node) {
    emits('update:expanded', expandedKeys.value);
    emits('expand', node);
  }

  const folderRef = ref();
  defineExpose({
    expandedKeys,
    selectedKeys,
    folderRef,
  });
</script>
<style lang="less">
  .otd-folder-menu {
    display: flex;
    flex-direction: column;
    width: 240px;
    max-width: 240px;
    min-width: 240px;
    height: 100%;
    max-height: 100%;
    background-color: var(--otd-basic-bg);
    padding: 10px;
    border-right: 1px solid var(--otd-border-color);
    &__title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      .otd-layout-action {
        line-height: 1;
        padding: 4px;
        border-radius: var(--otd-small-radius);
      }
      .otd-layout-action + .otd-layout-action {
        margin-left: 4px;
      }
    }
    .title-content {
      color: var(--otd-basic-text);
      font-size: 16px;
      font-weight: bold;
    }
  }
</style>
