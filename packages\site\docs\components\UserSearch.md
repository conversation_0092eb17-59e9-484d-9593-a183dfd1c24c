# 角色搜索

<p style="font-size:26px">代码演示</p>

## 基础

<demo src="../demo/UserSearch/basic.vue" title="基础"></demo>

## 远程搜索

<demo src="../demo/UserSearch/remoteSearch.vue" title="远程搜索"></demo>

## 单选

<demo src="../demo/UserSearch/singleChoice.vue" title="单选"></demo>

## 文字样式

<demo src="../demo/UserSearch/txtSearch.vue" title="文字样式"></demo>

## 输入框样式

<demo src="../demo/UserSearch/inputStyle.vue" title="输入框样式"></demo>

## 属性

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| isIcon | 是否为图标按钮 | string | -- | false | 1.0 |
| isText | 是否是文字模式 | string | -- | false | 1.0 |
| isSimple | 是否是简易模式 | string | -- | false | 1.0 |
| placeholderText | 文字选择的文本和浮动提示 | string | -- | 请选择 | 1.0 |
| mode | 是否多选 | string | multiple,tags | undefined | 1.0 |
| placement | 弹出位置 | string | 参考 antd 的[Popover](https://4x.ant.design/components/popover-cn/) | bottomLeft | 1.0 |
| searchText | 搜索框的 placeholder | string | -- | 搜索 | 1.0 |
| isFull | 是否 100% | Boolean | -- | false | 1.0 |
| disabled | 是否禁用 | Boolean | -- | false | 1.0 |
| triggerClass | 已选择用户样式 class | string | -- | -- | 1.0 |
| icon | icon 模式下的按钮图标 | string | -- | otd-icon-a-addassignee | 1.0 |
| maxShowUser | 最小展示数量，超出隐藏 | Number | -- | 3 | 1.0 |
| size | 尺寸 | string | large, default, small | large | 1.0 |
| getList | 加载列表 | Function | -- | () => { label: string, value: string \| number }[] | 1.0 |
| remote | 是否为远程搜索 | Boolean | -- | false | 1.0 |
| remoteMethod | 远程搜索方法 | Function | -- | (query: string)=>{ label: string, value: string \| number }[] | 1.0 |

## 事件

| 参数   | 说明             | 类型     | 可选值 | 默认值                | 版本 |
| ------ | ---------------- | -------- | ------ | --------------------- | ---- |
| change | 删除或选中时触发 | Function | --     | (allData, item) => {} | 1.0  |
