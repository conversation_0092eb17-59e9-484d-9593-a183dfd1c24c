// 分组表格样式
@group-table: ~'@{namespace}-group-table';
.@{group-table} {
  position: relative;
  overflow: auto;
  border-radius: var(--otd-border-radius);
  :deep(.otd-table-collapse-panel) {
    .ant-collapse-expand-icon {
      // display: none;
    }
  }

  :deep(.ant-table-wrapper) {
    overflow: unset;
    .ant-table-thead > tr > th,
    .ant-table-thead > tr > td {
      background-color: var(--otd-basic-bg);
    }

    .ant-table-content {
      overflow: unset !important;
    }
  }
  :deep(.otd-table) {
    .ant-table-wrapper {
      .ant-table {
        .ant-table-selection-col {
          width: 44px;
        }
      }
    }
  }

  :deep(.otd-table-collapse) {
    min-width: 100%;
    width: min-content;

    .otd-table-collapse-panel {
      &.ant-collapse-item {
        &.ant-collapse-item-active {
          .ant-collapse-header {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
            &::after {
              content: '';
              display: block;
              position: absolute;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 1px;
              background-color: var(--otd-border-color);
            }
          }
          .ant-table-thead {
            display: table-header-group;
            position: sticky;
            z-index: 4;
          }
        }
        .ant-table-thead {
          display: none;
          width: 100%;
        }
      }
      .ant-collapse-header {
        padding: 0;
        position: sticky;
        top: 0;
        left: 0;
        z-index: 5;
        padding: 14px 16px;
        background-color: var(--otd-basic-bg);
        border-radius: var(--otd-border-radius);
        .ant-collapse-expand-icon {
          padding-inline-end: 6px;
        }
      }
      .ant-collapse-content {
        border-top: unset;
        background-color: var(--otd-basic-bg);
        position: relative;
        .ant-collapse-content-box {
          padding: 0;
        }
      }
    }
  }

  :deep(.otd-table) {
    .ant-table-cell:nth-of-type(2) {
      .otd-arrow {
        margin-left: -16px;
      }
    }
    &.ant-table-wrapper {
      .ant-table-thead {
        position: sticky;
        z-index: 3;
      }
      .ant-table-selection-column {
        border-width: 0 !important;
      }
    }
    .ant-table-body {
      overflow: unset !important;
      background-color: var(--otd-basic-bg);
    }
  }

  &__empty {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__footer {
    padding: 14px 16px 14px 44px;
    position: sticky;
    left: 0;
    width: 100%;
  }
}

.otd-table__collapse-panel {
  &__header {
    position: sticky;
    left: 16px;
    width: fit-content;
    user-select: none;
    .otd-arrow {
      margin-right: 6px;
    }
  }
  :deep(.ant-collapse-content) {
    display: none;
    transition: unset !important;
    &.ant-collapse-content-active {
      display: block;
    }
  }
}
