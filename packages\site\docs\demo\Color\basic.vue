<template>
  <OtdColorPicker :data="data" @save="handelSaveColor" />
</template>
<script lang="ts" setup>
  import { OtdColorPicker, randomColor,  } from '@otd/otd-ui';
  import { ICreateTagInput } from 'otd-ui/src/components/OtdTag/src/types';
  import { reactive } from 'vue';

  const defaultContent: ICreateTagInput = {
    tagName: '',
    tagAuth: 1,
    color: randomColor(),
  };
  // 搜索内容
  const data = reactive({ ...defaultContent });
  // 保存颜色
  function handelSaveColor(data) {
    console.log('保存的颜色', data);
  }
</script>
<style lang="less" scoped></style>
