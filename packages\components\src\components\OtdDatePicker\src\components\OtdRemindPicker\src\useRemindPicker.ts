import { getCurrentInstance, reactive, ref } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { useModal } from '/@/components/BasicModal';
import { judgeDateBefore } from '/@/tool';
import dayjs from 'dayjs';

export function getTipStringToData(value: string) {
  if (!value) return;
  const [count, time] = value.split('|');
  return [Number(count), time] as const;
}

const today = dayjs().startOf('day');
export function useRemindPicker() {
  const { props, attrs, emit } = getCurrentInstance()!;
  const { t } = useI18n();
  const [registerModal, { openModal, closeModal }] = useModal();
  const pickerOptions = reactive(
    ['0|18:00', '1|9:00', '2|9:00', '7|9:00'].map((value) => ({ label: getTipStringText(value), value })),
  );

  function getTipStringText(value: string) {
    const content = getTipStringToData(value);
    if (!content) return;
    const [count, time] = content;
    const unit = count === 1 ? t('common.repeatPicker.day') : t('common.repeatPicker.days');
    return count === 0
      ? t('common.RemindPicker.onTheDay', { time })
      : `${t('common.RemindPicker.beforeDueDay', { n: count, unit })} ${time}`;
  }

  const selectVisible = ref(false);
  function handleSelectCustom() {
    selectVisible.value = false;
    openModal(true, { value: attrs.value });
  }

  function handleConfirm(data) {
    const value = `${data.days}|${data.time}`;
    (attrs['onUpdate:value'] as any)(value);
    handleChange();
    pickerOptions[4] = {
      value,
      label: getTipStringText(value),
    };
    closeModal();
  }
  function handleSelect() {
    pickerOptions.splice(4, 1);
  }
  function handleChange(...arg) {
    const date = props.dates?.[1];
    if (!date) {
      const content = judgeDateBefore([props.dates![0], today]);
      props.dates![0] = content[0];
      props.dates![1] = content[1];
    }
    emit('change', ...arg);
  }

  return [
    registerModal,
    {
      pickerOptions,
      selectVisible,
      getTipStringText,
      handleSelectCustom,
      handleConfirm,
      handleSelect,
      handleChange,
    },
  ] as const;
}
