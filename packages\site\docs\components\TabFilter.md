# 标签页筛选

<p style="font-size:26px">代码演示</p>

## 基础

<demo src="../demo/TabFilter/basic.vue" title="基础"></demo>

## 基础标签页筛选

<demo src="../demo/TabFilter/basicTabFilter.vue" title="基础标签页筛选"></demo>

## 卡片标签页筛选

<demo src="../demo/TabFilter/cardTabFilter.vue" title="卡片标签页筛选"></demo>

## 属性

| 参数            | 说明                             | 类型            | 可选值 | 默认值  | 版本 |
| --------------- | -------------------------------- | --------------- | ------ | ------- | ---- |
| native          | 是否使用原生滚动条               | Boolean         |        | `false` | 1.0  |
| tabOptions      | tab 标签页数据                   | Array           |        |         | 1.0  |
| formSchemas     | 表单表格数据-[配置](#formschema) | Array           |        |         | 1.0  |
| formConfig      | [表单配置](#formconfig)          | Object          |        |         | 1.0  |
| filterActions   | filter 数据                      | Array           |        |         | 1.0  |
| filterPlacement | filter 方向                      | String, Boolean | right  | `false` | 1.0  |
| showFilterText  | 是否显示 filter 文字             | Boolean         |        | `false` | 1.0  |
| isCard          | 是否是卡片                       | Boolean         |        | `false` | 1.0  |
| hideFilter      | 隐藏筛选条件                     | Boolean         |        | `false` | 1.0  |
| bordered        | 表单内容是否显示边框             | Boolean         |        | `false` | 1.0  |

## 插槽

| 参数   | 说明              | 类型 | 版本 |
| ------ | ----------------- | ---- | ---- |
| add    | tab 右侧插槽      | Slot | 1.0  |
| filter | filter 自定义内容 | Slot | 1.0  |

## FormConfig

[参考 Vben Form 组件-FormConfig](http://doc.vvbin.cn/components/form.html#props)

## Formschema

[参考 Vben Form 组件-Formschema](http://doc.vvbin.cn/components/form.html#formschema)
