<template>
  <div ref="GroupsSroll" class="otd-group-table" :style="{ height: height }">
    <OtdCollapse class="otd-group-table__collapse" v-model:activeKey="activeKey" v-if="dataSource.length > 0">
      <OtdCollapsePanel
        v-for="(group, index) in dataSource"
        :key="groupKey ? group[groupKey] : index"
        :value="index"
        @expand="handleExpand"
      >
        <template #header>
          <slot name="group-header" :group="group">
            <span> {{ group.name }}</span>
          </slot>
        </template>
        <OtdBasicTable
          :data-source="group[groupColumnName]"
          :columns="columns"
          :group-index="index"
          :group-data="setGroupIndex(group, index)"
          v-bind="$attrs"
          :filter="filter"
          :expanded-row-keys="expandedRowKeys?.[index]"
          @update:expanded-row-keys="(value) => (expandedRowKeys&& (expandedRowKeys![index] = value))"
        >
          <template #expend-footer="{ record }" v-if="$slots['expend-footer']">
            <slot name="expend-footer" :group="group" :record="record" />
          </template>
        </OtdBasicTable>
        <div class="otd-group-table__footer" v-if="$slots['group-footer']">
          <slot name="group-footer" :group="group" />
        </div>
      </OtdCollapsePanel>
    </OtdCollapse>
    <div class="otd-group-table__empty" v-else-if="!loading">
      <Empty />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { Empty } from 'ant-design-vue';
  import { OtdCollapse, OtdCollapsePanel } from '/@/components/OtdCollapse';
  import { onMounted, PropType, provide, ref, unref, watchEffect, watch } from 'vue';
  import { Recordable } from '/#/global';
  import { OtdBasicTable } from '/@/components/OtdTable/src/OtdBasicTable';
  import { TableColumnPropsType } from '/@/components/OtdTable/src/OtdTable';
  import { ScrollIntersectionType, useScrollIntersection } from '/@/hooks/web/useIntersection';
  import { BasicTableFilterType } from '../../OtdBasicTable';

  const props = defineProps({
    dataSource: {
      type: Array as PropType<Recordable[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
    },
    columns: {
      type: Array as PropType<TableColumnPropsType[]>,
      default: () => [],
    },
    groupKey: {
      type: String,
    },
    groupColumnName: { type: String, default: 'groupData' },
    expandedRowKeys: {
      type: Array as PropType<Array<string[]>>,
    },
    filter: {
      type: Object as PropType<BasicTableFilterType>,
    },
    height: { type: String },
    width: { type: String },
  });
  const emit = defineEmits(['update:expandedRowKeys']);

  const activeKey = ref<(string | number)[]>([]);
  watchEffect(() => {
    activeKey.value = props.dataSource.map((_, index) => index);
  });

  const GroupsSroll = ref();

  let Observer;
  const observerMap: ScrollIntersectionType[] = [];
  onMounted(() => {
    Observer = useScrollIntersection(GroupsSroll.value);
  });

  provide('observer', (dom, index) => {
    const observer = Observer();
    unref(observerMap)[index] = observer;
    observer.observe(dom);
    return observer;
  });
  provide('resetObserverInit', resetObserverInit);

  function resetObserverInit(index = 0) {
    setTimeout(() => {
      unref(observerMap)
        .slice(index)
        .map((observer) => observer?.init());
    }, 100);
  }
  watch(
    () => props.dataSource,
    () => {
      resetObserverInit();
    },
  );
  function handleExpand() {
    resetObserverInit();
  }

  function setGroupIndex(group: Recordable, index: number) {
    group.__GroupIndex = index;
    return group;
  }
</script>
<style lang="less" scoped>
  .otd-group-table {
    overflow: auto;
    border-radius: var(--otd-border-radius);
    :deep(.otd-basic-table__header) {
      position: sticky;
      top: 50px;
      z-index: 3;
    }
    &__collapse {
      width: fit-content;
    }
    &__footer {
      padding: 14px 16px 14px 40px;
      position: sticky;
      left: 0;
      width: fit-content;
    }
  }
</style>
