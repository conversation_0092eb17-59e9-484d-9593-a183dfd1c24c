import { InjectionKey, Ref } from "vue";
import { useContext } from "/@/hooks/core/useContext";

export interface AppProviderContextProps {
  prefixCls: Ref<string>;
  isMobile: Ref<boolean>;
}

const key: InjectionKey<AppProviderContextProps> = Symbol();
export function useDesign(scope: string) {
  const values = useContext(key);
  return {
    prefixCls: `${values.prefixCls}-${scope}`,
    prefixVar: values.prefixCls,
  };
}
