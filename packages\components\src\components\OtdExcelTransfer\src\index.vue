<template>
  <OtdMoreAction :expand-number="1" :actions="topActions" action-type="icon" />
  <BasicModal width="1000px" :z-index="99" :title="modalTitle" @ok="handleOk" @register="registerModal" v-bind="$attrs">
    <div class="otd-excel-transfer-btn">
      <OtdImpExcel dateFormat="YYYY-MM-DD HH:mm" is-index v-bind="$attrs" @success="loadDataSuccess">
        <Button type="primary">{{ btnPlaceholder }}</Button>
      </OtdImpExcel>
      <Button v-if="downloadUrl" class="ml-12" @click="downloadTemplate">{{ t('component.excel.download') }}</Button>
    </div>
    <OtdTable class="mt-12" :data-source="tableData" @register="registerTable" />
  </BasicModal>
</template>

<script lang="tsx" setup>
  import { OtdImpExcel, ExcelData } from '/@/components/OtdExcelAction';
  import { OtdMoreAction } from '/@/components/OtdMoreAction';
  import { BasicModal, useModal } from '/@/components/BasicModal';
  import { OtdTable, TableColumnPropsType, useTable } from '/@/components/OtdTable';
  import { Button, message } from 'ant-design-vue';
  import { PropType, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { downloadByUrl } from '/@/tool';

  const props = defineProps({
    btnPlaceholder: {
      type: String,
      default: undefined,
    },
    modalTitle: {
      type: String,
      default: undefined,
    },
    columns: {
      type: Array as PropType<TableColumnPropsType[]>,
      default: () => [],
    },
    downloadUrl: {
      type: String,
      default: undefined,
    },
    // excel的模板文件列数组
    templateColumns: {
      type: Array,
      default: () => [],
    },
  });
  const emit = defineEmits(['submit']);
  const { t } = useI18n();
  const topActions = [
    {
      id: 'add',
      name: props.btnPlaceholder,
      icon: 'otd-icon-add-2',
      action: () => {
        tableData.value = [];
        openModal();
      },
    },
  ];
  const [registerModal, { openModal, closeModal }] = useModal();
  const tableData = ref<any>([]);
  const selectRows = ref<any[]>([]);
  const [registerTable, { setSelectedRowKeys }] = useTable({
    columns: props.columns,
    bordered: true,
    pagination: false,
    scroll: { y: 450 },
    rowKey: 'id',
    rowSelection: {
      checkStrictly: false,
      onSelect: (_, __, selectedRows) => {
        selectRows.value = selectedRows;
      },
      onSelectAll: (_, selectedRows) => {
        selectRows.value = selectedRows;
      },
    },
  });

  function loadDataSuccess(excelDataList: ExcelData[]) {
    let header = excelDataList[0].header;
    if (props?.templateColumns?.length) {
      const checkResult = props.templateColumns.every((value, index) => value === header[index])
      if (!checkResult) {
        message.error(t('component.excel.templateError'));
        return;
      }
    }
    if (!excelDataList[0].results.length) {
      message.error(t('component.excel.noDataTip'));
      return;
    }
    let data = excelDataList[0].results?.filter(obj => obj[0] !== null);
    tableData.value = data?.map((obj) => {
      const transformObj = {};
      props.columns?.forEach((column) => {
        const [index, key] = column.dataIndex as string[];
        if (column.contentHandler) {
          transformObj[key] = column.contentHandler({ excel: obj });
        } else {
          transformObj[key] = obj[index];
        }
      });
      return transformObj;
    });
    tableData.value.forEach((item, index) => (item.id = index));
    let selectedRowKeys = tableData.value.map(item => item.id);
    setSelectedRowKeys(selectedRowKeys);
    selectRows.value = [...tableData.value];
  }
  function handleOk() {
    if (!selectRows.value.length) {
      message.error(t('component.excel.selectData'));
      return;
    }
    emit('submit', selectRows.value);
    closeModal();
  }
  function downloadTemplate() {
    downloadByUrl({ url: props.downloadUrl!, fileName: '任务表格模板.xlsx' });
  }
</script>

<style lang="less" scoped>
  .otd-excel-transfer-btn {
    display: flex;
    justify-content: flex-end;
  }
  .mt-12 {
    margin-top: 12px;
  }
  .ml-12 {
    margin-left: 12px;
  }
</style>
