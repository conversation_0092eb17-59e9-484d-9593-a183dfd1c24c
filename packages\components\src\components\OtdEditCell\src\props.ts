import { formItemProps, formProps } from 'ant-design-vue/es/form';
import { PropType, Ref } from 'vue';

export const getFormItemProps = () => ({
  offset: {
    type: Array as unknown as PropType<[number, number]>,
    default: [-14, -16],
  },
  trigger: {
    type: String,
    default: 'click',
  },
  align: {
    type: String as PropType<'left' | 'right' | 'center'>, // 文字对齐方式
    default: 'left',
  },
  isFull: {
    type: Boolean,
    default: true,
  },
  leftLabel: {
    type: Boolean,
    default: false,
  },
  bordered: {
    type: Boolean,
    default: false,
  },
  formItem: {
    type: Boolean,
    default: false,
  },
  hideErrorTip: {
    type: Boolean,
    default: false,
  },
  ...formItemProps(),
});

export const getFormProps = () => ({
  loading: {
    type: [Boolean, Object] as PropType<boolean | Ref<boolean>>,
    default: false,
  },
  bordered: {
    type: Boolean,
    default: false,
  },
  ...formProps(),
});
