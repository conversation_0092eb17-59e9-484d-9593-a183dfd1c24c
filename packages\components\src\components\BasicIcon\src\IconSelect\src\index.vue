<template>
  <BasicSearchPopover :disabled="disabled" width="290px">
    <div class="otd-icon-select__trigger" :class="{ 'otd-icon-select-disabled': disabled }">
      <OtdIconPark :type="modelValue" :key="modelValue" theme="filled" v-if="modelValue" />
      <div class="placehoder-hover placeholder-text" v-else>
        {{ placeholder ?? t('common.chooseText') }}
      </div>
      <DownOutlined class="placeholder-text" :style="{ fontSize: '12px' }" />
    </div>
    <template #content="{ keyword, closePopover }">
      <OtdIconGrid
        :value="modelValue"
        :height="200"
        :keyword="keyword"
        @icon-click="(key) => handleIconClick(key, closePopover)"
      />
    </template>
  </BasicSearchPopover>
</template>
<script lang="ts" setup>
  import { computed, PropType } from 'vue';
  import OtdIconPark from './IconPark.vue';
  import OtdIconGrid from './IconGrid.vue';
  import { BasicSearchPopover } from '/@/components/BasicSearchPopover';
  import { DownOutlined } from '@ant-design/icons-vue';
  import { IconsType } from '../../type';
  import { useI18n } from '/@/hooks/web/useI18n';

  const props = defineProps({
    value: {
      type: String,
    },
    defaultValue: {
      type: String as PropType<IconsType>,
    },
    placeholder: {
      type: String,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['update:value', 'change']);
  const { t } = useI18n();

  const modelValue = computed({
    get: () => {
      if (props.defaultValue && !props.value) {
        emit('update:value', props.defaultValue);
      }
      return props.value;
    },
    set: (val) => emit('update:value', val),
  });
  function handleIconClick(key, close) {
    modelValue.value = key;
    emit('change', key);
    close();
  }
</script>
<style lang="less" scoped>
  .otd-icon-select {
    &__trigger {
      width: fit-content;
      font-size: 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      column-gap: 10px;
      padding: 6px 8px;
      line-height: 1;
      border-radius: var(--otd-default-radius);
      border: 1px solid var(--otd-border-gray);
      .placehoder-hover {
        font-size: 14px;
        line-height: 20px;
      }
      &:hover {
        background-color: var(--otd-gray3-hover);
      }
    }
    &-disabled {
      background-color: inherit !important;
      cursor: not-allowed;
    }
  }
</style>
