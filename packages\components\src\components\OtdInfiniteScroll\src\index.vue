<template>
  <div ref="container" class="otd-infinite-scroll">
    <slot :items="value" :loading="loading" :noMoreData="noMoreData" v-if="!isEmpty"></slot>
    <Empty class="otd-infinite-scroll__empty otd-box-center" v-else />
    <template v-if="!getHideLoad">
      <div v-if="loading" class="otd-infinite-scroll__loading otd-box-center">
        <OtdSpin />
        <span>{{ getLoadingText }}</span>
      </div>
      <div v-if="error" class="otd-infinite-scroll__error" @click="retry">{{ getErrorText }}</div>
      <div v-if="loadNoMore" class="otd-infinite-scroll__no-more-data">{{ getNoMoreDataText }}</div>
    </template>
  </div>
</template>

<script lang="ts" setup>
  import type { PropType } from 'vue';
  import type { Recordable } from '/#/global';
  import { ref, onMounted, onUnmounted, computed, unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { OtdSpin } from '/@/components/OtdLoading';
  import { Empty } from 'ant-design-vue';

  const props = defineProps({
    value: {
      type: Array as PropType<Recordable[]>,
      required: true,
    },
    loadMore: {
      type: Function as PropType<(data) => Promise<{ items?: Recordable[]; totalCount?: number }>>,
    },
    loadingText: {
      type: String,
    },
    errorText: {
      type: String,
    },
    noMoreDataText: {
      type: String,
    },
    hideLoad: {
      type: Function as PropType<(data) => Boolean>,
    },
    immediateLoad: {
      type: Boolean,
      default: true,
    },
    customNoData: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:value']);
  const { t } = useI18n();
  const getLoadingText = computed(() => props.loadingText || t('common.loadingText'));
  const getErrorText = computed(() => props.errorText || t('common.loadErrorText'));
  const getNoMoreDataText = computed(() => props.noMoreDataText || t('common.noMoreDataText'));
  const getHideLoad = computed(
    () => props.hideLoad?.({ loading: unref(loading), noMoreData: unref(noMoreData) }) ?? false,
  );
  const isEmpty = computed(() => (props.customNoData ? false : unref(props.value).length <= 0 && unref(noMoreData)));
  const loadNoMore = computed(() =>
    props.customNoData ? unref(noMoreData) : unref(props.value).length > 0 && unref(noMoreData),
  );

  const loading = ref(false);
  const error = ref(false);
  const noMoreData = ref(false);
  const container = ref<HTMLDivElement>();
  const page = ref(0);

  const fetchData = async () => {
    if (loading.value || noMoreData.value || !props.loadMore) return;
    loading.value = true;
    error.value = false;
    try {
      page.value += 1;
      const { items, totalCount } = await props.loadMore!({ page: unref(page) });
      const list = [...props.value, ...(items ?? [])];
      if (list.length >= (totalCount ?? 0)) {
        noMoreData.value = true;
        handler.remove();
      }
      emit('update:value', list);
    } catch (e) {
      error.value = true;
      throw new Error(e as string);
    } finally {
      loading.value = false;
    }
  };

  const onScroll = () => {
    const { scrollTop, clientHeight, scrollHeight } = container.value!;
    // 触底距离
    if (scrollTop + clientHeight >= scrollHeight - 100) {
      fetchData();
    }
  };

  const handler = {
    on() {
      if (container.value && !this.isWatch && props.loadMore) {
        this.isWatch = true;
        container.value.addEventListener('scroll', onScroll);
      }
    },
    remove() {
      this.isWatch = false;
      if (container.value) {
        container.value.removeEventListener('scroll', onScroll);
      }
    },
    isWatch: false,
  };

  const retry = () => {
    if (!loading.value) fetchData();
  };

  const refresh = () => {
    page.value = 0;
    emit('update:value', []);
    noMoreData.value = false;
    error.value = false;
    loading.value = false;
    handler.on();
    fetchData();
  };

  onMounted(() => {
    if (props.immediateLoad) {
      fetchData();
    }
    handler.on();
  });

  onUnmounted(() => {
    handler.remove();
  });

  defineExpose({
    refresh,
  });
</script>

<style lang="less" scoped>
  .otd-infinite-scroll {
    height: 100%;
    overflow-y: auto;
    position: relative;
    display: flex;
    flex-direction: column;
    &__loading,
    &__error,
    &__no-more-data {
      padding: 12px;
      cursor: pointer;
      font-size: 13px;
      color: var(--otd-gray3-color);
      column-gap: 4px;
      text-align: center;
    }
    &__refresh-button {
      position: absolute;
      top: 10px;
      right: 10px;
    }
    &__empty {
      height: 100%;
      flex-direction: column;
    }
  }
</style>
