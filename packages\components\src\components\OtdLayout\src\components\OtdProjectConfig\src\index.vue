<template>
  <div class="otd-project-config">
    <MenuItem @click.stop="showSetting">
      <template #icon>
        <i class="otdIconfont otd-icon-uil_setting"></i>
      </template>
      <span>{{ t('layout.config.setting') }}</span>
    </MenuItem>
    <Drawer v-model:open="visible" :title="t('layout.config.drawerTitle')" :width="320" placement="right">
      <div class="otd-project-config__content">
        <Divider>{{ t('layout.config.darkMode') }}</Divider>
        <OtdDarkModeToggle />
        <Divider>{{ t('layout.config.interfaceSettings') }}</Divider>
        <RenderContent />
      </div>
    </Drawer>
  </div>
</template>
<script lang="tsx" setup>
  import { Drawer, Divider, MenuItem } from 'ant-design-vue';
  import { ref, unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { OtdDarkModeToggle } from '../../OtdDarkModeToggle';
  import SwitchItem from './components/SwitchItem.vue';
  import { HandlerEnum } from './enum';
  import { useRootSetting } from '/@/storage/projectConfigStorage';

  const { t } = useI18n();

  const visible = ref(false);

  const { getGrayMode, getColorWeak } = useRootSetting();
  const RenderContent = () => {
    return (
      <>
        {/* 灰色模式 */}
        <SwitchItem title={t('layout.config.grayMode')} event={HandlerEnum.GRAY_MODE} def={unref(getGrayMode)} />
        {/* 色弱模式 */}
        <SwitchItem title={t('layout.config.colorWeak')} event={HandlerEnum.COLOR_WEAK} def={unref(getColorWeak)} />
      </>
    );
  };

  function showSetting() {
    visible.value = true;
  }
</script>
<style lang="less" scoped>
  .otd-project-config {
    // &__picker {
    //   display: flex;
    //   column-gap: 8px;
    //   align-items: center;
    // }
    &__content {
      text-align: center;
    }
  }
</style>
