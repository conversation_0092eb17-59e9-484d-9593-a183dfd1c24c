import type { TableColumnPropsType, TableInstancePropsType } from '../types';
import type { TableSummaryPropsType, TableSummaryType } from '../types';
import { computed, ComputedRef, onMounted, Ref, ref, unref, VNode, watch } from 'vue';
import { INDEX_COLUMN_FLAG, ACTION_COLUMN_FLAG, PAGE_SIZE } from '../const';
import { FilterActionType } from '/@/components/OtdTabFilter';
import { useI18n } from '/@/hooks/web/useI18n';
import { CustomRender } from '/@/utils/domUtils';
import { isBoolean } from '/@/utils/is';
import ColumnSetting from '../components/ColumnSetting.vue';

export function getColumnKey(item) {
  return item.flag ?? (item.dataIndex as string) ?? item.key;
}

export function useOtdTable(refProps: ComputedRef<TableInstancePropsType>, { getPaginationInfo, reload }) {
  const { t } = useI18n();

  // 表格设置操作
  const TableSetting: FilterActionType[] = [
    {
      id: 'column-setting',
      title: t('common.table.settingColumn'),
      render: () => {
        return <ColumnSetting columnOptions={unref(tableColumns)} onChangeColumn={handleChangeColumn} />;
      },
    },
    {
      id: 'refresh-table',
      title: t('common.redo'),
      icon: 'otd-icon-refresh',
      action: () => {
        reload();
      },
    },
  ];
  // 表格列表
  const tableColumns = computed(() => {
    const columns = [...(unref(refProps).columns ?? [])];
    handleIndexColumn(columns);
    handleActionColumn(columns);
    return columns;
  });

  const tableShowColumns: Ref<TableColumnPropsType[]> = ref([]);
  onMounted(() => {
    tableShowColumns.value = unref(tableColumns).filter((item) => !item.defaultHidden);
  });
  watch(
    () => unref(tableColumns),
    () => {
      tableShowColumns.value = unref(tableColumns);
    },
    { deep: true, immediate: true },
  );
  // 选择表格列
  function handleChangeColumn(data) {
    tableShowColumns.value = data;
  }

  // 处理序号列
  function handleIndexColumn(columns) {
    if (unref(refProps).showIndexColumn) {
      const isFixedLeft = columns.some((item) => item.fixed === 'left');
      columns.unshift({
        width: 75,
        title: t('common.table.index'),
        align: 'center',
        customRender: ({ index }) => {
          const getPagination = unref(getPaginationInfo);
          let content = '';
          if (isBoolean(getPagination)) {
            content = `${index + 1}`;
          } else {
            const { current = 1, pageSize = PAGE_SIZE } = getPagination;
            content = ((current < 1 ? 1 : current) - 1) * pageSize + index + 1;
          }
          return <span>{content}</span>;
        },
        flag: INDEX_COLUMN_FLAG,
        ...(isFixedLeft
          ? {
              fixed: 'left',
            }
          : {}),
        ...unref(refProps).indexColumnProps,
      });
    }
  }

  // 处理操作列
  function handleActionColumn(columns) {
    const { actionColumn } = unref(refProps);
    if (!actionColumn) return;
    columns.push({
      fixed: 'right',
      ...actionColumn,
      flag: ACTION_COLUMN_FLAG,
    });
  }

  // 总结栏数据
  const summaryData = computed(() => {
    if (!unref(refProps).summary) {
      return [];
    }
    return (unref(refProps).summary as TableSummaryType[]).map((summary) => {
      const result: any[] = [];
      let index = 0;
      unref(tableShowColumns).map((column, i) => {
        const data = summary[column.dataIndex as string];
        if (index > i) {
          result[i] = undefined;
        } else if (!data) {
          result[i] = null;
        } else {
          if (data instanceof Object) {
            result[i] = data as TableSummaryPropsType;
            index = i + (result[i]?.colSpan ?? 1);
          } else {
            result[i] = data;
          }
        }
      });
      return result;
    });
  });

  // 自定义总结栏组件
  const CustomSummaryRender = ({
    summary,
    column,
  }: {
    summary: string | VNode | TableSummaryPropsType;
    column: TableColumnPropsType;
  }) => {
    let component: string | VNode;
    if (typeof summary === 'object') {
      component = (summary as TableSummaryPropsType).content;
    } else {
      component = summary;
    }
    return (
      <div style={{ textAlign: column.align ?? 'left' }}>
        <CustomRender component={component} data={unref(refProps).dataSource} column={column} />
      </div>
    );
  };

  return {
    TableSetting,
    tableShowColumns,
    summaryData,
    CustomSummaryRender,
  };
}
