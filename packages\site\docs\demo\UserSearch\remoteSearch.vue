<template>
  <OtdUserSearch
    isIcon
    mode="multiple"
    :getList="GetList"
    :remoteMethod="RemoteMethod"
    @change="handleChage"
    :immediate="false"
    remote
    isSimple
  />
</template>
<script lang="ts" setup>
  import { OtdUserSearch } from '@otd/otd-ui';

  function GetList() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([]);
      }, 300);
    });
  }

  function RemoteMethod(data) {
    const { keyword } = data;
    return new Promise((resolve, reject) => {
      if (keyword) {
        fetch(`https://api.github.com/search/users?q=${keyword}`, {
          method: 'GET',
        })
          .then((res) => {
            if (!res.ok) {
              throw new Error(`请求出错: ${res.status}`);
            }
            return res.json();
          })
          .then((data) => {
            const items = data.items.map((item) => {
              return {
                name: item.login,
                id: item.id,
                extraProperties: {
                  Avatar: item.avatar_url,
                },
                ...item,
              };
            });
            const result = items.map((item) => ({
              label: item.name,
              value: item.id,
              img: item?.extraProperties?.Avatar,
              email: item.email,
            }));
            resolve(result);
          })
          .catch((error) => {
            reject(error);
          });
      } else {
        resolve([]);
      }
    });
  }
  function handleChage(data) {
    console.log(data);
  }
</script>
<style lang="less" scoped></style>
