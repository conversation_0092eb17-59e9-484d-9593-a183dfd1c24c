import type { DataNode } from 'ant-design-vue/es/tree';
import type { ComponentInternalInstance } from 'vue';
import type { FolderPropsType } from '../../../type';
import { getCurrentInstance, ref } from 'vue';
import { useModalInner } from '/@/components/BasicModal';
import { useFolder } from '../../../useFolder';
import { useMessage } from '/@/hooks/web/useMessage';
import { useI18n } from '/@/hooks/web/useI18n';
import { message } from 'ant-design-vue';

type FolderType = DataNode & {
  parentNode?: DataNode;
};

export function useMoveFolder() {
  const { t } = useI18n();
  const { props, emit } = getCurrentInstance() as ComponentInternalInstance & { props: FolderPropsType };
  const [registerModal, { changeLoading, closeModal }] = useModalInner(({ data }) => {
    parentFolder.value = data;
  });
  const { createConfirm } = useMessage();

  // 文件夹
  const { treeData, onLoadData, moveFolder, handleRefrush } = useFolder();

  const selectedKeys = ref([]);
  const expandedKeys = ref([]);
  const parentFolder = ref<FolderType | null>(null);
  const chooseFolder = ref<FolderType | null>(null);

  // 选择文件夹
  function handleSelect(node) {
    if (props.folderClick || node.isLeaf) {
      chooseFolder.value = node;
    } else {
      chooseFolder.value = null;
    }
  }

  // 取消事件
  function handleCancel() {
    chooseFolder.value = null;
    parentFolder.value = null;
    selectedKeys.value = [];
    expandedKeys.value = [];
    return Promise.resolve(false);
  }

  // 确认事件
  function handleOk() {
    createConfirm({
      iconType: 'warning',
      title: t('common.tip'),
      content: `${t('common.folder.askMoveTo')}${chooseFolder.value?.name}`,
      onOk: async () => {
        try {
          changeLoading(true);
          await moveFolder(
            parentFolder.value?.id,
            chooseFolder.value?.id === '-1' ? undefined : chooseFolder.value?.id,
          );
          message.success(t('common.operationSuccess'));
          emit('reload', chooseFolder.value);
          closeModal();
          changeLoading(false);
        } catch (error) {
          changeLoading(false);
        }
      },
    });
  }

  return {
    registerModal,
    treeData,
    onLoadData,
    handleRefrush,
    selectedKeys,
    expandedKeys,
    parentFolder,
    chooseFolder,
    handleSelect,
    handleCancel,
    handleOk,
  };
}
