import type { UploadFile } from 'ant-design-vue';
import { getProps, getEmits } from './props';
import { ExtractPropTypes } from 'vue';
import { UploadProgressEvent, UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';
import { Dayjs } from 'dayjs';
import { EmitType } from '/#/global';

export type UploadFilePropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
export type UploadFileEmitType = EmitType<ReturnType<typeof getEmits>[number]>;

export interface OtdUploadRequestOption extends UploadRequestOption {
  onProgress?: (event: UploadProgressEvent) => void;
  onCancel?: () => void;
}

export interface UploadFileType extends UploadFile {
  creationTime?: Date | Dayjs | string;
  creatorName?: string;
}
