export const ColorOptions = [
  '#FF8989',
  '#FF773C',
  '#FFA15C',
  '#FF8F5E',
  '#FFD80E',
  '#F1E80D',
  '#B1F421',
  '#50F36A',
  '#6BE631',
  '#00CD2D',
  '#1FE2CA',
  '#00C394',
  '#00C5D2',
  '#5CA7FF',
  '#89B8FF',
  '#6BA1DF',
  '#3E8CD4',
  '#97A2D9',
  '#817FE7',
  '#F89AE4',
  '#F48FF0',
  '#DDDACC',
  '#B4BAC0',
  '#9DB4CA',
  '#CBAFCF',
  '#ADCC9E',
  '#CEB48E',
  '#d17391',
  '#d09beb',
  '#26de9d',
  '#db9797',
];

// 生成随机数
function generateRandomNumber(max) {
  return Math.floor(Math.random() * (max + 1));
}

// 生成随机颜色
export function randomColor() {
  return ColorOptions[generateRandomNumber(ColorOptions.length)];
}
