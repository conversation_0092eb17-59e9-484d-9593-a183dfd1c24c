import { ConfigEnv, UserConfig, defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { configSvgIconsPlugin } from './vite/plugins/svgSprite';
import vueDts from 'vite-plugin-dts';

import { resolve } from 'path';
import replaceImportMetaEnv from './vite/plugins/vite-plugin-replace-env';

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

// https://vitejs.dev/config/
export default defineConfig(({ command }: ConfigEnv): UserConfig => {
  const isBuild = command === 'build';
  return {
    resolve: {
      alias: [
        // /@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
      ],
    },
    build: {
      emptyOutDir: true,
      target: 'modules',
      lib: {
        entry: {
          index: resolve(__dirname, './src/index.ts'),
          tool: resolve(__dirname, './src/tool.ts'),
        },
        name: 'otd-ui',
        formats: ['es'],
      },
      minify: false,
      outDir: '../../dist',
      rollupOptions: {
        // 确保外部化处理那些你不想打包进库的依赖
        external: [
          'vue',
          'vue-i18n',
          'dayjs',
          'lodash-es',
          'jszip',
          'qrcode',
          'sortablejs',
          'vue3-colorpicker',
          'resize-observer-polyfill',
          // 'ant-design-vue',
          /^ant\-design\-vue(\/.*)?/,
          'tinymce',
          /^tinymce\/.*/,
          /^@npkg\/tinymce\-plugins\/.*/,
          '@vueuse/core',
          '@ant-design/icons-vue',
          /crypto\-js(\/.*)?/,
          /cropperjs(\/.*)?/,
          '@vue/shared',
          '@vue/runtime-core',
          'xlsx',
          'echarts',
          'vuedraggable',
          'vue-router',
        ],
        output: {
          entryFileNames: 'src/[name].js',
          chunkFileNames: '[name].js',
          assetFileNames: '[name].[ext]',
          manualChunks: (id) => {
            if (id.includes('/packages/components/src')) {
              const name = id.toString().split('/packages/components/')[1];
              const file = name.split('?')[0];
              return file.replace(/\.(vue|ts|tsx)/, '');
            }
            if (id.includes('plugin-vue:export-helper')) {
              return 'src/export-helper';
            }
          },
        },
      },
      assetsDir: '',
    },
    plugins: [
      vue(),
      vueJsx(),
      vueDts({
        // entryRoot: 'index.d.ts',
        insertTypesEntry: true,
        copyDtsFiles: true,
        cleanVueFileName: true,
        include: ['./types', './src'],
        exclude: ['./src/locales/lang'],
      }),
      configSvgIconsPlugin(isBuild),
      replaceImportMetaEnv(),
    ],
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            namespace: 'otd',
          },
          javascriptEnabled: true,
        },
      },
    },
    server: {
      port: 9000,
    },
  };
});
