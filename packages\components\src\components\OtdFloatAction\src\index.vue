<template>
  <div class="otd-float-action ant-back-top">
    <Badge
      :numberStyle="{
        height: '16px',
        lineHeight: '14px',
        padding: '0 4px',
      }"
      v-for="(item, index) in showAction"
      :key="item.id"
      :count="item.badge ? item.badge() : 0"
    >
      <Tooltip placement="top" :title="item.name">
        <div
          class="otd-float-action__item ant-back-top-content"
          :style="{ '--color': item.color }"
          :data-float-action-id="item.id"
          @click="handleAction(item)"
        >
          <span class="otd-float-action__item-text">
            <i class="otdIconfont" :class="[item.icon]" :style="{ fontSize: item.iconSize ?? undefined }"></i>
            <span v-if="index === showAction.length - 1">{{ item.name }}</span>
          </span>
        </div>
      </Tooltip>
    </Badge>
    <Dropdown
      :align="DropdownAlign"
      overlayClassName="otd-dropdown"
      placement="topRight"
      trigger="click"
      :disabled="hideAction.length <= 0"
      v-if="actions.length > 0"
    >
      <div class="otd-float-action__more">
        <BasicIcon icon="task-more|svg" />
      </div>
      <template #overlay>
        <Menu>
          <MenuItem v-for="item in hideAction" :key="item.id" @click="handleAction(item)">
            <div class="menu-item">
              <span class="menu-item__content">
                <i class="otdIconfont" :class="[item.icon]" :style="{ fontSize: item.iconSize, color: item.color }" />
                <span>{{ item.name }}</span>
              </span>
              <Tooltip :title="t('common.pin')" placement="top">
                <span class="menu-item__pin" @click.stop="handlePin(item)">
                  <i class="otdIconfont otd-icon-relieve-full" v-if="item.pin"></i>
                  <i class="otdIconfont otd-icon-relieve" v-else> </i>
                </span>
              </Tooltip>
            </div>
          </MenuItem>
        </Menu>
      </template>
    </Dropdown>
  </div>
</template>
<script lang="ts" setup>
  import type { Align } from 'ant-design-vue/es/dropdown/props';
  import type { MoreActionItem } from '/@/components/OtdMoreAction';
  import { Badge, Tooltip, Dropdown, Menu, MenuItem } from 'ant-design-vue';
  import { BasicIcon } from '/@/components/BasicIcon';
  import { PropType, computed, unref, watchEffect } from 'vue';
  import { createLocalStorage } from '/@/utils/cache';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { Pin_Action } from '/@/setting';

  const props = defineProps({
    actions: {
      type: Array as PropType<MoreActionItem[]>,
      default: () => [],
    },
  });
  const { t } = useI18n();

  const DropdownAlign: Align = { offset: [0, -10] };

  const ls = createLocalStorage();

  // 隐藏操作
  const hideAction = computed(() => {
    return props.actions.slice(1);
  });

  // 固定操作
  const pinAction = computed(() => {
    return props.actions.filter((item) => item.pin);
  });

  // 显示操作
  const showAction = computed(() => {
    return [props.actions[0]].concat(pinAction.value).reverse();
  });

  watchEffect(() => {
    handleShowPin(props.actions);
  });

  // 处理展示固定内容
  function handleShowPin(actions) {
    const data = ls.get(Pin_Action);
    actions.map((item) => {
      item.pin = item.pin || data?.includes(item.id);
    });
  }

  // 固定事件
  function handlePin(item) {
    item.pin = !item.pin;
    ls.set(
      Pin_Action,
      unref(pinAction).map((item) => item.id),
    );
  }

  // 操作
  function handleAction(item) {
    item.action.call(item);
  }
</script>
<style lang="less" scoped>
  .otd-float-action {
    position: fixed;
    right: 30px;
    bottom: 24px;
    display: flex;
    align-items: center;
    gap: 10px;
    height: 16px;
    z-index: 20;
    overflow: unset;
    width: auto;
    &__item,
    &__more {
      width: auto;
      height: auto;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--otd-default-radius);
      line-height: 1;
      padding: 8px;
      color: var(--otd-white-text) !important;
      cursor: pointer;
      overflow: inherit;
      &::before {
        content: '';
        transition: transform 0.3s;
        position: absolute;
        width: 100%;
        height: 100%;
        box-shadow: var(--otd-box-shadow);
        border-radius: var(--otd-default-radius);
      }
      &:hover {
        &::before {
          transform: scale(1.1);
        }
      }
      .otd-svg-icon {
        position: relative;
        z-index: 2;
      }
      > img {
        width: 16px;
        height: 16px;
        z-index: 1;
      }
    }
    &__item {
      font-size: 14px;
      --color: var(--otd-primary-main);
      &-text {
        z-index: 1;
        > span {
          margin-left: 4px;
        }
      }
      &::before {
        background-color: var(--color);
      }

      &:hover {
        &::after {
          filter: brightness(0.95);
        }
      }
    }

    &__more {
      text-align: center;
      &::before {
        background-color: var(--otd-basic-bg);
      }
    }
  }

  .menu-item {
    display: flex;
    justify-content: space-between;
    min-width: 150px;
    &__content {
      display: flex;
      align-items: center;
      .otdIconfont {
        width: 16px;
        margin-right: 4px;
      }
    }
    &__pin {
      color: var(--otd-primary-text);
      padding: 4px;
      cursor: pointer;
      line-height: 1;
      border-radius: var(--otd-default-radius);
      &:hover {
        background-color: var(--otd-basic-active);
      }
    }
  }
</style>
