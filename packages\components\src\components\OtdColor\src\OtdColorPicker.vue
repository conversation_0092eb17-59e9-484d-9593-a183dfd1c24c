<template>
  <Popover
    overlay-class-name="otd-popover action-popover"
    trigger="click"
    placement="right"
    :align="popoverAlign"
    destroy-tooltip-onHide
    v-model:open="popoverVisible"
  >
    <template #content>
      <div class="max-w-280px">
        <OtdColor :data="data" @save="saveColor" />
      </div>
    </template>
    <slot>
      <div class="otd-tags__create-icon" :style="{ '--color': data.color }"></div>
    </slot>
  </Popover>
</template>
<script lang="ts" setup>
  import { PropType } from 'vue';
  import OtdColor from './OtdColor.vue';
  import { ICreateTagInput } from '../../OtdTag/src/types';
  import { ref } from 'vue';
  import { Popover } from 'ant-design-vue';

  defineProps({
    data: {
      type: Object as PropType<ICreateTagInput>,
      default: () => ({}),
    },
  });
  const emit = defineEmits(['save']);
  const popoverAlign = { offset: [10] };
  const popoverVisible = ref(false);
  function saveColor(data) {
    if (data.isPicker) {
      popoverVisible.value = false;
      emit('save', data);
    }
  }
</script>
<style lang="less" scoped>
  .otd-tags__create-icon {
    display: flex;
    align-items: center;
    --color: lighten(#90949d, 20%);
    padding: 3px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    border: 1px dashed lighten(#90949d, 20%);
    cursor: pointer;
    &::before {
      content: '';
      display: block;
      background-color: var(--color);
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }
</style>
