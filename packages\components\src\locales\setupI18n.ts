import type { I18n, I18nOptions } from 'vue-i18n';
import { unref } from 'vue';
import { createI18n } from 'vue-i18n';
import { setHtmlPageLang, setLoadLocalePool } from './helper';
import { LocaleEnum } from '/@/enums/appEnum';
import { useLocale } from './useLocale';
import { i18n } from './setI18n';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/en';
import En from './lang/en';
import ZhCN from './lang/zh_CN';
const LangMap = { en: En, zh_CN: ZhCN };

function createI18nOptions(): I18nOptions {
  const { getLocale } = useLocale();
  const locale = unref(getLocale);
  const defaultLocal = LangMap[locale];
  const message = defaultLocal.message ?? {};

  setHtmlPageLang(locale);
  setLoadLocalePool((loadLocalePool) => {
    loadLocalePool.push(locale);
  });
  dayjs.locale(locale.toLocaleLowerCase().replace('_', '-'));

  return {
    legacy: false,
    locale,
    fallbackLocale: LocaleEnum.ZH_CN,
    messages: {
      [locale]: message,
    },
    availableLocales: [LocaleEnum.ZH_CN, LocaleEnum.EN_US],
    sync: true, //If you don’t want to inherit locale from global scope, you need to set sync of i18n component option to false.
    silentTranslationWarn: true, // true - warning off
    missingWarn: false,
    silentFallbackWarn: true,
  };
}

// setup i18n instance with glob
export function setupI18n() {
  const options = createI18nOptions();
  i18n.value = createI18n(options) as I18n;
}
