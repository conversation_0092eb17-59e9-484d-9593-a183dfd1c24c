<template>
  <p> <span>边框</span><Switch disabled v-model:checked="state.border" /> </p>
  <p> <span>禁用</span><Switch v-model:checked="state.disabled" /> </p>
  <OtdSignleTinymce
    :bordered="state.border"
    right-toolbar="| send"
    :plugins="['mention']"
    :disabled="state.disabled"
    :max-height="300"
    :get-mentions-request="getMentions"
  />
  <br />
  <OtdTextTinymce
    v-model:value="description"
    :description="description"
    :disabled="state.disabled"
    :bottom-margin="200"
    :max-height="400"
    @upload-attachment="handleTinyUploadAttachment"
  />
</template>
<script lang="ts" setup>
  import { OtdSignleTinymce, Switch, OtdTextTinymce } from '@otd/otd-ui';
  import { reactive, ref } from 'vue';

  const state = reactive({
    border: false,
    disabled: false,
  });

  const description = ref(`<p style='color:red;'>11111<p>`);

  function handleTinyUploadAttachment(_, __, onSuccess) {
    onSuccess('1111');
  }

  function getMentions(_, filter): Promise<Record<string, any>[]> {
    console.log(filter);

    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(
          [
            {
              tenantId: null,
              userName: '1154268',
              name: '朱成' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '3302b5f9d11a491fa83cb4b9bbbdd122',
              entityVersion: 3,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-09-26T16:00:17.748393',
              lastModifierId: null,
              creationTime: '2023-09-26T15:55:00.44181',
              creatorId: null,
              id: '1475ae74-6669-3a7b-d97c-3a0de2a8d17c',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '1022032',
              name: '陈永琴' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: 'c5070e7110b4487a8aa6dd14e483ea09',
              entityVersion: 10,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-12-15T16:59:03.323436',
              lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              creationTime: '2023-09-26T15:54:58.504459',
              creatorId: null,
              id: '245875ae-10f9-f520-cdca-3a0de2a8c9e8',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: 'admin',
              name: 'limin' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: '',
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '550d7f675fa24ee7af9c95ce15fee7e3',
              entityVersion: 230,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2024-05-15T13:19:31.998398',
              lastModifierId: null,
              creationTime: '2023-01-13T10:54:10.282165',
              creatorId: null,
              id: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: '3a0f074b-4dd5-3a27-c287-6949611e7fd9',
              },
            },
            {
              tenantId: null,
              userName: '<EMAIL>',
              name: 'adw2' + filter.tab,
              surname: null,
              email: '112@testcom',
              emailConfirmed: false,
              phoneNumber: '',
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '282ac1fe314449f386f2f3426a119294',
              entityVersion: 8,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2024-05-07T19:22:35.044619',
              lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              creationTime: '2023-10-23T13:33:30.672791',
              creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              id: '3a0e6d32-fa4d-f50d-bc46-7b4d9ad67446',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '204369',
              name: 'rikka' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: 'ae097271aa9d456498f27e1c7aaad6db',
              entityVersion: 3,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-12-06T12:57:28.9468',
              lastModifierId: null,
              creationTime: '2023-12-06T12:57:28.891637',
              creatorId: null,
              id: '3a0f4fa9-cddb-f885-2ecc-2b0b466686e0',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '201499',
              name: '汤卫国' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: 'aa0c199a5f04494bba92eb5454287f42',
              entityVersion: 4,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-09-26T16:00:15.041366',
              lastModifierId: null,
              creationTime: '2023-09-26T15:54:57.665696',
              creatorId: null,
              id: '3a538479-2b56-788d-9f48-3a0de2a8c697',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '200532',
              name: '范育菁' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: 'e643f9b4a3c64fdda48ca135837d97ac',
              entityVersion: 4,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-09-26T16:00:17.624882',
              lastModifierId: null,
              creationTime: '2023-09-26T15:55:00.177714',
              creatorId: null,
              id: '40960d80-cc85-fca6-ec6a-3a0de2a8d071',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '1118277',
              name: '张正花' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '9c67422b273e4117936c78f878b0fa78',
              entityVersion: 12,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2024-04-25T11:17:56.503369',
              lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              creationTime: '2023-09-26T15:54:59.622071',
              creatorId: null,
              id: '5873157c-943e-9b23-ed46-3a0de2a8ce36',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '1084632',
              name: '孙雪雯' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: 'd71ba7ac160043b4a662568a833e378b',
              entityVersion: 4,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-09-26T16:00:16.171813',
              lastModifierId: null,
              creationTime: '2023-09-26T15:54:58.776691',
              creatorId: null,
              id: '670775e0-0fee-f02c-da65-3a0de2a8caf9',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '201841',
              name: '徐青峰' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '7a661444f09b411892ab831b8417d413',
              entityVersion: 4,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-09-26T16:00:16.488659',
              lastModifierId: null,
              creationTime: '2023-09-26T15:54:59.058071',
              creatorId: null,
              id: '8878362d-b73e-ed17-8864-3a0de2a8cc10',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '1153348',
              name: '郝达芬' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '021172d2db4b41238f55257812fdc765',
              entityVersion: 3,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-09-26T16:00:16.614852',
              lastModifierId: null,
              creationTime: '2023-09-26T15:54:59.332985',
              creatorId: null,
              id: '956e0876-cc70-cf80-0f91-3a0de2a8cd25',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '201393',
              name: '智丹丹' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '7fbad22ffb804308aeeba62827355297',
              entityVersion: 8,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-09-26T16:00:18.016082',
              lastModifierId: null,
              creationTime: '2023-09-26T15:54:58.23814',
              creatorId: null,
              id: 'ca493acc-2948-a273-402e-3a0de2a8c8e0',
              extraProperties: {
                Avatar: 'https://picsum.photos/200/300',
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '201776',
              name: '胡洵洵' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '6b9f0bc0577c40be9d26b2edf1b729fb',
              entityVersion: 4,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-09-26T16:00:15.311446',
              lastModifierId: null,
              creationTime: '2023-09-26T15:54:57.960767',
              creatorId: null,
              id: 'de970639-704d-8de6-bf7f-3a0de2a8c7c9',
              extraProperties: {
                Avatar: null,
                AvatarFileId: null,
              },
            },
            {
              tenantId: null,
              userName: '202069',
              name: '韩立霞' + filter.tab,
              surname: null,
              email: '<EMAIL>',
              emailConfirmed: false,
              phoneNumber: null,
              phoneNumberConfirmed: false,
              isActive: true,
              lockoutEnabled: false,
              lockoutEnd: null,
              concurrencyStamp: '50d52168ee20416b9ed63ad17cbb02f5',
              entityVersion: 10,
              isDeleted: false,
              deleterId: null,
              deletionTime: null,
              lastModificationTime: '2023-10-12T10:05:20.250454',
              lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              creationTime: '2023-09-26T15:55:00.999535',
              creatorId: null,
              id: 'e17af8cd-f712-37e2-ffe9-3a0de2a8d3ab',
              extraProperties: {
                Avatar: null,
                AvatarFileId: null,
              },
            },
          ]
            .map((user) => ({
              label: user.name,
              value: user.id,
              img: user?.extraProperties?.Avatar,
            }))
            .filter((item) => item.label.toLowerCase().includes(filter.query.toLowerCase())),
        );
      }, 100);
    });
  }
</script>
<style lang="less" scoped>
  p {
    display: flex;
    align-items: center;
    > span {
      margin-right: 10px;
    }
  }
</style>
