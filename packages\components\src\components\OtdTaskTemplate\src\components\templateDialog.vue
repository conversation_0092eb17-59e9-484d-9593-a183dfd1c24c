<template>
  <Modal
    v-model:open="visible"
    :maskClosable="false"
    :centered="true"
    :title="title"
    :okButtonProps="{ disabled: !chooseTemplate }"
    :after-close="handleAfterClose"
    :confirmLoading="confirmLoading"
    width="1300px"
    v-bind="$attrs"
    @cancel="handleCancel"
    @ok="handleConfirm"
  >
    <div class="otd-taskTemplate-dialog">
      <OtdScrollbar class="otd-taskTemplate-dialog-scroll">
        <!-- 任务模板 -->
        <OtdFolderList
          :data="treeData"
          :folderRequest="folderRequest"
          :loadData="onLoadData"
          v-model:selected="selectedKeys"
          v-model:expanded="expandedKeys"
          hide-action
          :folder-click="false"
          @select="handleSelect"
        />
      </OtdScrollbar>
      <div class="subtask-content">
        <h1 class="font-bold">{{ t('common.taskList') }}</h1>
        <!-- 任务模板 -->
        <!-- :scroll="{ y: 400 }" -->
        <div class="subtask-content__table">
          <OtdTable
            ref="templateRef"
            :data-source="subtaskContent"
            :loading="loading"
            :row-selection="rowSelection"
            :padding="[12, 12]"
            size="small"
            default-show-select
            bordered
            canResize
            @dragEnd="dragEnd"
            @register="registerTable"
          />
        </div>
      </div>
    </div>
  </Modal>
</template>
<script lang="ts" setup>
  import { Modal } from 'ant-design-vue';
  import { OtdFolderList } from '/@/components/OtdFolderMenu/src/components/OtdFolderList';
  import { useSelectTemplate } from '../useSelectTemplate';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { useFolder } from '/@/components/OtdFolderMenu/src/useFolder';
  import { OtdTable, useTable } from '/@/components/OtdTable';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useTaskTable } from '../useTaskTable';
  import { getProps } from '../props';
  const { t } = useI18n();

  const props = defineProps({
    title: {
      type: String,
      default: 'Title',
    },
    folderApi: {
      type: Function,
    },
    resource: {
      type: Boolean,
      default: true,
    },
    notRoot: {
      type: Boolean,
      default: true,
    },
    ...getProps(),
  });

  const emit = defineEmits(['confirm', 'cancel', 'dragEnd']);

  const { treeData, onLoadData } = useFolder();
  const { tableColumns } = useTaskTable();
  const {
    visible,
    confirmLoading,
    selectedKeys,
    expandedKeys,
    chooseTemplate,
    loading,
    subtaskContent,
    handleSelect,
    handleAfterClose,
    handleCancel,
    handleConfirm,
    rowSelection,
  } = useSelectTemplate();

  const [registerTable] = useTable({
    columns: tableColumns,
    // maxHeight: 386,
    pagination: false,
    size: 'middle',
    // isTreeTable: true,
    draggable: props.draggable, //列表是否允许拖拽
    childrenColumnName: 'childTaskItems',
    isTreeTable: true,
    rowKey: 'id',
  });

  function dragEnd(val) {
    emit('dragEnd', val);
  }

  function openDialog() {
    visible.value = true;
    if (treeData.value === undefined) {
      onLoadData(undefined);
    }
  }

  defineExpose({
    openDialog,
  });
</script>
<style lang="less" scoped>
  .otd-taskTemplate-dialog {
    display: flex;
    height: 500px;
    &-scroll {
      padding: 10px;
      padding-bottom: 0;
      width: 240px;
    }
    .subtask-content {
      padding: 10px;
      overflow: hidden;
      border-left: 1px solid var(--otd-border-color);
      flex: 1;
      display: flex;
      flex-direction: column;
      &__table {
        flex: 1;
        overflow: hidden;
      }
      .subtask-content-item {
        line-height: 1;
        padding: 8px 10px;
      }
    }
  }
</style>
