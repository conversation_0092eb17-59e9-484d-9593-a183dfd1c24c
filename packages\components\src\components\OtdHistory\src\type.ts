import { DefaultOptionType } from 'ant-design-vue/es/select';
import { ExtractPropTypes, VNode } from 'vue';
import { getProps } from './props';

export type OtdHistoryFieldsType = {
  content: string;
  creationTime: string;
  creatorId: string;
  creatorName: string;
  type: string;
  prevValue: string;
  newValue: string;
  actionType: string;
};

export type HistoryOptionType = DefaultOptionType & {
  render?: (data) => VNode;
};

export type OtdHistoryPropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
