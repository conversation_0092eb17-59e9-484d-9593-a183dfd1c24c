<template>
  <div style="width: 800px">
    <Button @click="setData">reset</Button>
    <OtdTrendList :data="data" @click-item="handleClickItem">
      <template #title>1111</template>
    </OtdTrendList>
  </div>
</template>
<script lang="ts" setup>
  import { Button, OtdTrendList } from '@otd/otd-ui';
  import { ref } from 'vue';

  const data = ref<Record<string, any>[]>();

  function setData() {
    data.value = [
      {
        key: -1,
        label: '这是一个很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的任务',
        status: 30,
        value: Math.round(Math.random() * 100000),
      },
      ...new Array(10).fill(0).map((_, index) => ({
        key: index,
        label: `这是个任务${index}`,
        status: 20,
        value: Math.round(Math.random() * 100000),
      })),
    ];
  }

  setData();

  function handleClickItem(data) {
    console.log(data);
  }
</script>
<style lang="less" scoped></style>
