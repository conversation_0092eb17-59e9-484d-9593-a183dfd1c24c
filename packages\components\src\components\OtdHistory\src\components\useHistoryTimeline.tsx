import { Recordable } from '/#/global';
import { useI18n } from '/@/hooks/web/useI18n';
import { HistoryOptionType, OtdHistoryFieldsType, OtdHistoryPropsType } from '../type';
import { OtdStatusTrigger } from '/@/components/OtdStatus';
import { OtdPriorityTrigger } from '/@/components/OtdPriority';
import { useFileType } from '/@/components/OtdUploadFile';
import TagItem from '/@/components/OtdTag/src/components/TagItem.vue';
import { OtdUserTag } from '/@/components/OtdUserSearch';
import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
import { removeHtmlTag } from '/@/tool';
import { OtdEmoticonIcon } from '/@/components/OtdEmoticon';
import { computed, getCurrentInstance, unref, VNode } from 'vue';
import { DefaultFields } from '../props';
import { OtdTaskTypeTrigger } from '/@/components/OtdTaskType';
import { useTaskTypeDictionary } from '/@/hooks/dictionary';

export enum TaskLogTypeEnum {
  Create = 'Create',
  Status = 'Status',
  Delete = 'Delete',
  Assign = 'Assign',
  WorkSpend = 'WorkSpend',
  ChildTask = 'ChildTask',
  UpdateTitle = 'UpdateTitle',
  Schedule = 'Schedule',
  UpdateDescription = 'UpdateDescription',
  Priority = 'Priority',
  PreTask = 'PreTask',
  TaskProgress = 'TaskProgress',
  File = 'File',
  Document = 'Document',
  Collaboration = 'Collaboration',
  Tags = 'Tags',
  Responsible = 'Responsible',
  Checker = 'Checker',
  Follower = 'Follower',
  Creator = 'Creator',
  AutoCompletion = 'AutoCompletion',
  PlanStartDate = 'PlanStartDate',
  PlanDoneDate = 'PlanDoneDate',
  Comment = 'Comment',
  TaskType = 'TaskType',
}

export enum TaskLogActionEnum {
  Create = 'Create',
  Edit = 'Edit',
  Delete = 'Delete',
  // 交接
  HandOver = 'HandOver',
  //计划工时
  PlanTaskHourCreate = 'PlanTaskHourCreate',
  PlanTaskHourEditDate = 'PlanTaskHourEditDate',
  PlanTaskHourEditTime = 'PlanTaskHourEditTime',
  PlanTaskHourEditUser = 'PlanTaskHourEditUser',
  PlanTaskHourDelete = 'PlanTaskHourDelete',
  PlanTaskHourEditCategory = 'PlanTaskHourEditCategory',
  //实际工时
  RealTaskHourCreate = 'RealTaskHourCreate',
  RealTaskHourDelete = 'RealTaskHourDelete',
  RealTaskHourEditDate = 'RealTaskHourEditDate',
  RealTaskHourEditTime = 'RealTaskHourEditTime',
  RealTaskHourEditUser = 'RealTaskHourEditUser',
  RealTaskHourEditRemark = 'RealTaskHourEditRemark',
  RealTaskHourEditCategory = 'RealTaskHourEditCategory',
  //评论表情回复
  CommentEmojiReply = 'CommentEmojiReply',
}

function getTimeData(record: Recordable, fields: OtdHistoryFieldsType) {
  const { prevValue, newValue } = fields;
  try {
    if (typeof record.prevValue === 'string') record.prevValue = JSON.parse(record[prevValue]);
  } catch (error) {
    record.prevValue = null;
  }
  try {
    if (typeof record.newValue === 'string') record.newValue = JSON.parse(record[newValue]);
  } catch (error) {
    record.newValue = null;
  }
  return record;
}

function getArrow() {
  return <i className="otdIconfont otd-icon-jiantou"></i>;
}

function getUserContent({ record, fields, historyAction, option, getAvatar }) {
  let { prevValue, newValue } = getTimeData(record, fields);
  prevValue = prevValue && (Array.isArray(prevValue) ? prevValue : [prevValue]);
  newValue = newValue && (Array.isArray(newValue) ? newValue : [newValue]);
  return (
    <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
      {historyAction[record[fields.actionType]](option.label)}
      {prevValue?.map((item) => (
        <OtdUserTag user={{ label: item.Label, img: getAvatar(item.Value) }} />
      ))}
      {prevValue && newValue && getArrow()}
      {newValue?.map((item) => (
        <OtdUserTag user={{ label: item.Label, img: getAvatar(item.Value) }} />
      ))}
    </div>
  );
}

export function HistoryContent({ data, fields, historyOptions }): string | VNode {
  const { content, type } = fields;
  const option = historyOptions?.[data[type]];
  return option && (data[content] || option.render?.({ record: data, option }));
}

export function useHistoryTimeline() {
  const { t } = useI18n();
  const { props } = getCurrentInstance() as unknown as { props: OtdHistoryPropsType };
  const { fileTypeToIcon } = useFileType();
  const { getGlobalProvide } = useGlobalConfig();
  const { getAvatar, HistoryRecordHandler } = getGlobalProvide();
  const { getTaskTypeIcon, getTaskTypeName } = useTaskTypeDictionary();
  const getFields = computed(() => ({ ...DefaultFields, ...props.fields }));

  const fields = unref(getFields);
  const { actionType } = fields;

  const ActionHandler = {
    [TaskLogActionEnum.RealTaskHourEditRemark]: (data, record, option) => {
      const [date, user, remark] = data;
      return (
        <>
          {defaultHistoryAction[record[actionType]](option.label)}
          <span>{`${user?.Value} ( ${date?.Value} ) : ${remark?.Value}`}</span>
        </>
      );
    },
    [TaskLogActionEnum.CommentEmojiReply]: (data, _, option) => {
      const [content, , Emoji] = data;
      return (
        <>
          <OtdEmoticonIcon icon={Emoji.Value} size={22} style="margin:-4px 4px 0 0" />
          {option.label} :<span>{removeHtmlTag(content.Value)}</span>
        </>
      );
    },
  };
  // #region log-ActionType
  const defaultHistoryAction: Record<TaskLogActionEnum, HistoryOptionType> = {
    [TaskLogActionEnum.Create]: (content) => <span>{`${t('common.added')}${content} :`}&nbsp;</span>,
    [TaskLogActionEnum.Edit]: (content) => <span>{`${t('common.editedText')}${content} :`}&nbsp;</span>,
    [TaskLogActionEnum.Delete]: (content) => <span>{`${t('common.deledText')}${content} :`}&nbsp;</span>,
    [TaskLogActionEnum.HandOver]: (content) => <span>{`${t('common.handoverText')}${content} :`}&nbsp;</span>,
    // 创建计划工时
    [TaskLogActionEnum.PlanTaskHourCreate]: () => (
      <span>{`${t('common.added')}${t('common.trackTime.plannedTrackingTime')} :`}&nbsp;</span>
    ),
    // 删除计划工时
    [TaskLogActionEnum.PlanTaskHourDelete]: () => (
      <span>{`${t('common.deledText')}${t('common.trackTime.plannedTrackingTime')} :`}&nbsp;</span>
    ),
    // 修改计划工时-执行时间
    [TaskLogActionEnum.PlanTaskHourEditDate]: () => (
      <span>
        {`${t('common.editedText')}${t('common.trackTime.plannedTrackingTime')} ( ${t(
          'common.trackTime.ExecutionTime',
        )} ) :`}
        &nbsp;
      </span>
    ),
    // 修改计划工时-工时数
    [TaskLogActionEnum.PlanTaskHourEditTime]: () => (
      <span>
        {`${t('common.editedText')}${t('common.trackTime.plannedTrackingTime')} ( ${t(
          'common.trackTime.WorkHours',
        )} ) :`}
        &nbsp;
      </span>
    ),
    // 修改计划工时-工时执行者
    [TaskLogActionEnum.PlanTaskHourEditUser]: () => (
      <span>
        {`${t('common.editedText')}${t('common.trackTime.plannedTrackingTime')} ( ${t(
          'common.trackTime.WorkHourExecutor',
        )} ) :`}
        &nbsp;
      </span>
    ),
    // 修改计划工时-工时类别
    [TaskLogActionEnum.PlanTaskHourEditCategory]: () => (
      <span>
        {`${t('common.editedText')}${t('common.trackTime.plannedTrackingTime')} ( ${t(
          'common.trackTime.WorkHourCategory',
        )} ) :`}
        &nbsp;
      </span>
    ),
    // 创建实际工时
    [TaskLogActionEnum.RealTaskHourCreate]: () => (
      <span>{`${t('common.added')}${t('common.trackTime.actualTrackingTime')} :`}&nbsp;</span>
    ),
    // 删除实际工时
    [TaskLogActionEnum.RealTaskHourDelete]: () => (
      <span>{`${t('common.deledText')}${t('common.trackTime.actualTrackingTime')} :`}&nbsp;</span>
    ),
    // 修改实际工时-执行时间
    [TaskLogActionEnum.RealTaskHourEditDate]: () => (
      <span>
        {`${t('common.editedText')}${t('common.trackTime.actualTrackingTime')} ( ${t(
          'common.trackTime.ExecutionTime',
        )} ) :`}
        &nbsp;
      </span>
    ),
    // 修改实际工时-工时数
    [TaskLogActionEnum.RealTaskHourEditTime]: () => (
      <span>
        {`${t('common.editedText')}${t('common.trackTime.actualTrackingTime')} ( ${t(
          'common.trackTime.WorkHours',
        )} ) :`}
        &nbsp;
      </span>
    ),
    // 修改实际工时-工时执行者
    [TaskLogActionEnum.RealTaskHourEditUser]: () => (
      <span>
        {`${t('common.editedText')}${t('common.trackTime.actualTrackingTime')} ( ${t(
          'common.trackTime.WorkHourExecutor',
        )} ) :`}
        &nbsp;
      </span>
    ),
    // 修改实际工时-备注
    [TaskLogActionEnum.RealTaskHourEditRemark]: () => (
      <span>
        {`${t('common.editedText')}${t('common.trackTime.actualTrackingTime')} ( ${t(
          'common.trackTime.WorkProgress',
        )} ) :`}
        &nbsp;
      </span>
    ),

    // 修改实际工时-工时类别
    [TaskLogActionEnum.RealTaskHourEditCategory]: () => (
      <span>
        {`${t('common.editedText')}${t('common.trackTime.actualTrackingTime')} ( ${t(
          'common.trackTime.WorkHourCategory',
        )} ) :`}
        &nbsp;
      </span>
    ),
    [TaskLogActionEnum.CommentEmojiReply]: () => {},
  };
  // #endregion

  // #region log-type
  const DefaultHistoryOptions: Partial<Record<TaskLogTypeEnum, HistoryOptionType>> = {
    // 创建
    [TaskLogTypeEnum.Create]: {
      icon: 'otd-icon-chuangjianrenwu',
      label: t('common.history.createTask'),
      render: ({ record, option }) => {
        const { newValue } = getTimeData(record, fields);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            <span>{option.label} :</span>
            <span>{newValue.Label}</span>
          </div>
        );
      },
    },
    // 状态
    [TaskLogTypeEnum.Status]: {
      icon: 'otd-icon-xitongzhuangtai',
      label: t('common.status.status'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            <OtdStatusTrigger value={+prevValue.Value} is-text />
            {getArrow()}
            <OtdStatusTrigger value={+newValue.Value} is-text />
          </div>
        );
      },
    },
    // 任务类型
    [TaskLogTypeEnum.TaskType]: {
      icon: 'otd-icon-xitongzhuangtai',
      label: t('common.taskType.title'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        const [preTaskType, preCustomIcon, preCustomName] = prevValue;
        const [newTaskType, newCustomIcon, newCustomName] = newValue;

        const preData = {
          icon: preCustomIcon?.Value ?? getTaskTypeIcon(preTaskType.Value),
          label: preCustomName.Value ?? getTaskTypeName(preTaskType.Value),
        };
        const newData = {
          icon: newCustomIcon?.Value ?? getTaskTypeIcon(newTaskType.Value),
          label: newCustomName.Value ?? getTaskTypeName(newTaskType.Value),
        };

        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            <OtdTaskTypeTrigger value={preData} />
            {getArrow()}
            <OtdTaskTypeTrigger value={newData} />
          </div>
        );
      },
    },
    // 删除
    [TaskLogTypeEnum.Delete]: {
      icon: 'otd-icon-a-catdeletesize24',
      label: t('common.history.deleteTask'),
      render: ({ record, option }) => {
        const { prevValue } = getTimeData(record, fields);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            <span>{option.label} :</span>
            <span>{prevValue.Label}</span>
          </div>
        );
      },
    },
    // 分配人员
    // [TaskLogTypeEnum.Assign]: { icon: 'otd-icon-Ttubiao--', label: t('common.history.assignPeople') },
    // 工时
    [TaskLogTypeEnum.WorkSpend]: {
      icon: 'otd-icon-gongshitianbao',
      label: t('common.trackTime.trackTime'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        const [prevDate, prevHour, , prevUser, prevCategory] = prevValue ?? [];
        const [newDate, newHour, , newUser, newCategory] = newValue ?? [];
        const handler = ActionHandler[record[actionType]];
        const CustomHandler = HistoryRecordHandler?.[TaskLogTypeEnum.WorkSpend] ?? ((data) => data);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {handler
              ? handler(newValue, record, option)
              : CustomHandler(
                  <>
                    {defaultHistoryAction[record[actionType]](option.label)}
                    {prevValue &&
                      [
                        prevUser.Value,
                        prevCategory?.Value ? ` [ ${prevCategory.Value} ] ` : undefined,
                        ` ( ${prevDate.Value || t('common.trackTime.WorkHoursTotal')} ) `,
                        `${prevHour.Value} ${t('common.hours')}`,
                      ]
                        .filter(Boolean)
                        .join(' ')}
                    {prevValue && newValue && getArrow()}
                    {newValue &&
                      [
                        newUser.Value,
                        newCategory?.Value ? ` [ ${newCategory.Value} ] ` : undefined,
                        ` ( ${newDate.Value || t('common.trackTime.WorkHoursTotal')} ) `,
                        `${newHour.Value} ${t('common.hours')}`,
                      ].join(' ')}
                  </>,
                  record,
                )}
          </div>
        );
      },
    },
    // 子任务
    [TaskLogTypeEnum.ChildTask]: {
      icon: 'otd-icon-zirenwu',
      label: t('common.subtask.subtask'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        const label = prevValue?.Label || newValue?.Label;
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            {label}
          </div>
        );
      },
    },
    // 任务名称
    [TaskLogTypeEnum.UpdateTitle]: {
      icon: 'otd-icon-zhongmingming',
      label: t('common.subtask.taskName'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            <span>{prevValue.Value}</span>
            {getArrow()}
            <span>{newValue.Value}</span>
          </div>
        );
      },
    },
    // 截止时间
    // [TaskLogTypeEnum.Schedule]: { icon: 'otd-icon-riqi', label: t('common.history.endTime') },
    // 描述
    [TaskLogTypeEnum.UpdateDescription]: {
      icon: 'otd-icon-miaoshu',
      label: t('common.history.taskDescription'),
      render: ({ record, option }) => {
        const content = defaultHistoryAction[record[actionType]](option.label);
        content.children[0] = content.children[0].slice(0, -1);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {content}
          </div>
        );
      },
    },
    // 优先级
    [TaskLogTypeEnum.Priority]: {
      icon: 'otd-icon-youxianji',
      label: t('common.priority.priority'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            {prevValue && (
              <>
                <OtdPriorityTrigger value={+prevValue.Value} />
                {getArrow()}
              </>
            )}
            {newValue ? <OtdPriorityTrigger value={+newValue.Value} /> : t('common.none')}
          </div>
        );
      },
    },
    // 前置任务
    [TaskLogTypeEnum.PreTask]: {
      icon: 'otd-icon-qianzhirenwu',
      label: t('common.predecessorTask'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            {prevValue ? prevValue.Label : t('common.none')}
            {getArrow()}
            {newValue ? newValue.Label : t('common.none')}
          </div>
        );
      },
    },
    // 任务进度
    [TaskLogTypeEnum.TaskProgress]: {
      icon: 'otd-icon-jindutiao',
      label: t('common.history.taskProgress'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            {prevValue && <span>{prevValue.Value}%</span>}
            {prevValue && newValue && getArrow()}
            {newValue && <span>{newValue.Value}%</span>}
          </div>
        );
      },
    },
    // 上传附件
    [TaskLogTypeEnum.File]: {
      icon: 'otd-icon-fujian',
      label: t('common.history.uploadAttachment'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        const label = prevValue?.Label || newValue?.Label;
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            <span>{option.label} :</span>
            <span class="otd-column-gap otd-time-line-content" style="--gap:2px;">
              <img style="height: 22px;" src={fileTypeToIcon(label)} alt="" />
              <span>{label}</span>
            </span>
          </div>
        );
      },
    },
    // 协作文档
    [TaskLogTypeEnum.Document]: {
      icon: 'otd-icon-wenjian-L',
      label: t('common.collaborationDocument.collaborativeDoc'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            {prevValue && <span>{`${prevValue.Label ?? t('common.none')} (${prevValue.Value})`}&nbsp;</span>}
            {prevValue && newValue && getArrow()}
            {newValue && <span>{`${newValue.Label ?? t('common.none')} (${newValue.Value})`}&nbsp;</span>}
          </div>
        );
      },
    },
    // 协作清单
    [TaskLogTypeEnum.Collaboration]: {
      icon: 'otd-icon-wj-wjlz',
      label: t('common.history.collaborativeList'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            {prevValue && <span>{prevValue.Label}</span>}
            {prevValue && newValue && getArrow()}
            {newValue && <span>{newValue.Label}</span>}
          </div>
        );
      },
    },
    // 标签
    [TaskLogTypeEnum.Tags]: {
      icon: 'otd-icon-a-cattagsize24',
      label: t('common.tag.tag'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        const label = prevValue?.Label || newValue?.Label;
        const color = prevValue?.Color || newValue?.Color;
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            {<TagItem item={{ tagName: label, color }} is-card />}
          </div>
        );
      },
    },
    // 负责人
    [TaskLogTypeEnum.Responsible]: {
      icon: 'otd-icon-ren',
      label: t('common.subtask.resPerson'),
      render: ({ record, option }) => {
        return getUserContent({ record, option, fields, getAvatar, historyAction: defaultHistoryAction });
      },
    },
    // 确认人
    [TaskLogTypeEnum.Checker]: {
      icon: 'otd-icon-taskqqr',
      label: t('common.subtask.verifier'),
      render: ({ record, option }) => {
        return getUserContent({ record, option, fields, getAvatar, historyAction: defaultHistoryAction });
      },
    },
    // 关注人
    [TaskLogTypeEnum.Follower]: {
      icon: 'otd-icon-guanzhurenyuan',
      label: t('common.subtask.watcher'),
      render: ({ record, option }) => {
        return getUserContent({ record, option, fields, getAvatar, historyAction: defaultHistoryAction });
      },
    },
    // 创建人
    [TaskLogTypeEnum.Creator]: {
      icon: 'otd-icon-guanzhurenyuan',
      label: t('common.subtask.creator'),
      render: ({ record, option }) => {
        return getUserContent({ record, option, fields, getAvatar, historyAction: defaultHistoryAction });
      },
    },
    // 自动完成
    [TaskLogTypeEnum.AutoCompletion]: {
      icon: 'otd-icon-zidongwancheng',
      label: t('common.autoCompletion'),
      render: ({ record, option }) => {
        const { newValue } = getTimeData(record, fields);
        return (
          <span>
            {newValue.Value === 'True' ? t('common.openText') : t('common.closeText')}
            {option.label}
          </span>
        );
      },
    },
    // 开始时间
    [TaskLogTypeEnum.PlanStartDate]: {
      icon: 'otd-icon-kaishishijian-copy',
      label: t('common.subtask.startTime'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            {prevValue ? prevValue.Value : t('common.none')}
            {getArrow()}
            {newValue ? newValue.Value : t('common.none')}
          </div>
        );
      },
    },
    // 结束时间
    [TaskLogTypeEnum.PlanDoneDate]: {
      icon: 'otd-icon-jiezhishijian',
      label: t('common.subtask.endTime'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        return (
          <div class="otd-column-gap otd-time-line-content" style="--gap:4px;">
            {defaultHistoryAction[record[actionType]](option.label)}
            {prevValue ? prevValue.Value : t('common.none')}
            {getArrow()}
            {newValue ? newValue.Value : t('common.none')}
          </div>
        );
      },
    },
    // 评论
    [TaskLogTypeEnum.Comment]: {
      icon: 'otd-icon-huifu',
      label: t('common.comment'),
      render: ({ record, option }) => {
        const { prevValue, newValue } = getTimeData(record, fields);
        const handler = ActionHandler[record[actionType]];
        return (
          <div class="otd-time-line-content otd-word-break">
            {handler ? (
              handler(newValue, record, option)
            ) : (
              <>
                {defaultHistoryAction[record[actionType]](option.label)}
                {prevValue && <span>{removeHtmlTag(prevValue.Label)}</span>}
                {prevValue && newValue && getArrow()}
                {newValue && <span>{removeHtmlTag(newValue.Label)}</span>}
              </>
            )}
          </div>
        );
      },
    },
  };
  // #endregion
  return {
    getFields,
    DefaultHistoryOptions,
  };
}
