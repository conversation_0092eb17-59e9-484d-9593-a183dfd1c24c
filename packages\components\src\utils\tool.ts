import { map, cloneDeep, concat, flatMapDeep } from 'lodash-es';
import { useI18n } from '/@/hooks/web/useI18n';
import QRCode, { QRCodeToDataURLOptions } from 'qrcode';

import * as XLSX from 'xlsx';

/**
 * 停止冒泡
 * @param e
 */
export function stopEvent(e) {
  e.preventDefault();
  e.stopPropagation();
}

// base64编码
export function encode(value): string {
  return window.btoa(window.encodeURIComponent(JSON.stringify(value)));
}
// base64解码
export function decode(value): Record<string, any> {
  return JSON.parse(window.decodeURIComponent(window.atob(value)));
}

/**
 * 对象或数组转换成对象
 * @param value 对象或数组
 * @param key 对象key主键
 * @param callback
 * @returns
 */
export function keyBy(value, key, callback?): Record<string, any> {
  const data = {};
  map(value, (item, index) => {
    let record = null;
    if (callback) {
      record = callback(cloneDeep(item), index);
    } else {
      record = item;
    }
    data[item[key]] = record;
  });
  return data;
}

/**
 * 去除二维数组列数据都为空的数据
 * @param data 对象或数组
 * @param length 列长度
 * @returns
 */
export function omitColumn(data: Array<Array<number | string | null>>, length): Array<any> {
  const value = cloneDeep(data);
  let index = 0;
  for (let i = 0; i < length; i++) {
    let state = false;
    for (const item of value) {
      if (item[index]) {
        state = true;
      }
    }
    if (!state) {
      for (const item of value) {
        item.splice(index, 1);
      }
    } else {
      index++;
    }
  }
  return value;
}

/**
 * 数组转换成对象组
 * @param data 数组
 * @param callback
 * @returns
 */
export function convert(data, callback): Record<string, any> {
  const result = {};
  for (const index in data) {
    result[callback(data[index], index)] = data[index];
  }
  return result;
}

/**
 * 将字节数转换为易读单位（KB、MB、GB）
 * @param bytes 字节数
 * @param decimals 保留位数
 * @returns
 */
export function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 去除字符串中的HTML标签
 * @param htmlStr HTML字符串
 * @returns
 */
export function removeHtmlTag(htmlStr) {
  const { t } = useI18n();
  const textarea = document.createElement('textarea');
  textarea.innerHTML = htmlStr;
  const textWithoutTags = textarea.value.replace(/<img[^>]*>/gi, `[${t('common.image')}]`).replace(/(<([^>]+)>)/gi, '');
  return textWithoutTags;
}

/**
 * 将字符串中的转义码转译成指定字符
 * @param str 字符串
 * @returns
 */
export function decodeHtml(str) {
  let txt: HTMLTextAreaElement | null = document.createElement('textarea');
  txt.innerHTML = str;
  const content = txt.value;
  txt = null;
  return content;
}

/**
 * 插入指定数据到数组对应位置之后
 * @param originalArray
 * @param newData
 * @param insertIndex
 * @returns
 */
export function insert(originalArray: any[], newData: any[], insertIndex: number) {
  const modifiedArray = concat(originalArray.slice(0, insertIndex + 1), newData, originalArray.slice(insertIndex + 1));
  return modifiedArray;
}

// 设置图片预览
export function imagePreviewSet(html) {
  if (!html) return [];
  let dom: HTMLDivElement | undefined = document.createElement('div');
  dom.innerHTML = html;
  const images = dom.querySelectorAll('img:not([width])');
  const imgList: string[] = [];
  images.forEach((item) => {
    imgList.push(item.getAttribute('src') as string);
  });
  dom = undefined;
  return imgList;
}

// 数组扁平化
export function flattenedData(data, key) {
  return flatMapDeep(data, (item) => {
    if (item[key] && item[key].length > 0) {
      // 如果有子任务项，则将子任务项添加到扁平化数组中
      return [item, ...item[key]];
    } else {
      // 如果没有子任务项，只返回原始任务项
      return item;
    }
  });
}

/**
 * 递归遍历数据
 * @param {object[]} data
 * @param {string} childrenKey
 * @param {Function} handle 返回true，停止遍历
 * @returns
 */
export function TraverseData(data: any[] = [], childrenKey: string, handle: (data) => boolean) {
  for (const item of data) {
    let result = handle(item);
    if (result) return result;
    if (item[childrenKey]) {
      result = TraverseData(item[childrenKey], childrenKey, handle);
    }
    if (result) return result;
  }
  return undefined;
}

/**
 * 递归寻找数据中第一次出现的指定下边的值
 * @param {object[]} data
 * @param {string} key
 * @param {string} childrenKey
 * @returns
 */
export function findFirstValue(data: any[] = [], key: string, childrenKey: string) {
  return TraverseData(data, childrenKey, (item) => {
    return item[key];
  });
}

/**
 * 递归同步数据指定下标的内容
 * @param {object[]} data
 * @param {string} key
 * @param {string} childrenKey
 * @param {Function} handle 额外处理的事件
 */
export function syncDataKeyValue(data: any[] = [], key: string, childrenKey: string, handle?: Function) {
  const syncValue = findFirstValue(data, key, childrenKey);
  TraverseData(data, childrenKey, (item) => {
    item[key] = syncValue;
    handle?.(item, syncValue);
    return false;
  });
}

/**
 * 导出excel
 * @param data
 * @param filename
 * @param sheetName
 */
export function exportToExcel(data, filename = 'exported_data', sheetName = 'Sheet1') {
  const ws = XLSX.utils.json_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, sheetName);
  XLSX.writeFile(wb, filename + '.xlsx');
}

/**
 * 阶段执行任务
 * @param data
 * @param phaseTask
 */
export function phaseExecution(data: number | any[], phaseTask: Function) {
  if (typeof data === 'number') {
    data = new Array(data).fill(0);
  }
  let index = 0;
  let cancelIndex: number | null = null;
  let endTask: Function | undefined = undefined;
  function _run() {
    if (index >= (data as []).length) {
      endTask?.();
    }
    return requestIdleCallback((idle) => {
      while (idle.timeRemaining() > 0 && index < (data as []).length) {
        phaseTask(data[index], index);
        index++;
        cancelIndex = _run();
      }
    });
  }
  cancelIndex = _run();
  return {
    cancel() {
      cancelIdleCallback(cancelIndex as number);
    },
    end(task) {
      endTask = task;
    },
  };
}

// 生成二维码
export async function generateQrCode(text: string): Promise<string> {
  return new Promise((resolve) => {
    const opts: QRCodeToDataURLOptions = {
      errorCorrectionLevel: 'L',
      type: 'image/jpeg',
      width: 128,
      margin: 1,
      color: { dark: '#000', light: '#fff' },
    };
    QRCode.toDataURL(text, opts, function (err, url) {
      if (err) throw err;
      resolve(url);
    });
  });
}
