<template>
  <Popover
    v-model:open="popoverShow"
    overlay-class-name="otd-popover action-popover"
    :align="popoverAlign"
    :title="item.expand ? '' : undefined"
    :arrow="false"
    :trigger="item.expandTrigger ?? 'click'"
    :placement="item.expandPlacement ?? 'right'"
    destroy-tooltip-on-hide
    @open-change="getPopoverShow"
  >
    <template #content>
      <div class="max-w-280px" v-if="item.expand">
        <component :is="isFunction(item.expand) ? item.expand(data) : item.expand" :data="data" />
      </div>
    </template>
    <div
      class="add-icon add-action-item"
      :class="[`is-${size}`, `is-${actionType}`, white ? 'is-white' : '']"
      :style="{ color: item.color }"
      @click="(e) => handleItemClick(e, { item, data, list, index })"
    >
      <ActionContent v-if="notMenu" :item="item" :data="data" :hideExpandName="hideExpandName" />
      <MenuItem
        class="otd-menu-item-open"
        :style="{ color: item.color }"
        :key="item.id"
        :disabled="item.disabled?.(data)"
        v-else
      >
        <ActionContent :item="item" :data="data" />
      </MenuItem>
    </div>
  </Popover>
</template>
<script lang="ts" setup>
  import type { MoreActionItem } from './type';
  import { PropType, ref } from 'vue';
  import { MenuItem, Popover } from 'ant-design-vue';
  import { computed } from 'vue';
  import { ActionContent } from './actionContent';
  import { Recordable } from '/#/global';
  import { getProps } from './props';
  import { isFunction } from '/@/tool';

  const props = defineProps({
    ...getProps(),
    item: {
      type: Object as PropType<MoreActionItem>,
      default: () => ({}),
    },
    index: {
      type: Number,
      default: -1,
    },
    notMenu: {
      type: Boolean,
      default: false,
    },
    stopped: {
      type: Boolean,
      default: true,
    },
  });
  const emit = defineEmits(['select', 'open-change']);

  const popoverAlign = { offset: [10] };

  const popoverVisible = ref(false);
  const popoverShow = computed({
    get: () => (props.item.expand ? popoverVisible.value : false),
    set: (value: boolean) => (popoverVisible.value = value),
  });

  type ActionParamsType = {
    item: MoreActionItem;
    data: Recordable;
    list: Recordable[];
    index: number;
  };

  // 操作点击事件
  function handleItemClick(e: MouseEvent, { item, data, list, index }: ActionParamsType) {
    if (item.disabled?.(data)) return false;
    if (props.stopped) e.stopPropagation();
    item.action?.(data, list, index);
    if (!item.expand) {
      popoverVisible.value = false;
      emit('select', props.item);
    }
  }

  function getPopoverShow(visible) {
    if (props.item.expand) {
      emit('open-change', visible);
    }
  }
</script>
<style lang="less" scoped>
  .max-w-280px {
    max-width: 280px;
  }

  .add-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: var(--otd-small-radius);
    line-height: 1;
    min-width: 20px;
    color: var(--action-color, var(--otd-icon-text));

    :deep(.add-icon__text) {
      font-size: 14px;
    }

    .otdIconfont {
      font-size: inherit;
    }
    &:hover {
      background-color: var(--otd-gray-hover) !important;
    }
    &.is-btn {
      border: 1px solid var(--otd-dropdown-border);
      background-color: var(--otd-dropdown-bg);
      box-shadow: 0 1px 4px 0 var(--otd-dropdown-btn-shadow);
    }
    &.is-large {
      :deep(> .otdIconfont) {
        font-size: 20px;
      }
      :deep(.add-icon__text) {
        line-height: 20px;
      }
      padding: 6px;
    }
    &.is-medium {
      :deep(> .otdIconfont) {
        font-size: 18px;
      }
      :deep(.add-icon__text) {
        line-height: 18px;
      }
      padding: 6px;
    }
    &.is-default {
      :deep(> .otdIconfont) {
        font-size: 16px;
      }
      :deep(.add-icon__text) {
        line-height: 16px;
      }
      padding: 5px;
    }
    &.is-small {
      :deep(> .otdIconfont) {
        font-size: 14px;
        min-width: 14px;
      }
      :deep(.add-icon__text) {
        line-height: 14px;
      }
      padding: 4px;
    }
    &.is-mini {
      :deep(> .otdIconfont) {
        font-size: 12px;
        min-width: 12px;
      }
      padding: 4px;
    }
  }
</style>
