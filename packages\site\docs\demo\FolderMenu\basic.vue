<template>
  <OtdFolderMenu
    title="测试"
    v-model:selected="selectedKeys"
    :folder-request="FolderRequest"
    resource
    @select="judgFolderSelect"
    @reload="handleFolderRefrush"
  />
</template>
<script lang="ts" setup>
  import { FolderRequestType, OtdFolderMenu, ResourceCategoryEnum } from '@otd/otd-ui';
  import { ref } from 'vue';

  const selectedKeys = ref(['-1']);

  const FolderRequest: FolderRequestType = {
    getList: () => {
      const time = Date.now();
      return Promise.resolve({
        items: [
          {
            name: '测试-任务模板01',
            description: '测试-任务模板01',
            parentDirectory: null,
            type: 'Directory',
            hasChildDirectory: true,
            hasChildTemplate: true,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2023-05-31T16:26:17.633994',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0b8317-0e82-e436-108c-4fb3d4df99fc',
            extraProperties: {},
          },
          {
            name: '测试-任务模板02',
            description: '',
            parentDirectory: null,
            type: 'Directory',
            hasChildDirectory: false,
            hasChildTemplate: true,
            lastModificationTime: '2023-05-31T16:40:38.489108',
            lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            creationTime: '2023-05-31T16:26:23.600571',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0b8317-25ec-45c0-4509-a0caaee07e1f',
            extraProperties: {},
          },
        ],
      });
    },
    getResourceList: () => {
      const time = Date.now();
      return Promise.resolve({
        items: [
          {
            name: '测试-任务模板01',
            description: '测试-任务模板01',
            parentDirectory: null,
            type: 'Directory',
            hasChildDirectory: true,
            hasChildTemplate: true,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2023-05-31T16:26:17.633994',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0b8317-0e82-e436-108c-4fb3d4df99fc',
            extraProperties: {},
          },
          {
            name: '测试-任务模板02',
            description: '',
            parentDirectory: null,
            type: 'Directory',
            hasChildDirectory: false,
            hasChildTemplate: true,
            lastModificationTime: '2023-05-31T16:40:38.489108',
            lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            creationTime: '2023-05-31T16:26:23.600571',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0b8317-25ec-45c0-4509-a0caaee07e1f',
            extraProperties: {},
          },
          {
            name: '测试任务12',
            description: '测试任务11',
            parentDirectoryId: null,
            childMaxLevel: 0,
            isDeleted: false,
            deleterId: null,
            deletionTime: null,
            lastModificationTime: '2023-09-06T14:17:41.252426',
            lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            creationTime: '2023-04-28T09:50:25.986653',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0ad7ba-c5e5-458c-67ae-e20cd0319a7f',
            extraProperties: {},
          },
          {
            name: '测试任务2',
            description: '测试任务2',
            parentDirectoryId: null,
            childMaxLevel: 0,
            isDeleted: false,
            deleterId: null,
            deletionTime: null,
            lastModificationTime: null,
            lastModifierId: null,
            creationTime: '2023-04-28T10:33:05.794403',
            creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            id: time + '3a0ad7e1-d5ff-b0dd-7018-e273ba033e40',
            extraProperties: {},
          },
        ],
      });
    },
    create: (form) => {
      console.log(form, 'create');
      return Promise.resolve();
    },
    update: (form) => {
      console.log(form, 'update');
      return Promise.resolve();
    },
    delete: () => {
      return Promise.resolve();
    },
    move: (...arg) => {
      console.log(arg);

      return Promise.resolve();
    },
  };

  // 文件目录刷新
  function handleFolderRefrush() {
    console.log(333);
  }
  // 判断点击是文件夹还是文件
  function judgFolderSelect(data) {
    console.log(data);
  }
</script>
<style lang="less" scoped></style>
