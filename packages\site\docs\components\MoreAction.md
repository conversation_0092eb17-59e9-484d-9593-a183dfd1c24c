# 下拉操作

## 基础

<demo src="../demo/MoreAction/basic.vue" title="基础"></demo>

## 按钮平铺

<demo src="../demo/MoreAction/launchMoreAction.vue" title="按钮平铺"></demo>

## icon 按钮

<demo src="../demo/MoreAction/iconMoreAction.vue" title="icon按钮"></demo>

## 可扩展按钮

<demo src="../demo/MoreAction/extendMoreAction.vue" title="可扩展按钮"></demo>

## 可搜索

<demo src="../demo/MoreAction/searchMoreAction.vue" title="可扩展按钮"></demo>

## 显示操作名称

<demo src="../demo/MoreAction/basicShowExpandName.vue" title="显示操作名称"></demo>

## 属性

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| data | 数据字段 | object | -- | {} | 1.0 |
| list | 数据字段 | array | -- | [] | 1.0 |
| index | 索引 | number | -- | undefined | 1.0 |
| expandNumber | 选项平铺数量，超出隐藏 | number | -- | 0 | 1.0 |
| disabled | 是否禁用 | function | -- | ()=> false | 1.0 |
| hide-expand-name | 隐藏展开文本 | boolean | -- | false | 1.0 |
| destroyPopupOnHide | 关闭后是否销毁 Dropdown | boolean | -- | false | 1.0 |
| isShowTitle | 是否禁止显示 Tooltip | boolean | -- | false | 1.0 |
| hideClass | 隐藏样式类 | string | -- | hide-action | 1.0 |
| trigger | 触发方式 | string | hover, click, contextmenu | click | 1.0 |
| size | 尺寸 | string | small, default, medium, large | default | 1.0 |
| actionType | 操作按钮类型 | string | btn, icon | btn | 1.0 |
| search | 是否显示搜索框 | boolean | -- | false | 1.0 |
| emitSearch | 是否为外部搜索 | boolean | -- | false | 1.0 |
| Actions | 下拉操作的详细配置，参下表<a href="#actions">Actions</a> |  | -- | [] | 1.0 |
| Events | 下拉操作的事件，参下表<a href="#events">Events</a> |  | -- | -- | 1.0 |

### <span id='actions'>Actions</span>

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| id | 唯一值 | string,number | -- | -- | 1.0 |
| tip | 适用于展开按钮,当展开按钮 name 不存在时生效, 显示方式为浮动显示 | string | -- | -- | 1.0 |
| name | 选项名称当(为展开按钮时为 tip 提示, 需要使用 hideExpandName) | string | -- | -- | 1.0 |
| color | 选项颜色 | string | -- | -- | 1.0 |
| icon | 选项图标 | string | -- | -- | 1.0 |
| iconSize | 图标大小 | string | -- | -- | 1.0 |
| customRender | 自定义内容 | () => VNode | -- | -- | 1.0 |
| disabled | 禁用菜单内选项 | () => boolean | -- | false | 1.0 |
| isHide | 是否显示选项 | function | -- | ()=> boolean | 1.0 |
| expand | 点击选项,展示扩展内容 | VNode | -- | ()=> VNode | 1.0 |
| expandPlacement | 扩展弹出方向 | string | -- | `bottom` | 1.0 |
| expandTrigger | 扩展触发方式 | string | -- | `hover` | 1.0 |
| auth | 权限标识,需要配置 ConfigProvider | string | -- | -- | 1.0 |
| loading | 操作 loading | boolean | -- | `false` | 1.0 |
| isHide | 是否隐藏操作 | (data) =>boolean | -- | -- | 1.0 |
| action | 点击事件的回调, 返回当前对象, 当前数组,及当前索引 | function | -- | function(data, list, index) | 1.0 |

### <span id='events'>Events</span>

| 参数  | 说明                                             | 类型     | 可选值 | 默认值                  | 版本 |
| ----- | ------------------------------------------------ | -------- | ------ | ----------------------- | ---- |
| query | 搜索模式下当 emitSearch 存在, Input 值改变时触发 | function | --     | (value: string) => void | 1.0  |
