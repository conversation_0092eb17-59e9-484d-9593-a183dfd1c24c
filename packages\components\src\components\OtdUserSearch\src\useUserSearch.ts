import type { UserOptionItemType, UserSearchPropsType } from './type';
import { ComponentInternalInstance, getCurrentInstance, ref, unref } from 'vue';
import { uniqBy } from 'lodash-es';
import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';

export function useUserSearch() {
  const { props } = getCurrentInstance() as ComponentInternalInstance & {
    props: UserSearchPropsType;
  };

  const { getGlobalProvide } = useGlobalConfig();
  const { getFetchUser, getUserData } = getGlobalProvide();
  const requestMap = {
    getFetchUser: props.remoteMethod ?? getFetchUser,
    getUserData: props.getList ?? getUserData,
  };

  // 搜索用户
  function getRemoteMethod(query) {
    return props.remote ? fetchUser(query) : getOrganizationUser(query);
  }

  // 远程搜索获取用户
  function fetchUser(query) {
    return requestMap.getFetchUser?.(query);
  }

  // 原始数据
  const sourceUserData = ref<UserOptionItemType[]>();
  // 获取组织机构用户
  function getOrganizationUser(query) {
    if (props.remoteMethod)
      return props.remoteMethod(query).then((res) => {
        const data = uniqBy(res, 'value');
        sourceUserData.value = data;
        return data;
      });
    if (sourceUserData.value) {
      return Promise.resolve(unref(sourceUserData)?.filter((item) => item.label?.includes(query.keyword ?? '')) ?? []);
    } else {
      return requestMap.getUserData?.(query).then((res) => {
        const data = uniqBy(res, 'value');
        sourceUserData.value = data;
        return data;
      });
    }
  }

  return {
    getRemoteMethod,
  };
}
