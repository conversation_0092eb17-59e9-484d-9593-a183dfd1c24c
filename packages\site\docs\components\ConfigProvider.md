# ConfigProvider 全局设置

<p style="font-size:26px">代码演示</p>

## 基础 ConfigProvider

<demo src="../demo/ConfigProvider/basic.vue" title="基础 ConfigProvider"></demo>

## 属性

| 参数         | 说明       | 类型                              | 可选值 | 默认值 | 版本 |
| ------------ | ---------- | --------------------------------- | ------ | ------ | ---- |
| layoutConfig | 全局化配置 | Object [类型](#layoutconfig-类型) |        |        | 1.0  |

其他参考属性参考 ant-design-vue[ConfigProvider 全局化配置](https://next.antdv.com/components/config-provider-cn)

## LayoutConfig 类型

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| userInfo | 用户信息 | Object [类型](#userinfo-类型) |  |  | 1.0 |
| logout | 返回登录逻辑 | () => Promise(any) |  |  | 1.0 |
| resetPassword | 重置密码接口 | (form) => Promise(any) |  |  | 1.0 |
| uploadAvatar | 上传头像接口 | (data) => Promise(any) |  |  | 1.0 |
| webSocketUrl | 长连接链接 | () => string |  |  | 1.0 |
| notifyPageSize | 通知每页条数 | Number | 5 |  | 1.0 |
| notificationRequest | 通知请求接口 | (data) => Promise({items:Array,totalCount:number}) |  |  | 1.0 |
| readAllNotify | 通知全部已读接口 | (type) => Promise(any) |  |  | 1.0 |
| setReadNotify | 通知设置单条已读接口 | (id) => Promise(any) |  |  | 1.0 |

## ActionItem 类型

| 参数   | 说明                 | 类型                        | 可选值 | 默认值 | 版本 |
| ------ | -------------------- | --------------------------- | ------ | ------ | ---- |
| id     | 唯一标识             | String                      |        |        | 1.0  |
| name   | 操作名称             | String                      |        |        | 1.0  |
| icon   | 操作图标（Iconfont） | String                      |        |        | 1.0  |
| action | 操作事件             | (data, list, index) => void |        |        | 1.0  |
