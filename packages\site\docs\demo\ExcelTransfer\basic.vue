<template>
  <OtdExcelTransfer
    modalTitle="导入任务"
    btnPlaceholder="导入"
    :columns="columns"
    :template-columns="templateColumns"
    download-url="https://picsum.photos/200/300"
    limit-size="20KB"
    @submit="handleSubmit"
  />
</template>

<script lang="tsx" setup>
  import dayjs from 'dayjs';
  import { OtdExcelTransfer, OtdEditCellItem, OtdUserSearch, Input, OtdDatePicker, TableColumnPropsType } from '@otd/otd-ui';

  const columns: TableColumnPropsType[] = [
    {
      title: '任务名称',
      dataIndex: [0, 'name'],
      key: 'name',
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <Input v-model:value={record.name} />
          </OtdEditCellItem>
        );
      },
    },
    {
      title: '时间',
      dataIndex: [2, 'planDate'],
      key: 'planDate',
      contentHandler: ({ excel }) => {
        return [excel['1'] && dayjs(excel['1']), excel['2'] && dayjs(excel['2'])];
      },
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <OtdDatePicker v-model:value={record.planDate} hide-remind hide-repeat />
          </OtdEditCellItem>
        );
      },
    },
    {
      title: '负责人',
      dataIndex: [3, 'user'],
      key: 'user',
      contentHandler: ({ excel }) => {
        return { label: excel['3'], value: '5873157c-943e-9b23-ed46-3a0de2a8ce36', img: '' };
      },
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <OtdUserSearch v-model:value={record.user} placeholder="负责人" is-text clearable />
          </OtdEditCellItem>
        );
      },
    },
  ];
  const templateColumns = ['任务名称', '开始时间', '截止时间', '负责人'];
  function handleSubmit(value) {
    console.log(value);
  }
</script>
