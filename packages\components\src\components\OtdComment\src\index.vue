<template>
  <div class="otd-comment">
    <template v-if="!loading">
      <CommentCard
        v-for="(comment, index) in list"
        :key="index"
        :list="list"
        :index="index"
        :comment="comment"
        v-bind="getComProps"
        @emoticon="handleEmoticon"
        @reply="handleReply"
        @delete="handleDelete"
        @confirm="handleConfirm"
      />
    </template>
    <template v-else>
      <Skeleton :avatar="{ size: 'default' }" active v-for="index in 3" :key="index" />
    </template>
  </div>
</template>
<script lang="ts" setup>
  import { Skeleton } from 'ant-design-vue';
  import CommentCard from './components/CommentCard.vue';
  import { getEmits } from './props';
  import { PropType, useAttrs } from 'vue';
  import { Recordable } from '/#/global';
  import { getProps } from '/@/components/OtdTinymce/src/components/OtdSignleTinymce/props';
  import { computed } from 'vue';
  import { omit } from 'lodash-es';

  const props = defineProps({
    list: {
      type: Array as PropType<Recordable[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    ...getProps(),
  });
  const emit = defineEmits(getEmits());

  const attrs = useAttrs();
  const getComProps = computed(() => {
    return {
      ...omit(props, 'list'),
      ...attrs,
    };
  });

  function handleEmoticon(...arg) {
    emit('emoticon', ...arg);
  }
  function handleReply(...arg) {
    emit('reply', ...arg);
  }
  function handleDelete(...arg) {
    emit('delete', ...arg);
  }
  function handleConfirm(...arg) {
    emit('confirm', ...arg);
  }
</script>
<style lang="less" scoped>
  .otd-comment {
    display: flex;
    flex-direction: column;
    row-gap: 16px;
    .ant-skeleton:not(:last-child) {
      border-bottom: 1px solid var(--otd-border-color);
      padding-bottom: 16px;
    }
  }
</style>
