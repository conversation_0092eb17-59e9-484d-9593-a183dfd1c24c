import { throttle, merge } from 'lodash-es';
import { ScrollbarConfigType, ScrollbarInfoType } from '../type';
// 滚动条模块
export class ScrollbarMode {
  private tool: { ctx; canvas; parentNode: HTMLDivElement; content; updateCanvas };
  private config: ScrollbarConfigType = {
    borderColor: '#ebebeb',
  };
  private scrollInfo: ScrollbarInfoType = {
    scrollY: '',
    scrollX: '',
  };
  private scrollDom: { x?: HTMLDivElement; y?: HTMLDivElement } = {};
  // 滚动位置
  scroll = { x: 0, y: 0 };

  constructor(config: ScrollbarConfigType, tool) {
    this.config = merge(this.config, config);
    this.tool = tool;
    const idTime = Date.now();
    this.scrollInfo.scrollY = `scrollY_${idTime}`;
    this.scrollInfo.scrollX = `scrollX_${idTime}`;
    this.init();
  }
  // 初始化
  private init() {
    this.addEventListener();
    this.addScrollBarStyle();
  }
  // 添加滚动条样式
  private addScrollBarStyle() {
    const { canvas } = this.tool;
    canvas.classList.add('otd-scroll_contariner');
    const style = document.createElement('style');
    // .otd-scroll_contariner:hover ~ .otd-scroll_bar,
    // .otd-scroll_bar:hover {
    //   visibility: unset;
    // }
    // visibility: hidden;
    style.innerHTML = `
      .otd-scroll_bar {
        overflow: scroll;
        position: absolute
      }
      .otd-scroll_bar::-webkit-scrollbar-track {
        background-color: transparent;
      }
    `;
    document.head.append(style);
  }
  private updateCanvas(status = true) {
    this.tool.updateCanvas.bind(this.tool)(status);
  }
  // 监听事件
  private addEventListener() {
    // 滚动滚轮
    this.tool.canvas.addEventListener('wheel', throttle(this.handleScroll.bind(this), 30));
  }
  // 设置滚动条内容
  private setScrollbarSize(scrollbar, otherDom, attr) {
    return new Promise((resolve, reject) => {
      if (scrollbar) {
        const { content } = this.tool;
        const node = scrollbar.childNodes[0] as HTMLDivElement;
        const scrollbarWidth = scrollbar.offsetWidth - scrollbar.clientWidth;
        node.style[attr] = `${content[attr] - scrollbarWidth + (otherDom ? 8 : 0)}px`;
        return resolve(true);
      }
      reject(false);
    });
  }
  // 处理滚动事件
  private handleScroll(event) {
    const { canvas, content } = this.tool;
    const {
      scrollInfo: { scrollXDom, scrollYDom },
    } = this;
    if (event.shiftKey) {
      if (!scrollXDom) return;
      // 按住Shift键时，触发横向滚动
      const scrollLeft = scrollXDom.scrollLeft + event.deltaY;
      // 限制滚动范围
      scrollXDom.scrollLeft = Math.max(0, Math.min(content.width - canvas.offsetWidth, scrollLeft));
    } else {
      if (!scrollYDom) return;
      // 否则，触发竖直滚动
      const scrollTop = scrollYDom.scrollTop + event.deltaY;
      // 限制滚动范围
      scrollYDom.scrollTop = Math.max(0, Math.min(content.height - canvas.offsetHeight, scrollTop));
    }
    this.updateCanvas();
  }
  // 纵向滚动条
  drawVerticalScrollBar() {
    const { scrollInfo, scroll } = this;
    const { canvas, parentNode, content } = this.tool;
    const { borderColor } = this.config;
    if (content.height > canvas.height) {
      this.setScrollbarSize(scrollInfo.scrollYDom, scrollInfo.scrollXDom, 'height').catch(() => {
        parentNode.style.position = 'relative';
        const scrollbar = document.createElement('div');
        scrollbar.classList.add('otd-scroll_bar');
        scrollbar.innerHTML = `<div style="height:${content.height + 8}px;width:1px"></div>`;
        scrollInfo.scrollYDom = scrollbar;
        scrollbar.setAttribute('id', scrollInfo.scrollY);
        scrollbar.style.borderLeft = `1px solid ${borderColor}`;
        scrollbar.style.right = '0';
        scrollbar.style.top = '0';
        scrollbar.style.height = '100%';
        parentNode.append(scrollbar);
        parentNode.style.setProperty('padding-right', `${scrollbar.offsetWidth}px`);
        this.scrollDom.y = scrollbar;
        scrollbar.addEventListener(
          'scroll',
          throttle(() => {
            scroll.y = scrollbar.scrollTop;
            this.updateCanvas(false);
          }, 30),
        );
      });
    } else {
      parentNode.querySelector(`#${scrollInfo.scrollY}`)?.remove();
      parentNode.style.paddingRight = '0';
      scrollInfo.scrollYDom = undefined;
    }
  }
  // 横向滚动条
  drawHorizontalScrollBar() {
    const { scrollInfo, scroll } = this;
    const { canvas, parentNode, content } = this.tool;
    const { borderColor } = this.config;
    if (content.width > canvas.width) {
      this.setScrollbarSize(scrollInfo.scrollXDom, scrollInfo.scrollYDom, 'width').catch(() => {
        parentNode.style.position = 'relative';
        const scrollbar = document.createElement('div');
        scrollbar.classList.add('otd-scroll_bar');
        scrollbar.innerHTML = `<div style="width:${content.width + 8}px;height:1px"></div>`;
        scrollInfo.scrollXDom = scrollbar;
        scrollbar.setAttribute('id', scrollInfo.scrollX);
        scrollbar.style.borderTop = `1px solid ${borderColor}`;
        scrollbar.style.left = '0';
        scrollbar.style.bottom = '0';
        scrollbar.style.width = '100%';
        parentNode.append(scrollbar);
        parentNode.style.setProperty('padding-bottom', `${scrollbar.offsetHeight}px`);
        this.scrollDom.x = scrollbar;
        scrollbar.addEventListener(
          'scroll',
          throttle(() => {
            scroll.x = scrollbar.scrollLeft;
            this.updateCanvas(false);
          }, 30),
        );
      });
    } else {
      parentNode.querySelector(`#${scrollInfo.scrollX}`)?.remove();
      parentNode.style.paddingBottom = '0';
      scrollInfo.scrollXDom = undefined;
    }
  }

  setScroll(config?: { x: number; y: number }) {
    const {
      scrollInfo: { scrollXDom, scrollYDom },
    } = this;
    if (scrollXDom) {
      scrollXDom.scrollLeft = config?.x ?? scrollXDom.scrollLeft;
    }
    if (scrollYDom) {
      scrollYDom.scrollTop = config?.y ?? scrollYDom.scrollTop;
    }
  }

  update(borderColor?: string) {
    if (borderColor) {
      Object.values(this.scrollDom).map((dom) => {
        dom.style.setProperty('border-color', borderColor);
      });
    }
  }
}
