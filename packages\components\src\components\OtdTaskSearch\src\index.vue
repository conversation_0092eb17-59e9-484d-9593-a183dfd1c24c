<template>
  <div class="otd-task-search">
    <BasicSearchPopover
      v-model:value="modelValue"
      v-bind="getBindValue"
      :container-text="t('common.lineUp.recent')"
      :remote-method="requestMap.getFetchTask"
      width="350px"
    >
      <template #default="{ cancelItem }">
        <slot>
          <div class="otd-task-search__trigger placeholder-hover placeholder-text otd-truncate">
            <template v-if="!modelValue">
              <i class="otdIconfont" :class="[icon]"></i>
              <span>{{ getPlaceholder }}</span>
            </template>
            <template v-else>
              <div
                class="otd-task-option-name otd-truncate otd-column-gap"
                v-for="item in getModelValue"
                :key="item.value"
              >
                <OtdStatusTrigger :value="item.status" is-text hide-text />
                <span class="otd-truncate">{{ item.label }}</span>
                <Tooltip :title="t('common.delText')">
                  <i class="otdIconfont otd-icon-a-catthicksize24" @click.stop="cancelItem(item)"></i>
                </Tooltip>
              </div>
            </template>
          </div>
        </slot>
      </template>
      <template #item="{ item }">
        <Popover placement="bottomLeft" :align="popoverAlign" destroy-tooltip-on-hide>
          <template #content>
            <div class="otd-task-option-source" v-if="item.source.relatedType">
              {{ item.source.relatedType }} /
              <span v-if="item.source.subRelatedType">{{ item.source.subRelatedType }} /</span>
              {{ item.source.relatedName }}
            </div>
            <div class="otd-task-option-name otd-column-gap">
              <OtdStatusTrigger :value="item.status" is-text hide-text />
              <span class="otd-truncate">{{ item.label }}</span>
            </div>
          </template>
          <div class="otd-task-option otd-truncate">
            <div class="otd-task-option-name otd-truncate otd-column-gap">
              <OtdStatusTrigger :value="item.status" is-text hide-text />
              <span class="otd-truncate">{{ item.label }}</span>
            </div>
            <div class="otd-task-option-user otd-column-gap">
              <OtdAvatar :url="item.user.avatar" />
              <span>{{ item.user.name }}</span>
            </div>
          </div>
        </Popover>
      </template>
    </BasicSearchPopover>
  </div>
</template>
<script lang="ts" setup>
  import { computed, useAttrs } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useTaskSearch } from './useTaskSearch';
  import { getEmits, getProps } from './props';
  import { BasicSearchPopover } from '/@/components/BasicSearchPopover';
  import { OtdStatusTrigger } from '/@/components/OtdStatus';
  import { Popover, Tooltip } from 'ant-design-vue';
  import { OtdAvatar } from '../../OtdAvatar';
  import { TaskOptionItemType } from './type';
  // import { OtdAvatar } from '/@/components/OtdAvatar';

  const props = defineProps(getProps());
  const emit = defineEmits(getEmits());

  const { t } = useI18n();
  const attrs = useAttrs();
  const popoverAlign = { offset: [200, 0] };

  const { requestMap } = useTaskSearch();
  const getPlaceholder = computed(() => props.placeholder || t('common.chooseText'));
  const modelValue = computed({
    get: () => props.value,
    set: (value) => emit('update:value', value),
  });

  const getModelValue = computed<TaskOptionItemType[]>(() =>
    Array.isArray(modelValue.value) ? modelValue.value : [modelValue.value!],
  );
  const getBindValue = computed(() => ({
    ...props,
    ...attrs,
  }));

  // function cancelItem(index) {
  //   if (Array.isArray(modelValue.value)) {
  //     modelValue.value?.splice(index, 1);
  //   } else {
  //     modelValue.value = undefined;
  //   }
  // }
</script>
<style lang="less" scoped>
  .otd-task-search {
    &__trigger {
      font-size: 13px;
      display: flex;
      align-items: center;
      column-gap: 4px;
      .otdIconfont {
        font-size: 12px;
      }
    }
  }
  .otd-task-option {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-source {
      --gap: 4px;
      font-size: 12px;
      color: var(--otd-gray3-color);
      line-height: 20px;
      flex-wrap: nowrap;
      width: 100%;
    }
    &-name {
      // float: left;
      flex-wrap: nowrap;
    }
    &-user {
      // float: right;
      flex-wrap: nowrap;
      column-gap: 4px;
    }
  }
</style>
