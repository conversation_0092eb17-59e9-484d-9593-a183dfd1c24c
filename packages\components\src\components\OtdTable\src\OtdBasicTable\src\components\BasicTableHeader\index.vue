<template>
  <div class="otd-basic-table__header otd-basic-table__tr">
    <BasicTableCell
      class="otd-basic-table__th"
      v-for="(field, fieldIndex) in columns"
      :key="field['dataIndex'] as string ?? fieldIndex"
      :column="field"
      :row-key="rowKey"
      is-header
      render="headerRender"
      v-bind="$attrs"
    >
      <template #indent v-if="fieldIndex === 0">
        <slot name="checkbox"></slot>
      </template>
      {{ field.title }}
    </BasicTableCell>
  </div>
</template>
<script lang="ts" setup>
  import { BasicTableCell } from '../BasicTableCell';
  import { getProps } from '../../props';

  defineProps(getProps());
</script>
<style lang="less" scoped>
  .otd-basic-table {
    &__header {
      // display: grid;
    }
  }
</style>
