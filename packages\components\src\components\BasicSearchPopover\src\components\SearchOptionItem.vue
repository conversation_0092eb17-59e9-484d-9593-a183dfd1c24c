<template>
  <li
    class="otd-search-popover__item otd-option-item"
    :class="{ 'is-active': isActive }"
    @click.stop="selectItem?.(item, index)"
  >
    <div class="otd-search-popover__item-info">
      <slot :item="item" :index="index"></slot>
    </div>

    <div class="otd-search-popover__item-operation" @click.stop="cancelItem?.(item)" v-if="showCancel">
      <Tooltip :title="t('common.delText')">
        <i class="otdIconfont otd-icon-a-catthicksize24 placeholder-hover"></i>
      </Tooltip>
    </div>
  </li>
</template>
<script lang="ts" setup>
  import { useI18n } from '/@/hooks/web/useI18n';
  import { SearchOptionType } from '../type';
  import { PropType } from 'vue';
  import { Tooltip } from 'ant-design-vue';

  defineProps({
    item: {
      type: Object as PropType<SearchOptionType>,
      required: true,
    },
    index: {
      type: Number,
    },
    isActive: {
      type: Boolean,
      default: false,
    },
    selectItem: {
      type: Function,
    },
    cancelItem: {
      type: Function,
    },
    showCancel: {
      type: Boolean,
      default: false,
    },
  });

  const { t } = useI18n();
</script>
<style lang="less" scoped>
  @user-search: ~'@{namespace}-search-popover';
  .@{user-search}__item {
    &-info {
      display: flex;
      align-items: center;
      column-gap: 8px;
      flex: 1;
      overflow: hidden;
      &__name {
        flex: 1;
        font-size: 14px;
        color: var(--otd-basic-text);
        line-height: 16px;
      }
      .otdIconfont {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: middle;
        background-color: var(--otd-gray4-color);
        width: 24px;
        height: 24px;
        color: var(--otd-body-text);
        border-radius: var(--otd-circle-radius);
        line-height: 1;
      }
    }
    &-operation {
      .otdIconfont {
        font-size: 12px;
        color: var(--otd-gray3-color);
      }
    }
  }
</style>
