<template>
  <div class="otd-track-time">
    <OtdTable @register="registerMainTable" bordered>
      <template #headerCell="{ column }">
        <div class="otd-table-action" v-if="column.dataIndex === 'time'">
          <span>{{ column.title }}</span>
          <OtdMoreAction
            :actions="[
              {
                id: 1,
                name: t('common.viewText'),
                icon: 'otd-icon-view',
                action: () => openModalDetail(true, 1),
              },
            ]"
            size="medium"
            :expand-number="1"
            hide-expand-name
          />
        </div>
      </template>
      <template #expandedRowRender="{ record }">
        <OtdTable :dataSource="record.expandChildren" @register="registerExpandTable" />
      </template>
    </OtdTable>

    <!-- 工时详情 -->
    <TrackTimeDetail
      ref="hourDetail"
      :title="t('common.trackTime.WorkHoursDetails')"
      :load-detail="loadDetail"
      :actions="actionList"
      :disabled="disabled"
      :category-options="categoryOptions"
      :default-category="defaultCategory"
      v-bind="getToProps"
      @register="registerDetail"
    />
    <!-- 录入工时 -->
    <!-- :title="`${t('common.trackTime.trackTime')}${t('common.entry')}`" -->

    <TrackTimeEntry
      :default-date="defaultDate"
      :category-options="categoryOptions"
      :default-category="defaultCategory"
      v-bind="getToProps"
      @register="registerEntry"
    />
  </div>
</template>
<script lang="ts" setup>
  import { OtdTable } from '/@/components/OtdTable';
  import { OtdMoreAction } from '/@/components/OtdMoreAction';
  import { useTrackTime } from './useTrackTime';
  import { computed, useAttrs } from 'vue';
  import { getEmits, getProps } from './props';
  import TrackTimeDetail from './TrackTimeDetail/index.vue';
  import TrackTimeEntry from './TrackTimeEntry/index.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { watchEffect } from 'vue';

  defineProps(getProps());
  const emit = defineEmits(getEmits());

  const { t } = useI18n();
  const attrs = useAttrs();
  const getToProps = computed(() => ({
    ...attrs,
    onDelete: (...arg) => emit('delete', ...arg),
    onDeleteDetail: (...arg) => emit('delete-detail', ...arg),
    onSubmit: (...arg) => emit('submit', ...arg),
    onReload: (...arg) => handleUpdateRecord(...arg),
  }));

  const [
    { registerEntry, registerDetail, registerMainTable, registerExpandTable },
    { actionList, setTrackTimeSource, openModalDetail, handleUpdateRecord },
  ] = useTrackTime();

  watchEffect(() => {
    setTrackTimeSource();
  });
</script>
<style lang="less" scoped>
  .otd-track-time {
  }
</style>
