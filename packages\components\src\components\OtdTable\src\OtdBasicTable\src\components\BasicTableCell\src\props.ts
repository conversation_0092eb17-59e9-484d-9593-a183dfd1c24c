import { PropType } from 'vue';
import { getProps as getTableProps } from '../../../props';
import { Recordable } from '/#/global';
import { TableColumnPropsType } from '/@/components/OtdTable/src/OtdTable';

export const getProps = () => ({
  ...getTableProps(),
  record: {
    type: Object as PropType<Recordable>,
    default: () => ({}),
  },
  render: {
    type: String as PropType<'customRender' | 'headerRender'>,
    default: 'customRender',
  },
  column: {
    type: Object as PropType<TableColumnPropsType>,
    default: () => ({}),
  },
  showExpand: {
    type: Boolean,
    default: false,
  },
  isHeader: {
    type: Boolean,
    default: false,
  },
});
