<template>
  <div
    class="otd-poster"
    v-loading="loading"
    :loading-tip="t('common.poster.generating')"
    loading-background="var(--otd-basic-bg)"
  >
    <canvas ref="canvasRef" :style="{ width, height }"></canvas>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted } from 'vue';
  import { useDrawPoster } from './useDrawPoster';
  import { getProps } from './props';
  import { useI18n } from '/@/hooks/web/useI18n';

  defineProps(getProps());

  const { t } = useI18n();

  const { canvasRef, loading, drawPosterInit, drawPosterContent, toDataURL } = useDrawPoster();

  onMounted(() => {
    drawPosterInit();
  });

  defineExpose({
    toDataURL,
    drawPoster: drawPosterContent,
  });
</script>
<style lang="less" scoped>
  .otd-poster {
    display: flex;
    position: relative;
    width: fit-content;
    height: fit-content;
    border-radius: var(--otd-small-radius);
    overflow: hidden;
    border: 1px solid var(--otd-border-color);
  }
</style>
