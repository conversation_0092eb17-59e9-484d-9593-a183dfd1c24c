<template>
  <OtdTable @register="register" @dragEnd="dragEnd">
    <template #filter>111</template>
  </OtdTable>
</template>
<script lang="tsx" setup>
  import { OtdTable, Select, TableColumnPropsType, TableSummaryType, useTable } from '@otd/otd-ui';
  import { reactive } from 'vue';

  const tableColumn: TableColumnPropsType[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      fixed: 'left',
      width: '100px',
      sorter: true,
      hideSetting: true,
      key: 'name',
      customRender: ({ record }) => {
        return <div>{record.key}</div>;
      },
    },
    {
      title: '姓名',
      dataIndex: 'name2',
      fixed: 'left',
      width: '100px',
      key: 'name2',
      sorter: true,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: '1200px',
      align: 'center',
      key: 'age',
    },
    {
      title: '住址',
      // fixed: "right",
      dataIndex: 'address',
      width: '200px',
      key: 'address',
    },
    {
      title: '住址',
      fixed: 'right',
      dataIndex: 'address2',
      width: '200px',
      key: 'address2',
    },
  ];

  const SummaryData: TableSummaryType[] = [
    {
      age: ({ data, column }) => {
        const sum = data.reduce((count, next) => {
          return count + Number(next[column.dataIndex]);
        }, 0);
        return <div style="text-align: center;">{sum}</div>;
      },
      address: '1111',
    },
    {
      name: {
        content: 'Summary',
        colSpan: 2,
      },
      age: ({ data, column }) => {
        const sum = data.reduce((count, next) => {
          return count + Number(next[column.dataIndex]);
        }, 0);
        return <div style="text-align: center;">{sum}</div>;
      },
    },
  ];
  const dataSource = reactive(
    new Array(4).fill(0).map((_, i) => ({
      key: i,
      name: `胡彦斌${i}`,
      name2: `胡彦斌${i}`,
      age: 32,
      address: '西湖区湖底公园1号',
      address2: '西湖区湖底公园1号',
      children: new Array(3).fill(0).map((_, index) => ({
        key: `${i}-${index}`,
        name: '胡彦斌',
        age: 32,
        address: '西湖区湖底公园2号',
      })),
    })),
  );

  const [register] = useTable({
    columns: tableColumn,
    scroll: { y: 500 },
    dataSource: dataSource,
    pagination: {
      total: 100,
      current: 5,
      simple: true,
    },
    formConfig: {
      schemas: [
        {
          field: 'filter',
          label: '关键字',
          bordered: false,
          component: 'Input',
        },
        {
          field: 'aaa',
          label: '选择',
          bordered: false,
          component: 'Input',
          render: ({ model }) => (
            <Select
              v-model:value={model.aaa}
              mode="multiple"
              placeholder="Please select"
              options={[...Array(25)].map((_, i) => ({ value: (i + 10).toString(36) + (i + 1) }))}
            ></Select>
          ),
        },
        {
          field: 'ccc',
          label: '选择ccc',
          bordered: false,
          component: 'Input',
          render: ({ model }) => (
            <Select
              v-model:value={model.ccc}
              placeholder="Please select"
              options={[...Array(25)].map((_, i) => ({ value: (i + 10).toString(36) + (i + 1) }))}
            ></Select>
          ),
        },
      ],
    },
    defaultShowSelect: true,
    draggable: false, //列表是否允许拖拽
    isTreeTable: true,
    showIndexColumn: true,
    showTableSetting: true,
    indexColumnProps: {
      width: '100px',
    },
    rowKey: 'key',
    rowSelection: {
      checkStrictly: false,
      onSelect: (record: Record<string, any>, selected: boolean, selectedRows: Record<string, any>[]) => {
        // console.log(record, selected, selectedRows);
      },
      onSelectAll: (selected: boolean, selectedRows: Record<string, any>[], changeRows: Record<string, any>[]) => {
        // console.log(selected, selectedRows, changeRows);
      },
    },
    summary: SummaryData,
  });

  function dragEnd(val) {
    // console.log('sx,val')
  }
</script>
<style lang="less" scoped></style>
