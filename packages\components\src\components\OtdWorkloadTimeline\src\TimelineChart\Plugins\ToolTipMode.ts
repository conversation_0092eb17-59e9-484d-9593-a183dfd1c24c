import { Tooltip } from 'ant-design-vue';
import { createVNode, render } from 'vue';

export type ToolTipModeConfigType = {
  rootContainer: HTMLElement;
  mousemoveDom: HTMLElement;
  tooltipId: string;
};
export class ToolTipMode {
  private config: ToolTipModeConfigType;
  tooltip!: HTMLDivElement;
  constructor(config: ToolTipModeConfigType) {
    this.config = config;
    this.init();
  }
  // 初始化
  private init() {
    this.addTooltip();
  }
  // 添加悬浮提示组件
  private addTooltip() {
    const { tooltipId, rootContainer, mousemoveDom } = this.config;
    const container = document.createElement('div');
    container.style.setProperty('display', 'none');
    const config = createVNode(Tooltip, {
      overlayClassName: tooltipId,
      open: true,
      getPopupContainer: () => rootContainer,
    });
    mousemoveDom.parentElement?.append(container);
    requestAnimationFrame(() => {
      this.tooltip = rootContainer.querySelector(`.${tooltipId}`) as HTMLDivElement;
      const { tooltip } = this;
      tooltip?.classList.add('ant-tooltip-placement-top');
      if (tooltip) {
        tooltip.style.visibility = 'hidden';
        // tooltip.style.position = 'absolute';
        tooltip.style.top = '0';
        tooltip.style.left = '0';
      }
    });
    render(config, container);
  }
  // 设置悬浮提示内容
  setToolTip(innerHTML, coordinate: (content: Element) => { x: number; y: number }) {
    const { tooltip } = this;
    if (tooltip) {
      const content = tooltip.querySelector('.ant-tooltip-inner');
      if (content) {
        content.innerHTML = innerHTML;
        const { x, y } = coordinate(content);
        tooltip.style.top = y + 'px';
        tooltip.style.left = x + 'px';
        tooltip.style.visibility = 'unset';
      }
    }
  }
  // 隐藏
  hidden() {
    this.tooltip.style.visibility = 'hidden';
  }
}
