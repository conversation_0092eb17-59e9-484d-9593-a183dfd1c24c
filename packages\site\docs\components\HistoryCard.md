## 历史记录卡片

<demo src="../demo/HistoryCard/basic.vue" title="历史记录卡片"></demo>

## 属性

| 参数          | 说明         | 类型                                               | 可选值 | 默认值   | 版本 |
| ------------- | ------------ | -------------------------------------------------- | ------ | -------- | ---- |
| data          | 数据列表     | array                                              | --     | --       | 1.0  |
| fields        | 取值的字段   | object                                             | --     | 参考下面 | 1.0  |
| height        | 滚动高度     | string                                             | --     | 100%     | 1.0  |
| loadMore      | 加载更多     | () => Promise<{items:object[],totalClount:number}> | --     | --       | 1.0  |
| immediateLoad | 第一次就请求 | boolean                                            | --     | `true`   | 1.0  |

```
{
  creatorId:'creatorId',
  creatorName: 'creatorName',
  content: 'content',
  creationTime: 'creationTime',
  type: 'type',
  prevValue: 'prevValue',
  newValue: 'newValue',
  actionType: 'actionType',
  relatedId: 'relatedId',
  relatedTitle: 'relatedTitle',
}
```
