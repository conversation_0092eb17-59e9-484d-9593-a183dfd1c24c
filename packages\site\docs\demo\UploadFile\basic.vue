<template>
  <OtdUploadFile
    v-model:file-list="attachmentList"
    :uploadFile="uploadFile"
    :remove-file="handleRemoveAttrachment"
    :preview-file="() => Promise.resolve(111)"
  />
</template>
<script lang="ts" setup>
  import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';
  import type { UploadFile } from 'ant-design-vue';
  import { OtdUploadFile, OtdUploadRequestOption, useFileFormat, UploadFileType } from '@otd/otd-ui';
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';

  const attachmentList = ref<UploadFileType[]>(
    new Array(100).fill(0).map(() => ({
      uid: 'vc-upload-1717723613845-7',
      name: '1-200514232F3.xls',
      size: 15360,
      type: 'application/vnd.ms-excel',
      creationTime: '2024-06-07T01:28:01.069Z',
      creatorName: 'limin',
    })),
  );
  const { convertAttachmentFormat } = useFileFormat();

  function uploadFile(data: OtdUploadRequestOption) {
    let percent = 0;
    let timeInterval;
    return new Promise((resolve, reject) => {
      data.onCancel = () => {
        clearInterval(timeInterval);
        reject(data);
      };
      timeInterval = setInterval(() => {
        data.onProgress!({
          percent: (percent += 1),
        });
        if (percent >= 100) {
          resolve(data);
          message.success('操作成功');
          clearInterval(timeInterval);
        }
      }, 100);
    }).then((file) => {
      return handleUpdateAttrachment(file as UploadRequestOption);
    });
  }

  function handleUpdateAttrachment(data: UploadRequestOption) {
    return new Promise((resolve) => {
      resolve(data.file);
    }).then((res) => {
      return { file: { ...convertAttachmentFormat(data.file), creationTime: new Date() }, res };
    });
  }

  function handleRemoveAttrachment(file: UploadFile) {
    return new Promise((resolve) => {
      message.success('操作成功');
      resolve(file);
    });
  }
</script>
<style lang="less" scoped></style>
