# Layout 框架布局

<p style="font-size:26px">代码演示</p>

## 基础 Layout

<demo src="../demo/Layout/basic.vue" title="基础 Layout"></demo>

## 属性

| 参数         | 说明                     | 类型                          | 可选值 | 默认值  | 版本 |
| ------------ | ------------------------ | ----------------------------- | ------ | ------- | ---- |
| logoUrl      | logo 图片                | String                        |        |         | 1.0  |
| hiddenLayout | 隐藏框架，只显示内容区域 | Boolean                       |        | `false` | 1.0  |
| selectedKeys | 选中的菜单               | String[] , Number[]           |        |         | 1.0  |
| menuItems    | 菜单内容                 | ItemType[]                    |        |         | 1.0  |
| userInfo     | 用户信息                 | Object [类型](#userinfo-类型) |        |         | 1.0  |

## UserInfo 类型

| 参数            | 说明       | 类型     | 可选值 | 默认值 | 版本 |
| --------------- | ---------- | -------- | ------ | ------ | ---- |
| avatar          | 头像       | String   |        |        | 1.0  |
| email           | 邮箱       | String   |        |        | 1.0  |
| token           | 登录 token | String   |        |        | 1.0  |
| realName        | 真是名称   | String   |        |        | 1.0  |
| roles           | 角色       | String[] |        |        | 1.0  |
| tenantInfo      | 组织信息   | Object   |        |        | 1.0  |
| tenantInfo.id   | 组织 ID    | String   |        |        | 1.0  |
| tenantInfo.name | 组织名称   | String   |        |        | 1.0  |
| userId          | 用户 ID    | String   |        |        | 1.0  |
| username        | 用户名     | String   |        |        | 1.0  |
