<template>
  loading:<Switch v-model:checked="loading" />
  <OtdEditCell :loading="loading">
    <OtdSubtask
      v-model:value="dataList"
      :update-subtask="handleChange"
      :detail="detail"
      :generate-subtask="generateSubTaskTitles"
      :isShowClosed="isShowClosed"
      @getIsShowClosed="getIsShowClosed"
      @sync="handleSync"
      @delete="handleRemove"
      @to-detail="handleDetail"
      @confirm-ai="addBatchSubtask"
    >
      <template #action>1111</template>
    </OtdSubtask>
  </OtdEditCell>
  <OtdSubtask
    v-model:value="dataList2"
    :update-subtask="handleChange"
    :detail="detail"
    :generate-subtask="generateSubTaskTitles"
    @sync="handleSync"
    @delete="handleRemove"
    @to-detail="handleDetail"
    @confirm-ai="addBatchSubtask"
  >
    <template #action>1111</template>
  </OtdSubtask>
</template>
<script lang="tsx" setup>
  import { OtdEditCell, OtdSubtask, ISetSubtaskInputDetail, Switch } from '@otd/otd-ui';
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';
  const loading = ref(true);
  const isShowClosed = ref(false); //初始值
  const detail = ref({
    title: '任务标题',
    description: '任务描述',
  });
  const dataList2 = ref();
  const dataList = ref<ISetSubtaskInputDetail[]>([
    {
      id: '1',
      title: '名称111',
      responsibleUser: {
        label: '阿达',
        value: 1,
        img: 'https://th.bing.com/th?id=OIP.MLH6YlnCVSWscmMC6N0CVgAAAA&w=250&h=250&c=8&rs=1&qlt=90&o=6&pid=3.1&rm=2',
      },
      date: null,
      status: 60,
    },
    ...new Array(3).fill(0).map((_, index) => ({
      id: '2' + index,
      title: '名称222',
      responsibleUser: {
        label: '阿星',
        value: 2,
        img: 'https://th.bing.com/th?id=OIP.MLH6YlnCVSWscmMC6N0CVgAAAA&w=250&h=250&c=8&rs=1&qlt=90&o=6&pid=3.1&rm=2',
      },
      date: null,
      status: 30,
    })),
  ]);

  const handleChange = (record: ISetSubtaskInputDetail, key: string) => {
    console.log('子任务改变', record, key);
    record.id = Date.now().toString();
    message.success('操作成功');
    return Promise.resolve();
  };
  function addBatchSubtask(list) {
    console.log(list);
  }
  function handleSync(row) {
    message.success('同步截止时间');
    console.log('同步截止时间', row);
  }
  function generateSubTaskTitles() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(['任务1', '任务2', '任务3', '任务4'].map((title) => ({ title })));
      }, 300);
    });
  }
  function getIsShowClosed(val) {
    console.log(val);
  }
  function handleDetail() {
    message.success('去详情');
  }

  const handleRemove = ({ data, index }) => {
    console.log(index);
    console.log(data);
    dataList.value.splice(data.dataIndex, 1);
    console.log(dataList.value);

    message.success('操作成功');
  };
</script>
<style lang="less" scoped></style>
