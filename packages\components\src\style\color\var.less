html {
  --otd-primary-color: #003de8;
  --otd-disabled-color: #d4d4d4;
  --otd-gray3-color: #828282;
  --otd-gray4-color: #bdbdbd;
  --otd-gray7-color: #f8f8f8;
  --otd-help-color: #9e9e9e;
  --otd-border-color: #f3f3f3;
  --otd-border-table: #f5f6fa;
  --otd-border-primary: #177cfd;
  --otd-border-gray: #e0e0e0;
  --otd-primary-text: #003de8;
  --otd-header-text: #6a6a6a;
  --otd-basic-text: #333;
  --otd-menu-text: #177cfd;
  --otd-icon-text: #4f4f4f;
  --otd-body-text: #fff;
  --otd-disabled-bg: #e0e0e0;
  --otd-basic-bg: #fff;
  --otd-header-bg: #fcfcfc;
  --otd-panel-bg: #fcfcfc;
  --otd-content-bg: #f5f6fa;
  --otd-scroll-bg: #9e9e9e;
  --otd-icon-bg: #f3f3f3;
  --otd-expand-bg: #f9fafd;
  --otd-basic-active: #eef1ff;
  --otd-gray-hover: #f8f8f8;
  --otd-gray3-hover: #f3f3f3;
  --otd-dropdown-bg: #fff;
  --otd-dropdown-active-base: #dbdee7;
  --otd-dropdown-border: #e0e0e0;
  --otd-dropdown-scroll-thumb: #c7c9cc;
  --otd-dropdown-scroll-hover: #cccdcf;
  --otd-dropdown-btn-shadow: rgba(0, 0, 0, 0.08);
  --otd-dropdown-menu-item-disabled: rgba(0, 0, 0, 0.3);
  --otd-dropdown-hover-base: #f2f2f2;
  --otd-box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.08);
  --otd-popover-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 0 6px -4px rgba(0, 0, 0, 0.12),
    0 0 4px 4px rgba(0, 0, 0, 0.05);
  --otd-gradient-bg: linear-gradient(179deg, #5539ff 0%, rgba(116, 0, 232, 0.82) 43%, rgba(213, 0, 232, 0.82) 99%);
  // 黑夜模式变量
  &[data-theme='dark'],
  &.dark {
    --otd-gradient-bg: linear-gradient(179deg, #b266ff 0%, #c727ff 43%, rgba(200, 0, 217, 0.82) 99%);
    --otd-basic-active: #212130;
    // text
    --otd-basic-bg: #212130;
    --otd-header-bg: #212130;
    --otd-panel-bg: #fcfcfc;
    --otd-body-text: #2e2e42;
    --otd-content-bg: #171622;
    --otd-scroll-bg: #454565;
    --otd-icon-bg: #2e2e42;
    --otd-expand-bg: #2e2e42;
    --otd-disabled-bg: #2e2e42;
    --otd-icon-text: #bdbdbd;
    --otd-basic-text: #fff;
    --otd-header-text: #bdbdbd;
    --otd-menu-text: #6993ba;
    --otd-primary-text: #5c83d5;
    --otd-primary-color: #fff;
    --otd-disabled-color: #000;
    --otd-gray3-color: #fff;
    --otd-gray4-color: #fff;
    --otd-help-color: #fff;
    --otd-border-color: #2f2f44;
    --otd-border-primary: #003de8;
    --otd-border-gray: #2f2f44;
    --otd-border-table: #2f2f44;
    --otd-gray-hover: #2f2f44;
    --otd-gray3-hover: #2e2e42;
    --otd-gray7-color: #313131;
    --otd-dropdown-hover-base: #212130;
    --otd-dropdown-active-base: #212130;
    --otd-dropdown-bg: #2e2e42;
    --otd-dropdown-border: #212130;
    --otd-dropdown-menu-item-disabled: rgba(255, 255, 255, 0.25);
    --otd-box-shadow: 0 0 4px 0 rgba(255, 255, 255, 0.08);
    --otd-popover-shadow: 0 6px 16px 0 rgba(255, 255, 255, 0.08), 0 0 6px -4px rgba(255, 255, 255, 0.12),
      0 0 4px 4px rgba(255, 255, 255, 0.05);
  }
}

// 其他变量
:root {
  /*start 状态*/
  --otd-status-UnStarted-bg: rgba(158, 158, 158, 0.17); //未开始
  --otd-status-UnStarted-color: #676767;
  --otd-status-OnGoing-bg: rgba(0, 133, 255, 0.17); //进行中
  --otd-status-OnGoing-color: #0038ff;
  --otd-status-Stopped-bg: rgba(255, 168, 0, 0.17); //待确认
  --otd-status-Stopped-color: #ff7a00;
  --otd-status-Reject-bg: rgba(255, 70, 70, 0.17); //已退回
  --otd-status-Reject-color: #ec1010;
  --otd-status-Close-bg: rgba(0, 163, 137, 0.1); //完成
  --otd-status-Close-color: #008872;
  --otd-status-Done-bg: rgba(202, 0, 181, 0.1); //暂停
  --otd-status-Done-color: #c000ad;
  --otd-status-Cancelled-bg: rgba(158, 158, 158, 0.17); //已取消
  --otd-status-Cancelled-color: #676767;
  /*end 状态*/
  --otd-border-radius: 8px;
  --otd-default-radius: 6px;
  --otd-small-radius: 4px;
  --otd-mini-radius: 2px;
  --otd-middle-radius: 16px;
  --otd-large-radius: 999px;
  --otd-circle-radius: 50%;
  --otd-basic-line: 22px;
  --otd-basic-size: 16px;
  --otd-white-text: #fff;
  --otd-black-bg: #000;
  --otd-primary-main: #003de8;
  --otd-error-color: #e34d59;
  --otd-warning-color: #ed7b2f;
  --otd-success-color: #27ae60;
  --otd-lock-z-index: 3000;
  --otd-dropdown-search-base: #333333;
}
