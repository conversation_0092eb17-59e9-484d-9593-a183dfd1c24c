import type { Fetch<PERSON>ara<PERSON>, TableColumnEmitType, TableColumnPropsType, TableInstancePropsType } from '../types';
import type { Recordable } from '/#/global';
import { merge } from 'lodash-es';
import { ComponentInternalInstance, computed, ComputedRef, getCurrentInstance, reactive, ref, unref } from 'vue';
import { DEFAULT_FILTER_FN, DEFAULT_SORT_FN, ROW_KEY } from '../const';
import { buildUUID } from '/@/utils/uuid';

interface SearchState {
  sortInfo: Recordable;
  filterInfo: Record<string, string[]>;
}

export function useDataSource(propsRef: ComputedRef<TableInstancePropsType>, { getPaginationInfo, setPagination }) {
  const { emit, proxy } = getCurrentInstance() as ComponentInternalInstance & {
    proxy: TableColumnPropsType;
    emit: TableColumnEmitType;
  };
  const tableLoading = ref<boolean>(false);
  const tableRequestData = ref<Recordable[] | undefined>(undefined);
  const searchState = reactive<SearchState>({
    sortInfo: {},
    filterInfo: {},
  });

  const getAutoCreateKey = computed(() => {
    return unref(propsRef).autoCreateKey && !unref(propsRef).rowKey;
  });

  const getRowKey = computed(() => {
    const { rowKey } = unref(propsRef);
    return unref(getAutoCreateKey) ? ROW_KEY : rowKey;
  });

  const tableDataSource = computed({
    get: () => tableRequestData.value ?? unref(propsRef).dataSource,
    set: (value) => {
      if (tableRequestData.value) {
        tableRequestData.value = value;
      } else {
        unref(propsRef).dataSource.length = 0;
        unref(propsRef).dataSource.push(...value);
      }
    },
  });
  // const tableDataSource = computed(() => {
  //   return tableRequestData.value ?? unref(propsRef).dataSource;
  // });

  function setLoading(load = false) {
    tableLoading.value = load;
  }
  function getLoading() {
    return tableLoading.value;
  }

  function setTableKey(items: any[]) {
    if (!items || !Array.isArray(items)) return;
    items.forEach((item) => {
      if (!item[ROW_KEY]) {
        item[ROW_KEY] = buildUUID();
      }
      if (item.children && item.children.length) {
        setTableKey(item.children);
      }
    });
  }

  const getDataSourceRef = computed(() => {
    const dataSource = unref(tableDataSource);
    if (!dataSource || dataSource.length === 0) {
      return unref(tableDataSource);
    }
    if (unref(getAutoCreateKey)) {
      const firstItem = dataSource[0];
      const lastItem = dataSource[dataSource.length - 1];

      if (firstItem && lastItem) {
        if (!firstItem[ROW_KEY] || !lastItem[ROW_KEY]) {
          const data = unref(tableDataSource);
          data?.forEach((item) => {
            if (!item[ROW_KEY]) {
              item[ROW_KEY] = buildUUID();
            }
            if (item.children && item.children.length) {
              setTableKey(item.children);
            }
          });
          tableDataSource.value = data;
        }
      }
    }
    return unref(tableDataSource);
  });

  // 发起远程请求
  function makeRemoteRequest(info: Recordable = {}) {
    if (getLoading()) return Promise.resolve();
    const { current, pageSize, setPagination } = unref(getPaginationInfo);
    const { tabFilterRef } = proxy.$refs as any;
    let tabFilterForm = {};
    if (tabFilterRef) {
      const { getFieldsValue } = tabFilterRef;
      tabFilterForm = getFieldsValue();
    }
    let pageParams: Recordable = { pageIndex: info?.page ?? current, pageSize };
    const { sortInfo = {}, filterInfo } = searchState;
    const params = merge(
      pageParams,
      tabFilterForm,
      info?.searchInfo,
      sortInfo,
      info?.sortInfo,
      filterInfo,
      info?.filterInfo,
    );
    setPagination?.({
      current: info?.page || current,
    });

    const { remoteRequest } = unref(propsRef);
    if (remoteRequest) {
      setLoading(true);
      return remoteRequest(params)
        .then((data) => {
          if (!data) return;
          const { items = [], totalCount = 0 } = data;
          tableRequestData.value = items;
          setPagination?.({ total: totalCount });
          return data;
        })
        .finally(() => {
          setLoading();
        });
    }
    return Promise.resolve();
  }

  // 监听table的change事件
  function handleTableChange(...arg) {
    const { setPagination } = unref(getPaginationInfo);
    emit('change', ...arg);
    const [pagination, filters, sorter] = arg;
    setPagination(pagination);
    const params: Recordable = {};
    if (sorter) {
      const sortInfo = DEFAULT_SORT_FN(sorter);
      searchState.sortInfo = sortInfo;
      params.sortInfo = sortInfo;
    }

    if (filters) {
      const filterInfo = DEFAULT_FILTER_FN(filters);
      searchState.filterInfo = filterInfo as any;
      params.filterInfo = filterInfo;
    }
    makeRemoteRequest(params);
  }

  // 重新加载
  function reload(info: FetchParams = {}) {
    return makeRemoteRequest(info);
  }

  function setTableData<T = Recordable>(values: T[]) {
    tableRequestData.value = values as any;
  }

  function getDataSource<T = Recordable>() {
    return tableDataSource.value as T[];
  }

  function findTableDataRecord(rowKey: string | number) {
    if (!tableDataSource.value || tableDataSource.value.length == 0) return;

    const rowKeyName = unref(getRowKey);
    if (!rowKeyName) return;

    const { childrenColumnName = 'children' } = unref(propsRef);

    const findRow = (array: any[]) => {
      let ret;
      array.some(function iter(r) {
        if (typeof rowKeyName === 'function') {
          if ((rowKeyName(r) as string) === rowKey) {
            ret = r;
            return true;
          }
        } else {
          if (Reflect.has(r, rowKeyName) && r[rowKeyName] === rowKey) {
            ret = r;
            return true;
          }
        }
        return r[childrenColumnName] && r[childrenColumnName].some(iter);
      });
      return ret;
    };

    // const row = dataSourceRef.value.find(r => {
    //   if (typeof rowKeyName === 'function') {
    //     return (rowKeyName(r) as string) === rowKey
    //   } else {
    //     return Reflect.has(r, rowKeyName) && r[rowKeyName] === rowKey
    //   }
    // })
    return findRow(tableDataSource.value);
  }

  function updateTableDataRecord(rowKey: string | number, record: Recordable): Recordable | undefined {
    const row = findTableDataRecord(rowKey);

    if (row) {
      for (const field in row) {
        if (Reflect.has(record, field)) row[field] = record[field];
      }
      return row;
    }
  }
  function insertTableDataRecord(record: Recordable, index: number): Recordable | undefined {
    // if (!dataSourceRef.value || dataSourceRef.value.length == 0) return;
    index = index ?? tableDataSource.value?.length;
    unref(tableDataSource)?.splice(index, 0, record);
    return unref(tableDataSource);
  }

  function deleteTableDataRecord(rowKey: string | number | string[] | number[]) {
    if (!tableDataSource.value || tableDataSource.value.length == 0) return;
    const rowKeyName = unref(getRowKey);
    if (!rowKeyName) return;
    const rowKeys = !Array.isArray(rowKey) ? [rowKey] : rowKey;
    for (const key of rowKeys) {
      let index: number | undefined = tableDataSource.value.findIndex((row) => {
        let targetKeyName: string;
        if (typeof rowKeyName === 'function') {
          targetKeyName = rowKeyName(row) as string;
        } else {
          targetKeyName = rowKeyName as string;
        }
        return row[targetKeyName] === key;
      });
      if (index >= 0) {
        tableDataSource.value.splice(index, 1);
      }
      index = unref(propsRef).dataSource?.findIndex((row) => {
        let targetKeyName: string;
        if (typeof rowKeyName === 'function') {
          targetKeyName = rowKeyName(row) as string;
        } else {
          targetKeyName = rowKeyName as string;
        }
        return row[targetKeyName] === key;
      });
      if (typeof index !== 'undefined' && index !== -1) unref(propsRef).dataSource?.splice(index, 1);
    }
    setPagination({
      total: unref(propsRef).dataSource?.length,
    });
  }

  return {
    tableLoading,
    tableRequestData,
    tableDataSource,
    getDataSourceRef,
    setLoading,
    getLoading,
    makeRemoteRequest,
    handleTableChange,
    reload,
    getDataSource,
    setTableData,
    findTableDataRecord,
    updateTableDataRecord,
    insertTableDataRecord,
    deleteTableDataRecord,
  };
}
