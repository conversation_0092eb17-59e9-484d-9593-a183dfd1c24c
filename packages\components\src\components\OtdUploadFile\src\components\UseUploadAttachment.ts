import type { FileType } from 'ant-design-vue/es/upload/interface';
import type { RcFile, UploadProgressEvent } from 'ant-design-vue/es/vc-upload/interface';
import type { UploadFile } from 'ant-design-vue';
import type {
  UploadFilePropsType,
  UploadFileEmitType,
  OtdUploadRequestOption,
} from '/@/components/OtdUploadFile/src/type';
import { getCurrentInstance, reactive } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { useI18n } from '/@/hooks/web/useI18n';
import { isFunction } from 'lodash-es';
import { useFileFormat } from '../useUploadFile';

const uploadReuqestQueue = new Map<string, OtdUploadRequestOption>();
export function useUploadAttachment() {
  const { convertAttachmentFormat } = useFileFormat();
  const { emit, props } = getCurrentInstance() as unknown as {
    props: UploadFilePropsType;
    emit: UploadFileEmitType;
  };
  const { createConfirm } = useMessage();
  const { t } = useI18n();

  // 上传前判断
  function handleBeforeUpload(file): Promise<FileType> {
    return new Promise((resolve) => {
      resolve(file);
    });
  }

  // 文件进度条
  function getProcess(data: OtdUploadRequestOption, record) {
    const { file, onProgress } = data;
    uploadReuqestQueue.set((file as RcFile).uid, data);
    return function (params: UploadProgressEvent) {
      record.percent = params.percent;
      onProgress!(params);
    };
  }

  // 上传文件
  function handleUpload(data: OtdUploadRequestOption) {
    if (data) {
      if (props.uploadFile && isFunction(props.uploadFile)) {
        const { file, onSuccess, onError } = data;
        const record = reactive(convertAttachmentFormat(file));
        const reuqest = props.uploadFile(data);
        data.onProgress = getProcess(data, record);
        props.fileList.unshift(record);
        reuqest
          .then(({ file }) => {
            onSuccess?.(Object.assign(record, file));
          })
          .catch((err) => {
            // 调用实例的失败方法通知组件该文件上传失败
            onError?.(err);
          });
      }
    }
  }

  // 更新附件列表
  function updateFileList(list) {
    emit('change', list);
  }

  // 上传成功回调
  function handleUploadSuccess(record) {
    uploadReuqestQueue.delete(record.uid);
    updateFileList(props.fileList);
  }

  // 移除文件
  function handleRemoveFile(file): Promise<UploadFile[] | []> {
    return new Promise((resolve, reject) => {
      createConfirm({
        title: t('common.removeAttachmentTip', { name: file.name }),
        iconType: 'warning',
        class: 'otd-dialog',
        onOk: () => {
          const list = props.fileList.filter((item) => item.uid !== file.uid);
          if (props.removeFile && isFunction(props.removeFile)) {
            props.removeFile(file, list).then(() => resolve(list));
          } else {
            resolve(list);
          }
        },
        onCancel: () => reject(false),
      });
    });
  }

  // 取消上传
  function cancelUpload(record, index: number) {
    const data = uploadReuqestQueue.get(record.uid);
    if (data && data.onCancel) {
      data.onCancel();
      props.fileList.splice(index, 1);
      uploadReuqestQueue.delete(record.uid);
    } else {
      throw new Error("Cancel upload error, 'onCancel' in undefined");
    }
  }

  return {
    handleBeforeUpload,
    handleUpload,
    handleUploadSuccess,
    handleRemoveFile,
    cancelUpload,
    uploadReuqestQueue,
  };
}
