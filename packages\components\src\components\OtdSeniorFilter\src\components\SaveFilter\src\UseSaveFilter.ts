import type {
  FilterListItemType,
  FilerListType,
  AddFilterItem,
  SeniorFilterEmitType,
  SeniorFilterPropsType,
} from '../../../type';
import { getCurrentInstance, reactive, ref } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { useMessage } from '/@/hooks/web/useMessage';
import { FormInstance, message } from 'ant-design-vue';
import { ERROR_COLOR } from '/@/setting';
import { MoreActionItem } from '/@/components/OtdMoreAction';

export function useSaveFilter() {
  const { props, emit } = getCurrentInstance() as unknown as {
    emit: SeniorFilterEmitType;
    props: SeniorFilterPropsType;
  };
  const { t } = useI18n();
  const { createConfirm } = useMessage();
  class formData {
    name? = '';
  }
  const isEdit = ref(false);
  const filterItem = ref({});
  const editDate = ref<string>('');
  const itemId = ref<string>('');
  const loading = ref(false);
  const saveFilterModal = ref(false);
  const formRef = ref<FormInstance>();
  const saveFilter = ref<string | undefined>(undefined);
  const SelectOpen = ref(false);
  const FilterFormData = reactive({ ...new formData() });
  const FilterList = ref<FilterListItemType[]>([]);
  const allList = ref<FilterListItemType[]>([]);
  const time = ref<any>(null);
  const editIndex = ref<number>(-1);

  // 数据重置
  function handleFilterModalCancel(): any {
    formRef.value?.resetFields();
    Object.assign(FilterFormData, { ...new formData() });
  }

  // 打开保存筛选结果弹窗
  function handleOpenFilterModal() {
    // emit('update:value', false);
    saveFilterModal.value = true;
    isEdit.value = true;
  }

  function init() {
    loading.value = true;
    props.getFilterList().then((res: FilerListType) => {
      loading.value = false;
      FilterList.value = res.items;
      allList.value = res.items;
    });
  }

  function handleSelectSelect() {
    SelectOpen.value = !SelectOpen.value;
    if (SelectOpen.value) {
      init();
    }
  }

  // 筛选列表item点击
  function handleSelectItemClick(item: FilterListItemType) {
    emit('item-click', item);
    saveFilter.value = item.name;
    SelectOpen.value = false;
  }

  // 保存
  function handleSaveFilter() {
    formRef.value?.validate().then(() => {
      const filterData = isEdit.value ? JSON.stringify(props.value) : editDate.value;
      const saveData: AddFilterItem = {
        name: FilterFormData.name,
        text: filterData as string,
      };
      if (!isEdit.value) {
        props.updateFilterItem({ item: filterItem.value, saveData, index: editIndex.value }).then(() => {
          saveFilterModal.value = false;
          message.success(t('common.operationSuccess'));
        });
        return;
      } else {
        props.saveFilter(saveData).then(() => {
          saveFilterModal.value = false;
          message.success(t('common.operationSuccess'));
        });
      }
    });
  }

  //筛选列表删除
  function handleDeleteFilter(item: FilterListItemType, index: number) {
    // emit('update:value', false);
    let msg = t('common.askDelete');
    createConfirm({
      iconType: 'warning',
      title: t('common.tip'),
      content: msg,
      onOk: async () => {
        props
          .removeFilterItem({ item, index })
          .then(() => {
            message.success(t('common.operationSuccess'));
            init();
            setTimeout(() => {
              if (allList.value.findIndex((i) => i.name === saveFilter.value) === -1) {
                saveFilter.value = undefined;
              }
            }, 100);
          })
          .catch((err) => {
            console.log(err);
          });
      },
    });
  }

  // 处理搜索
  function handleSearch(val: string) {
    clearTimeout(time.value);
    time.value = setTimeout(() => {
      FilterList.value = allList.value.filter((item) => {
        const regex = new RegExp(val, 'i');
        return regex.test(item.name as string);
      });
    }, 500);
  }

  // 编辑
  function handleEditFilter(item: FilterListItemType, index: number) {
    // emit('update:value', false);
    saveFilterModal.value = true;
    isEdit.value = false;
    filterItem.value = item;
    editDate.value = item.text as string;
    FilterFormData.name = item.name;
    editIndex.value = index;
    itemId.value = item.id as string;
  }

  const Actions: MoreActionItem[] = [
    // 编辑
    {
      id: 0,
      icon: 'otd-icon-a-cateditsize24',
      name: t('common.editText'),
      action: ({ itemFilter, index }) => {
        handleEditFilter(itemFilter, index);
      },
    },
    // 删除
    {
      id: 1,
      icon: 'otd-icon-a-catdeletesize24',
      name: t('common.delText'),
      color: ERROR_COLOR,
      action: ({ itemFilter, index }) => {
        handleDeleteFilter(itemFilter, index);
      },
    },
  ];
  return {
    handleFilterModalCancel,
    handleOpenFilterModal,
    init,
    handleSelectSelect,
    handleSelectItemClick,
    handleSaveFilter,
    handleSearch,
    SelectOpen,
    Actions,
    saveFilter,
    saveFilterModal,
    FilterFormData,
    loading,
    FilterList,
    isEdit,
    formRef,
  };
}
