import { ComponentPublicInstance, computed, getCurrentInstance, nextTick, reactive, ref } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';

import { useI18n } from '/@/hooks/web/useI18n';
import { ActionItem } from '/@/utils/types';
import { ERROR_COLOR } from '/@/setting';
import { BoardEmitsType, BoardPropsType } from './type';
export function useBoard() {
  const { t } = useI18n();
  const { createConfirm } = useMessage();
  const { emit, props, proxy } = getCurrentInstance() as unknown as {
    props: BoardPropsType;
    emit: BoardEmitsType;
    proxy: ComponentPublicInstance;
  };
  const { listLabel } = props;
  // 看板组-拖动任务
  const boardDrag = {
    name: 'board',
    put: true, //允许拖入
    pull: true,
  };

  // 分组操作
  const boardAction = ref<ActionItem[]>([
    // 新增任务
    {
      id: 'add',
      tip: props.hoverTip ?? t('common.create'),
      icon: 'otd-icon-add-2',
      isHide: (...data) => props.hideActionItem?.(...data) ?? false,
      action: (data) => handleCreateTask(data, 'unshift'),
    },
    // 重命名分组
    {
      id: 'rename',
      name: t('common.tag.rename'),
      icon: 'otd-icon-a-cateditsize24',
      isHide: (...data) => props.hideActionItem?.(...data) ?? false,
      action: (data) => {
        data.isEdit = true;
        nextTick(() => {
          const dom = document.querySelector('.drap-title-input');
          (dom as HTMLInputElement)?.focus();
        });
      },
    },
    // 删除分组
    {
      id: 'delete',
      color: ERROR_COLOR,
      name: t('common.delText'),
      icon: 'otd-icon-a-catdeletesize24',
      isHide: (...data) => props.hideActionItem?.(...data) ?? false,
      action: (data, _taskList, index: number) => {
        createConfirm({
          content: (props.deleteTip as string) || t('common.askDelete'),
          iconType: 'warning',
          class: 'otd-dialog w-450px',
          onOk: () => {
            emit('deleteGroup', data as string, index);
          },
        });
      },
    },
  ]);
  // 分组创建对象
  const groupObject = reactive<any>({ name: '', [listLabel]: [], isAdd: false });

  const boardGroupList = computed({
    get: () => props.list ?? [],
    set: (value) => emit('update:list', value),
  });
  // 处理创建任务
  function handleCreateTask(data, action: 'unshift' | 'push' = 'push') {
    if (!data.items) data[listLabel] = [];
    data[listLabel][action]({ title: '', isAdd: true, responsibleGroup: data });
  }
  // 拖拽分组结束的事件
  function handleMoveTaskGroup(data) {
    const { newIndex, oldIndex } = data;
    if (newIndex === oldIndex) return false;
    emit('moveGroup', data);
  }
  // 拖拽任务结束的事件
  function handleMoveTaskCard(data) {
    if (data.to.dataset.index === data.from.dataset.index) return false;
    const group = boardGroupList.value[data.to.dataset.index];
    const list = group[listLabel] ?? [];
    const prevTask = list[data.newDraggableIndex - 1];
    const record = list[data.newDraggableIndex];
    const afterTask = list[data.newDraggableIndex + 1];
    emit('moveCard', { record, group, prevTask, afterTask });
  }
  // 处理创建分组
  function handleCreateGroup() {
    groupObject.isAdd = true;
    nextTick(() => {
      const { groupRef } = proxy.$refs as any;
      groupRef.focus();
    });
  }

  // 处理分组保存
  function handleGroupSave(data) {
    if (data.groupId) {
      // 编辑分组
      emit('editGroup', data);
      data.isEdit = false;
    } else if (!data.id && groupObject.name) {
      // 创建分组
      emit('createGroup', groupObject.name);
    }
    groupObject.name = '';
    groupObject.isAdd = false;
  }

  return {
    t,
    handleMoveTaskGroup,
    boardGroupList,
    handleCreateTask,
    handleMoveTaskCard,
    groupObject,
    handleCreateGroup,
    handleGroupSave,
    boardAction,
    boardDrag,
  };
}
