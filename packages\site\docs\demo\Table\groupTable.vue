<template>
  <Button @click="setData()">reset</Button>&nbsp;
  <Button @click="setFilter">filter</Button>
  {{ expends }}
  <OtdGroupTable
    height="900px"
    group-column-name="groupData2"
    group-key="id"
    children-column-name="children2"
    row-key="id"
    :columns="tableColumn"
    :data-source="dataSource"
    :rowSelection="rowSelection"
    :expandedRowKeys="expends"
    :filter="filter"
    @resizeColumn="handleResizeColumn"
    @expand="handleExpand"
  >
    <template #group-header="{ group }">
      <span>负责人 ({{ group.title }})({{ group.groupData2.length }})</span>
    </template>
    <template #group-footer> 1111 </template>
    <template #expend-footer> 2222 </template>
  </OtdGroupTable>
</template>
<script lang="tsx" setup>
  import {
    OtdGroupTable,
    OtdEditCellItem,
    Input,
    OtdStatus,
    OtdPriority,
    OtdUserSearch,
    OtdTag,
    TableColumnPropsType,
    Button,
    TaskStatusEnum,
  } from '@otd/otd-ui';
  import { onMounted, reactive, ref } from 'vue';

  const expends = reactive([['0-0']]);

  const tableColumn: TableColumnPropsType[] = reactive([
    {
      title: '姓名',
      dataIndex: 'name',
      fixed: 'left',
      width: 600,
      minWidth: 200,
      isMain: true,
      resizable: true,
      headerRender: ({ column }) => <span style="margin-left:-16px">{column.title}</span>,
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <Input v-model:value={record.name} />
          </OtdEditCellItem>
        );
      },
    },
    {
      title: '姓名',
      dataIndex: 'status',
      // fixed: "left",
      // minWidth: 160,
      width: 160,
      resizable: true,
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem formItem={true}>
            <OtdStatus value={record.status} />
          </OtdEditCellItem>
        );
      },
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: 200,
      minWidth: 100,
      resizable: true,
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <OtdPriority />
          </OtdEditCellItem>
        );
      },
    },
    {
      title: '住址',
      // fixed: "right",
      dataIndex: 'address',
      width: 200,
      minWidth: 100,
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <OtdUserSearch />
          </OtdEditCellItem>
        );
      },
    },
    {
      title: '住址',
      fixed: 'right',
      dataIndex: 'address',
      width: 300,
      minWidth: 300,
      customRender: ({ record }) => {
        return (
          <OtdEditCellItem>
            <OtdTag options={new Array(100).fill(0).map((_, index) => ({ tagName: index.toString(), id: index }))} />
          </OtdEditCellItem>
        );
      },
    },
  ]);
  const dataSource = ref<any[]>([]);

  function setData(status = true) {
    if (status) {
      expends[0].push('0-0-1');
    }
    const data = new Array(13).fill(0).map((_, i) => ({
      id: i,
      title: '胡彦斌' + i,
      age: 32,
      address: '西湖区湖底公园1号',
      groupData2: new Array(parseInt((Math.random() * 50).toString())).fill(0).map((_, index) => ({
        id: `${i}-${index}`,
        name: '胡彦斌' + index,
        age: 32,
        address: '西湖区湖底公园2号',
        status: index % 2 === 0 ? TaskStatusEnum.Close : TaskStatusEnum.OnGoing,
        children2:
          index % 2 === 0
            ? new Array(2).fill(0).map((_, index2) => ({
                id: `${i}-${index}-${index2}`,
                name: '胡彦斌' + index2,
                age: 32,
                address: '西湖区湖底公园3号',
                status: index2 % 2 === 0 ? TaskStatusEnum.Close : TaskStatusEnum.OnGoing,
                children2: new Array(2).fill(0).map((_, index3) => ({
                  id: `${i}-${index}-${index2}-${index3}`,
                  name: '胡彦斌' + index3,
                  age: 32,
                  status: index3 % 2 === 0 ? TaskStatusEnum.Close : TaskStatusEnum.OnGoing,
                  address: '西湖区湖底公园4号',
                  children2: new Array(2).fill(0).map((_, index4) => ({
                    id: `${i}-${index}-${index2}-${index3}-${index4}`,
                    name: '胡彦斌' + index3,
                    age: 32,
                    status: index4 % 2 === 0 ? TaskStatusEnum.Close : TaskStatusEnum.OnGoing,
                    address: '西湖区湖底公园4号',
                    children2: new Array(2).fill(0).map((_, index5) => ({
                      id: `${i}-${index}-${index2}-${index3}-${index4}-${index5}`,
                      name: '胡彦斌' + index3,
                      age: 32,
                      status: index5 % 2 === 0 ? TaskStatusEnum.Close : TaskStatusEnum.OnGoing,
                      address: '西湖区湖底公园4号',
                    })),
                  })),
                })),
              }))
            : undefined,
      })),
    }));
    dataSource.value = data;
  }

  const defaultFilter = {
    status: {
      handler: (task) => ![TaskStatusEnum.Cancelled, TaskStatusEnum.Close].includes(task.status!),
    },
  };
  const filter = ref<Record<string, any>>();
  function setFilter() {
    filter.value = filter.value ? undefined : defaultFilter;
  }

  onMounted(() => {
    setTimeout(() => {
      setData(false);
    }, 1000);
  });

  function handleResizeColumn(data) {
    console.log(data);
  }
  function handleExpand(data) {
    console.log(data);
  }

  const rowSelection = ref({
    checkStrictly: false,
    onSelect: (record: Record<string, any>, selected: boolean, selectedRows: Record<string, any>[]) => {
      console.log(record, selected, selectedRows);
    },
    onSelectAll: (selected: boolean, selectedRows: Record<string, any>[], changeRows: Record<string, any>[]) => {
      console.log(selected, selectedRows, changeRows);
    },
    gap: 1,
  });
</script>
<style lang="less" scoped></style>
