<template>
  <div>禁用：<Switch v-model:checked="disabled"></Switch></div>
  <br />
  <OtdTrackTime
    :data="dataSource"
    :disabled="disabled"
    :load-detail="loadHourDetail"
    :category-options="categoryOptions"
    default-category="Default"
    @submit="handleSubmit"
    @delete="handleDelete"
    @reload="handleUpdate"
    @delete-detail="handleDeleteDetail"
  />
</template>
<script lang="ts" setup>
  import { ITaskHourDetailDto, OtdTrackTime, TrackTimeDataType, Switch } from '@otd/otd-ui';
  import { onMounted, reactive, ref } from 'vue';

  const dataSource = reactive<TrackTimeDataType>({
    planTime: {
      totalTime: 0,
      timeRecord: [],
    },
    actualTime: {
      totalTime: 0,
      timeRecord: [],
    },
  });
  const categoryOptions = [
    { label: '默认工时', value: 'Default' },
    { label: '出差工时', value: '2' },
  ];
  const disabled = ref(false);

  onMounted(() => {
    setTimeout(() => {
      Object.assign(dataSource, {
        planTime: {
          totalTime: 100,
          timeRecord: [
            {
              userAvatar: 'https://picsum.photos/200/300',
              userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              userName: 'limin',
              hourType: 0,
              workDate: undefined,
              // workDate: '2024-06-26T00:00:00',
              workHour: 2.8,
              workDay: 0.4,
              workTime: 9900000,
              remark: undefined,
              id: '3a12f2dd-47fe-318f-8f5d-3d0c634e2112',
            },
            // {
            //   userAvatar: 'https://picsum.photos/200/300',
            //   userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            //   userName: 'limin',
            //   hourType: 0,
            //   workDate: '2024-06-26T00:00:00',
            //   workHour: 2.8,
            //   workDay: 0.4,
            //   workTime: 9900000,
            //   remark: undefined,
            //   id: '3a12f2dd-47eb-f177-abb5-ba8dfb4858f8',
            // },
            // {
            //   userAvatar: 'https://picsum.photos/200/300',
            //   userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            //   userName: 'limin',
            //   hourType: 0,
            //   workDate: '2024-06-25T00:00:00',
            //   workHour: 2.8,
            //   workDay: 0.4,
            //   workTime: 9900000,
            //   remark: undefined,
            //   id: '3a12f2dd-47cb-5424-3a49-7a4db8b7aa3d',
            // },
            // {
            //   userAvatar: 'https://picsum.photos/200/300',
            //   userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
            //   userName: 'limin',
            //   hourType: 0,
            //   workDate: '2024-06-24T00:00:00',
            //   workHour: 2.8,
            //   workDay: 0.4,
            //   workTime: 9900000,
            //   remark: undefined,
            //   id: '3a12f2dd-47a4-c9a6-f7d4-d3dc14cc1d23',
            // },
          ],
        },
        actualTime: {
          totalTime: 100,
          timeRecord: [
            {
              userAvatar: 'https://picsum.photos/200/300',
              userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              userName: '张三',
              hourType: 1,
              workDate: '2024-05-24T00:00:00',
              workHour: 3,
              workDay: 0.4,
              workTime: 10800000,
              remark: '1111',
              id: '3a12f2dd-01e4-a6d5-ba10-f0f783a8f818',
            },
            {
              userAvatar: 'https://picsum.photos/200/300',
              userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              userName: '张三',
              hourType: 1,
              workDate: '2024-05-23T00:00:00',
              workHour: 3,
              workDay: 0.4,
              workTime: 10800000,
              remark: undefined,
              id: '3a12f2dd-01d5-6ba4-709b-af3eaf18edd2',
            },
            {
              userAvatar: 'https://picsum.photos/200/300',
              userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              userName: '张三',
              hourType: 1,
              workDate: '2024-05-22T00:00:00',
              workHour: 3,
              workDay: 0.4,
              workTime: 10800000,
              remark: undefined,
              id: '3a12f2dd-01c0-f011-caa5-cf576f85dcc0',
            },
            {
              userAvatar: 'https://picsum.photos/200/300',
              userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
              userName: '张三',
              hourType: 1,
              workDate: '2024-05-21T00:00:00',
              workHour: 3,
              workDay: 0.4,
              workTime: 10800000,
              remark: undefined,
              id: '3a12f2dd-0167-7e5e-d301-ef6f39b7be84',
            },
          ],
        },
      });
    }, 1000);
  });

  function loadHourDetail(): Promise<ITaskHourDetailDto> {
    return new Promise((resolve) => {
      resolve({
        isShowWorkByDay: false,
        planSpendDay: 1.5,
        realSpendDay: 34.4,
        planSpendHours: 12,
        realSpendHours: 11,
        users: new Array(3).fill(0).map((_, index) => ({
          userId: index.toString(),
          userName: 'limin' + index,
          userAvatar: 'https://picsum.photos/200/300',
          planSpendDay: 1.5,
          realSpendDay: 34.4,
          planSpendHours: 12,
          realSpendHours: index % 3 === 1 ? 275 : index % 3 === 2 ? 12 : 10,
          taskHours: [
            {
              category:'默认',
              categoryCode:'Default',
              hourType: 1,
              workDate: '2024-07-17T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: '11111' + index,
              id: '3a12f30f-0ead-3fa7-a1ee-5648616230fa',
            },
            {
              hourType: 1,
              workDate: '2024-07-16T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0e96-2d03-a0a5-cfa0fbfd430c',
            },
            {
              hourType: 1,
              workDate: '2024-07-15T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0e7d-a5a2-7cfa-c667d55abdb6',
            },
            {
              hourType: 1,
              workDate: '2024-07-14T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0e62-effa-2a3b-b891d33898b6',
            },
            {
              hourType: 1,
              workDate: '2024-07-13T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0e49-2890-cdf5-628d4d1713ec',
            },
            {
              hourType: 1,
              workDate: '2024-07-12T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0e35-ddac-2c81-ad4a5ebbe5f2',
            },
            {
              hourType: 1,
              workDate: '2024-07-11T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0e22-06f8-babe-1b0b1d4af373',
            },
            {
              hourType: 1,
              workDate: '2024-07-10T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0e10-8d78-125f-999e2b3687a2',
            },
            {
              hourType: 1,
              workDate: '2024-07-09T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0dff-53aa-5171-6b78cbcd0476',
            },
            {
              hourType: 1,
              workDate: '2024-07-08T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0dee-d956-c51b-e069a90c50fb',
            },
            {
              hourType: 1,
              workDate: '2024-07-07T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0dde-e053-88e9-bac41ae51ec4',
            },
            {
              hourType: 1,
              workDate: '2024-07-06T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0dce-65f7-bb28-968c5886e713',
            },
            {
              hourType: 1,
              workDate: '2024-07-05T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0dbe-5ed7-a88e-3816218d09e2',
            },
            {
              hourType: 1,
              workDate: '2024-07-04T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0daa-e0db-2bd0-466603e2ac40',
            },
            {
              hourType: 1,
              workDate: '2024-07-03T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0d99-4f8c-9e6b-9ea9cbb89f09',
            },
            {
              hourType: 1,
              workDate: '2024-07-02T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0d89-b168-e936-5dc33a1dc964',
            },
            {
              hourType: 1,
              workDate: '2024-07-01T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0d7a-b510-834d-3b42c5f7dbdd',
            },
            {
              hourType: 1,
              workDate: '2024-06-30T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0d6a-b115-304a-b98adeb08604',
            },
            {
              hourType: 1,
              workDate: '2024-06-29T00:00:00',
              workHour: 8,
              workDay: 1,
              workTime: 28800000,
              remark: undefined,
              id: '3a12f30f-0d55-2841-cdb8-0d17aa1f04e6',
            },
            {
              hourType: 1,
              workDate: '2024-06-27T00:00:00',
              workHour: 2.8,
              workDay: 0.4,
              workTime: 9900000,
              remark: undefined,
              id: '3a12f2dd-47fe-318f-8f5d-3d0c634e2112',
            },
            {
              hourType: 1,
              workDate: '2024-06-26T00:00:00',
              workHour: 2.8,
              workDay: 0.4,
              workTime: 9900000,
              remark: undefined,
              id: '3a12f2dd-47eb-f177-abb5-ba8dfb4858f8',
            },
            {
              hourType: 1,
              workDate: '2024-06-25T00:00:00',
              workHour: 2.8,
              workDay: 0.4,
              workTime: 9900000,
              remark: undefined,
              id: '3a12f2dd-47cb-5424-3a49-7a4db8b7aa3d',
            },
            {
              hourType: 1,
              workDate: '2024-06-24T00:00:00',
              workHour: 2.8,
              workDay: 0.4,
              workTime: 9900000,
              remark: undefined,
              id: '3a12f2dd-47a4-c9a6-f7d4-d3dc14cc1d23',
            },
            {
              hourType: 0,
              workDate: undefined,
              workHour: 3,
              workDay: 0.4,
              workTime: 10800000,
              remark: undefined,
              id: '3a12f2dd-01e4-a6d5-ba10-f0f783a8f818',
            },
            {
              hourType: 0,
              workDate: '2024-05-23T00:00:00',
              workHour: 3,
              workDay: 0.4,
              workTime: 10800000,
              remark: undefined,
              id: '3a12f2dd-01d5-6ba4-709b-af3eaf18edd2',
            },
            {
              hourType: 0,
              workDate: '2024-05-22T00:00:00',
              workHour: 3,
              workDay: 0.4,
              workTime: 10800000,
              remark: undefined,
              id: '3a12f2dd-01c0-f011-caa5-cf576f85dcc0',
            },
            {
              hourType: 0,
              workDate: '2024-05-21T00:00:00',
              workHour: 3,
              workDay: 0.4,
              workTime: 10800000,
              remark: undefined,
              id: '3a12f2dd-0167-7e5e-d301-ef6f39b7be84',
            },
          ],
        })),
      });
    });
  }

  function handleUpdate() {
    console.log('刷新');
  }
  function handleDelete({ id, reload, type }) {
    const index = dataSource.planTime?.timeRecord.findIndex((item: any) => item.id === id) ?? -1;
    if (index >= 0) {
      dataSource.planTime?.timeRecord.splice(index, 1);
    }
    console.log(id);
    console.log(type);
    reload();
  }
  function handleDeleteDetail({ data, reload }) {
    console.log(data);
    reload();
  }

  function handleSubmit({ value: { id, data }, resolve }) {
    console.log(id);
    console.log(data);
    resolve();
  }
</script>
<style lang="less" scoped></style>
