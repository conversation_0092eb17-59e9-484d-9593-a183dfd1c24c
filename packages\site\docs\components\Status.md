## 状态

<demo src="../demo/Status/basic.vue" title="状态"></demo>

## 属性

| 参数           | 说明                                          | 类型          | 可选值 | 默认值 | 版本 |
| -------------- | --------------------------------------------- | ------------- | ------ | ------ | ---- |
| value(v-model) | 绑定值                                        | string,number | --     | --     | 1.0  |
| visible        | 状态按钮可点击状态                            | boolean       | --     | false  | 1.0  |
| options        | 状态操作的详细配置，参下表[options](#options) | array         | --     | []     | 1.0  |

### <span id='options'>options</span>

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| title | 状态文本 | string | -- | -- | 1.0 |
| id | 当前状态绑定值 | string，number | -- | -- | 1.0 |
| isProgress | 是否在步骤条中展示及展示顺序, 0 表示不展示 | function | -- | -- | 1.0 |
| color | 状态颜色 | string | grey,orange,blue,green,pink,red | -- | 1.0 |
| actions | 操作按钮配置，参下表[actions](#actions) | string | array | [] | 1.0 |

### <span id='actions'>actions</span>

| 参数   | 说明               | 类型           | 可选值  | 默认值  | 版本 |
| ------ | ------------------ | -------------- | ------- | ------- | ---- |
| label  | 操作按钮的文本     | string         | --      | --      | 1.0  |
| id     | 当前操作绑定值     | string，number | --      | --      | 1.0  |
| action | 点击操作按钮的回调 | function       | --      | --      | 1.0  |
| type   | 按钮类型           | string         | primary | default | 1.0  |
