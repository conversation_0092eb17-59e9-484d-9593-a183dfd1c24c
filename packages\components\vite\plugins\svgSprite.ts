/**
 *  Vite Plugin for fast creating SVG sprites.
 * https://github.com/anncwb/vite-plugin-svg-icons
 */

import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import path from 'path';

export function configSvgIconsPlugin(isBuild: boolean) {
  const cwd = process.cwd();
  const svgIconsPlugin = createSvgIconsPlugin({
    iconDirs: [path.resolve(cwd, 'src/assets/svgs')],
    svgoOptions: isBuild,
    // default
    symbolId: 'icon-[dir]-[name]',
    customDomId: '__otd__svg__icons__dom__',
  });

  return svgIconsPlugin;
}
