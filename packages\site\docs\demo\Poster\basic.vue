<template>
  <OtdPoster ref="PosterRef" :draw-options="drawOptions" :ratio="2" />

  <Button @click="handleCopy">复制</Button>
  <Button @click="handleClick">重新生成</Button>
</template>
<script lang="ts" setup>
  import { Button, DrawOptionType, message, OtdPoster } from '@otd/otd-ui';
  import { copyImageToClipboard } from '@otd/otd-ui/src/tool';
  import { reactive, ref } from 'vue';

  const drawOptions: DrawOptionType[] = reactive([
    {
      type: 'image',
      content: 'https://picsum.photos/200/300',
      coordinate: [0, 0],
      style: { width: 200, height: 300, borderRadius: 4 },
    },
    {
      type: 'text',
      content: 'Hello World',
      coordinate: [50, 44],
      style: { color: '#fff', fontSize: 18 },
    },
    {
      type: 'image',
      content: 'https://picsum.photos/200/300',
      coordinate: [10, 30],
      style: { borderRadius: 16, width: 32, height: 32 },
    },
    {
      type: 'image',
      content: () =>
        new Promise((resolve) =>
          setTimeout(() => {
            resolve(
              'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsCAIAAAD2HxkiAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAEnklEQVR4nO3d0W3jMBBAwfMh/bfstEAgBN+KminAdqQ88Gex/Hy/339A53/9A+DtRAgxEUJMhBATIcRECDERQkyEEBMhxEQIMRFCTIQQEyHERAgxEUJMhBATIcRECDERQkyEEBMhxEQIMRFC7GfXB30+n10f9Tgru1tXns+uHbDTvutWu56hkxBiIoSYCCEmQoiJEGIihJgIISZCiIkQYiKEmAghtm12dMWuWbuTds1GTpsLPTlf+ub3vsJJCDERQkyEEBMhxEQIMRFCTIQQEyHERAgxEUJMhBA7Oju64uTM3rSZxpPznNO8+b07CSEmQoiJEGIihJgIISZCiIkQYiKEmAghJkKIiRBi42ZHb7VrNvLN86W3chJCTIQQEyHERAgxEUJMhBATIcRECDERQkyEEBMhxMyOHrLrHvkV5kufxUkIMRFCTIQQEyHERAgxEUJMhBATIcRECDERQkyEEBs3O/rmmcZd86UnP2eXN793JyHERAgxEUJMhBATIcRECDERQkyEEBMhxEQIMRFC7Ojs6K69mk80bVbzpDe/9xVOQoiJEGIihJgIISZCiIkQYiKEmAghJkKIiRBiIoTY59Z5RXgKJyHERAgxEUJMhBATIcRECDERQkyEEBMhxEQIMRFCbNyd9SfvZN9l2h3xt95r/8RnuMJJCDERQkyEEBMhxEQIMRFCTIQQEyHERAgxEUJMhBDbtnd02r3k02YaV3gXfzftXaxwEkJMhBATIcRECDERQkyEEBMhxEQIMRFCTIQQEyHEHjk7euve0V3ftWLabtI37zh1EkJMhBATIcRECDERQkyEEBMhxEQIMRFCTIQQEyHEts2O7nLrLOIK865/98Tf7CSEmAghJkKIiRBiIoSYCCEmQoiJEGIihJgIISZCiB3dOzptXvGJd7s/cSZ2xbT/nxX2jsIlRAgxEUJMhBATIcRECDERQkyEEBMhxEQIMRFCbNze0Vu9eSZ2xcm52Wnzt05CiIkQYiKEmAghJkKIiRBiIoSYCCEmQoiJEGIihNjRvaO3evP87ZtnYu0dhUuIEGIihJgIISZCiIkQYiKEmAghJkKIiRBiIoTYz8kve+KM5a55xZO7Lk9+166Zz2kzqPaOwouIEGIihJgIISZCiIkQYiKEmAghJkKIiRBiIoTY0dnRFSd3S06bZZ22V3PFtJnPJ95r7ySEmAghJkKIiRBiIoSYCCEmQoiJEGIihJgIISZCiI2bHb3VtDvZp+0m3fU5J+dLd3ESQkyEEBMhxEQIMRFCTIQQEyHERAgxEUJMhBATIcTMjh4y7c76aXY9n5PP0N5RuIQIISZCiIkQYiKEmAghJkKIiRBiIoSYCCEmQoiNmx2ddo/8Lrf+XbtMm/m0dxReRIQQEyHERAgxEUJMhBATIcRECDERQkyEEBMhxI7Ojj5xH+ZJu57Pybvdd5l2j7y9o/AiIoSYCCEmQoiJEGIihJgIISZCiIkQYiKEmAgh9rEPE1pOQoiJEGIihJgIISZCiIkQYiKEmAghJkKIiRBiIoSYCCEmQoiJEGIihJgIISZCiIkQYiKEmAghJkKIiRBiIoTYL4HeGV2uOtaLAAAAAElFTkSuQmCC',
            );
          }, 600),
        ),
      coordinate: [120, 70],
      style: { width: 50, height: 50 },
    },
    {
      type: 'text',
      content: 'Hello World222',
      coordinate: [50, 60],
      style: { color: 'blue', fontSize: 14 },
    },
  ]);

  const PosterRef = ref();
  function handleCopy() {
    copyImageToClipboard(PosterRef.value.toDataURL());
    message.success('复制成功');
  }
  function handleClick() {
    drawOptions[1].content += '1';
    PosterRef.value.drawPoster();
  }
</script>
<style lang="less" scoped></style>
