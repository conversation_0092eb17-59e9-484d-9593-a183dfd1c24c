// token key
export const TOKEN_KEY = 'OTD_TOKEN__';

export const LOCALE_KEY = 'OTD_LOCALE__';

// user info key
export const USER_INFO_KEY = 'OTD_USER__INFO__';

export const ACCOUNT_INFO_KEY = 'OTD_ACCOUNT__INFO__';

// role info key
export const ROLES_KEY = 'OTD_ROLES__KEY__';

// project config key
export const PROJ_CFG_KEY = 'OTD_PROJ__CFG__KEY__';

// lock info
export const LOCK_INFO_KEY = 'OTD_LOCK__INFO__KEY__';

export const APP_DARK_MODE_KEY_ = 'OTD_APP__DARK__MODE__';

// base global local key
export const APP_LOCAL_CACHE_KEY = 'COMMON__LOCAL__KEY__';

// base global session key
export const APP_SESSION_CACHE_KEY = 'COMMON__SESSION__KEY__';

export const TETANT_KEY = 'OTD_TETANT__';

export const AUTH_KEY = 'OTD_AUTH__KEY__';

export const LAST_PAGE = 'OTD_LAST_PAGE__';

export enum CacheTypeEnum {
  SESSION,
  LOCAL,
}
