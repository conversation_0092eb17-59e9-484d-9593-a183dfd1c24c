import { FilterActionType } from '/@/components/OtdTabFilter';
import { TablePaginationConfig } from 'ant-design-vue';
import { PropType } from 'vue';
import { BasicFormProps } from '/@/components/BasicForm';
import { TableColumnPropsType, TableSummaryType } from './types';
import { Recordable } from '/#/global';
import { GetRowKey, TableRowSelection } from 'ant-design-vue/es/table/interface';
import { mutable } from '/@/utils/props';

export const tableProps = () => ({
  dataSource: {
    type: Array as PropType<Recordable[]>,
    default: () => [],
  },
  pagination: {
    type: [Object, Boolean] as PropType<TablePaginationConfig | false>,
    default: () => {},
  },
  bordered: {
    type: Boolean,
    default: false,
  },
  formBorder: {
    type: Boolean,
    default: true,
  },
  showIndexColumn: {
    type: Boolean,
    default: false,
  },
  indexColumnProps: {
    type: Object as PropType<TableColumnPropsType>,
    default: () => ({}),
  },
  actionColumn: {
    type: Object as PropType<TableColumnPropsType>,
    default: null,
  },
  columns: {
    type: Array as PropType<TableColumnPropsType[]>,
    default: () => [],
  },
  summary: {
    type: Array as PropType<TableSummaryType[]>,
  },
  rowSelection: {
    type: Object as PropType<TableRowSelection>,
  },
  rowKey: {
    type: [String, Function] as PropType<string | GetRowKey<any>>,
  },
  childrenColumnName: {
    type: String,
  },
  canResize: {
    type: Boolean,
    default: false,
  },
  defaultShowSelect: {
    type: Boolean,
    default: false,
  },
  showTableSetting: {
    type: Boolean,
    default: false,
  },
  settingConfig: {
    type: Array as PropType<FilterActionType[]>,
    default: () => [],
  },
  formConfig: {
    type: Object as PropType<BasicFormProps>,
  },
  immediateRequest: {
    type: Boolean,
    default: true,
  },
  // 远程请求
  remoteRequest: {
    type: Function as PropType<(...arg: any) => Promise<any>>,
  },
  // 是否自动生成key
  autoCreateKey: {
    type: Boolean,
    default: false,
  },
  isTreeTable: {
    type: Boolean,
    default: false,
  },
  // 可拖拽
  draggable: {
    type: Boolean,
    default: false,
  },
  padding: {
    type: Array as unknown as PropType<[number, number]>,
    default: () => [14, 16],
  },
  // 点击行选中
  clickToRowSelect: Boolean,
});

const emit = [
  'fetch-success',
  'fetch-error',
  'selection-change',
  'register',
  'row-click',
  'row-dbClick',
  'row-contextmenu',
  'row-mouseenter',
  'row-mouseleave',
  'edit-end',
  'edit-cancel',
  'edit-row-end',
  'edit-change',
  'expanded-rows-change',
  'change',
  'columns-change',
  'drag-end',
] as const;

export const getEmits = () => mutable(emit);
