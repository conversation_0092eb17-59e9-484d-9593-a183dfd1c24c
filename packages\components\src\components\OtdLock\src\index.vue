<template>
  <transition name="fade-bottom" mode="out-in">
    <LockPage v-if="getIsLock" />
  </transition>
</template>
<script lang="ts" setup>
  import { computed } from 'vue';
  import LockPage from './LockPage.vue';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';

  const { getGlobalProvide } = useGlobalConfig();
  const { getLockStorage } = getGlobalProvide();
  const getIsLock = computed(() => getLockStorage?.getLockInfo.value?.isLock ?? false);
</script>
