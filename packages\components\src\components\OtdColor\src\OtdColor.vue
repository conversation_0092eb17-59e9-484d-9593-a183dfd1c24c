<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div class="otd-color">
    <!-- <h1>{{ t("common.color") }}</h1> -->
    <div class="otd-color-picker" v-if="openPicker">
      <ColorPicker
        v-model:pureColor="data.color"
        picker-type="chrome"
        is-widget
        disable-history
        disable-alpha
        format="hex"
      />
      <Button class="w-full mt-4px" :disabled="isDisabled" @click="handleSaveColor(data.color, true)">
        {{ t('common.saveText') }}
      </Button>
    </div>
    <RadioGroup
      v-model:value="data.color"
      class="otd-color-group"
      :style="`--count: ${colunmCount}`"
      @change="(e) => handleSaveColor(e.target.value)"
    >
      <RadioButton v-for="item in ColorOptions" :key="item" :value="item" :style="`--color:${item}`">
        <div class="otd-color-item"></div>
      </RadioButton>
      <label class="otd-color__choose" @click="handleOpenPicker">
        <i class="otdIconfont otd-icon-a-cateditsize24"></i>
      </label>
    </RadioGroup>
  </div>
</template>
<script lang="ts" setup>
  import { PropType, ref } from 'vue';
  import { Radio, RadioGroup, Button } from 'ant-design-vue';
  import { ColorOptionType } from './types';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { ColorPicker } from 'vue3-colorpicker';
  import 'vue3-colorpicker/style.css';
  import { onMounted } from 'vue';
  import { unref } from 'vue';
  import { computed } from 'vue';
  import { ColorOptions } from './color';

  const RadioButton = Radio.Button;

  const { t } = useI18n();
  const props = defineProps({
    data: {
      type: Object as PropType<ColorOptionType>,
      default: () => ({}),
    },
    colunmCount: {
      type: Number,
      default: 8,
    },
  });
  const lastColor = ref('');
  const isDisabled = computed(() => {
    return lastColor.value === props.data.color;
  });

  const emit = defineEmits(['save']);

  const openPicker = ref(false);
  // 打开颜色选择器
  function handleOpenPicker() {
    openPicker.value = !openPicker.value;
  }

  // 保存颜色
  function handleSaveColor(color, isPicker = false) {
    emit('save', {
      value: props.data,
      color,
      lastColor: unref(lastColor),
      isPicker,
    });
  }

  onMounted(() => {
    lastColor.value = props.data.color as string;
  });
</script>
<style lang="less" scoped>
  .otd-color {
    .ant-radio-group {
      .ant-radio-button-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 26px;
        height: 26px;
        --color: '#fff';
        border: 2px solid transparent;
        border-radius: 50%;
        padding: 4px;
        &::before {
          content: unset;
        }
        &:hover {
          border-color: inherit;
        }
        &.ant-radio-button-wrapper-checked {
          border-color: var(--color);
        }
      }
    }
    :deep(.vc-colorpicker) {
      background-color: transparent;
      box-shadow: unset;
      width: 222px;
      margin-bottom: 10px;
      .vc-colorpicker--container {
        padding: 0;
        .vc-input-toggle {
          width: unset;
          height: unset;
        }
      }
    }
    &-group {
      display: grid;
      --count: 8;
      grid-template-columns: repeat(var(--count), 26px);
      gap: 2px;
    }
    &-item {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background-color: var(--color);
    }
    &__choose {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 8px;
      .otdIconfont {
        font-size: 18px;
      }
      &:hover {
        background-color: var(--otd-gray-hover);
      }
    }
    &-picker {
      margin-bottom: 8px;
    }
  }
</style>
