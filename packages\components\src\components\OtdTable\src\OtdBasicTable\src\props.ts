import { PropType } from 'vue';
import { Recordable } from '/#/global';
import { mutable } from '/@/tool';
import { GetRowKey } from 'ant-design-vue/es/vc-table/interface';
import { TableColumnPropsType } from '/@/components/OtdTable/src/OtdTable';
import { TableRowSelection } from 'ant-design-vue/es/table/interface';
import { BasicTableFilterType } from './type';

export const getProps = () => ({
  columns: {
    type: Object as PropType<TableColumnPropsType[]>,
    default: () => [],
  },
  rowKey: {
    type: Function as PropType<GetRowKey<Recordable>>,
    require: true,
  },
  indent: {
    type: Number,
    default: 0,
  },
  expandedRowKeys: {
    type: Array as PropType<string[]>,
  },
  rowSelection: {
    type: Object as PropType<TableRowSelection>,
  },
  rowHeight: {
    type: Number,
    default: 51,
  },
  dataSource: {
    type: Array as PropType<Recordable[]>,
    default: () => [],
  },
  childrenColumnName: {
    type: String,
    default: 'children',
  },
  filter: {
    type: Object as PropType<BasicTableFilterType>,
  },
});
const emit = ['update:expandedRowKeys', 'expand', 'expandedRowsChange', 'resizeColumn'] as const;

export const getEmits = () => mutable(emit);
