## 优先级

<demo src="../demo/Priority/basic.vue" title="优先级"></demo>

## 属性

| 参数            | 说明                   | 类型     | 可选值 | 默认值 | 版本 |
| --------------- | ---------------------- | -------- | ------ | ------ | ---- |
| value / v-model | 绑定值                 | number   | --     | --     | 1.0  |
| tipLabel        | hover 提示内容的 label | string   | --     | null   | 1.0  |
| isSimple        | 是否简洁显示           | boolean  | --     | false  | 1.0  |
| placeholder     | placeholder 值         | boolean  | --     | false  | 1.0  |
| options         | 下拉选项               | array    | --     | []     | 1.0  |
| disabled        | 是否可编辑             | boolean  | --     | false  | 1.0  |
| change          | 改变当前值的方法       | function | --     | --     | 1.0  |
| clear           | 清除当前值的方法       | function | --     | --     | 1.0  |
