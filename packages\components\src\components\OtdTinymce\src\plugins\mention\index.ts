import tinymce from 'tinymce';
import MentionPopover from './MentionPopover/index.vue';
import { nextTick, render } from 'vue';
import { createVNode } from 'vue';
import { isFunction } from 'lodash-es';
import { UserOptionItemType } from '/@/components/OtdUserSearch/src/type';
import { AtMentionEnum } from './MentionPopover/useMentionPopover';
import { PRIMARY_COLOR } from '/@/setting';

type SourceQueryType = { query?: string; tab?: AtMentionEnum; isChangeTab?: boolean };

export type AutoCompleteOptionType<T = undefined> = {
  source: (
    data: SourceQueryType,
    success: (data?: UserOptionItemType[]) => void,
    delimiter: string | string[],
  ) => Record<string, UserOptionItemType>[] | Record<string, UserOptionItemType>[];
  delimiter: T;
  delay: number;
  queryBy: string;
  insertFrom?: string;
  items: number;
  rootBody: HTMLElement;
  renderDropdown?: () => void;
  insert?: () => string;
  setOffset: () => { x: number; y: number };
};

const DefaultOptions = {
  source: [],
  delimiter: '',
  delay: 300,
  queryBy: 'label',
  items: 10,
  rootBody: document.body,
  setOffset: () => ({ x: 0, y: 0 }),
};

class AutoComplete {
  editor;
  query?: string;
  isChangeTab: boolean = true;
  searchTimeout?: NodeJS.Timeout;
  hasFocus: boolean = false;
  options: AutoCompleteOptionType<string>;
  $dropdown?: HTMLElement;
  editorKeyUpProxy?: (e) => void;
  editorKeyDownProxy?: (e) => void;
  editorClickProxy?: (e) => void;
  bodyClickProxy?: () => void;
  rteScroll?: () => void;
  dropdownExposed?: Record<string, any>;
  constructor(ed, options: Partial<AutoCompleteOptionType<any>>) {
    this.editor = ed;
    this.options = Object.assign({}, DefaultOptions, options);
  }
  init(delimiter: string) {
    this.options.delimiter = delimiter;
    this.renderDropdown = this.options.renderDropdown?.bind(this) || this.renderDropdown;
    this.insert = this.options.insert?.bind(this) || this.insert;

    this.query = '';
    this.isChangeTab = true;
    this.hasFocus = true;

    this.renderInput();
    this.bindEvents();
  }

  renderInput() {
    const rawHtml =
      '<span id="autocomplete">' +
      '<span id="autocomplete-delimiter">' +
      this.options.delimiter +
      '</span>' +
      '<span id="autocomplete-searchtext"><span class="dummy">\uFEFF\uFEFF</span></span>' +
      '</span>';

    this.editor.execCommand('mceInsertContent', false, rawHtml);
    this.editor.focus();
    this.editor.selection.select(this.editor.selection.dom.select('span#autocomplete-searchtext span')[0]);
    this.editor.selection.collapse(0);
  }

  renderDropdown() {
    const dom = document.createElement('div');
    const VueInstance = this.editor['VueInstance'];

    const vnode = createVNode(MentionPopover, {
      onSelect: (data, tab) => {
        this.select(data, tab);
        this.cleanUp(true);
      },
      onSearch: (keyword, tab, isChangeTab) => {
        this.isChangeTab = isChangeTab;
        this.lookup(tab, keyword);
      },
    });
    const { provides, appContext } = VueInstance;
    vnode.appContext = { ...appContext, provides };
    render(vnode, dom);
    const { exposed } = vnode.component!;
    this.dropdownExposed = exposed!;
    this.$dropdown = dom.firstElementChild as HTMLElement;
  }

  show() {
    this.renderDropdown();
    this.options.rootBody.appendChild(this.$dropdown!);
    nextTick(() => {
      const { top = 0, left = 0 } = this.getCursorPosition() ?? {};
      if (this.dropdownExposed) {
        this.dropdownExposed.setDropdownSeat([top, left]);
        setTimeout(() => {
          this.dropdownExposed!.setShow();
        });
      }
    });
  }

  getCursorPosition() {
    const iframe = this.editor.iframeElement;
    const iframeRect = iframe.getBoundingClientRect();
    const node = this.editor.getBody();
    const container = node.querySelector('#autocomplete');
    return {
      left: container.offsetLeft + iframeRect.left,
      top: container.offsetTop + iframeRect.top,
    };
  }

  bindEvents() {
    this.editor.on('keyup', (this.editorKeyUpProxy = this.rteKeyUp.bind(this)));
    this.editor.on('keydown', (this.editorKeyDownProxy = this.rteKeyDown.bind(this)), true);
    this.editor.on('click', (this.editorClickProxy = this.rteClicked.bind(this)));

    this.options.rootBody.addEventListener('click', (this.bodyClickProxy = this.rteLostFocus.bind(this)));

    this.editor.getWin().addEventListener(
      'scroll',
      (this.rteScroll = function (this) {
        this.cleanUp(true);
      }.bind(this)),
    );
  }

  unbindEvents() {
    this.editor.off('keyup', this.editorKeyUpProxy);
    this.editor.off('keydown', this.editorKeyDownProxy);
    this.editor.off('click', this.editorClickProxy);
    this.options.rootBody?.removeEventListener('click', this.bodyClickProxy!);
    this.editor.getWin()?.removeEventListener('scroll', this.rteScroll);
  }

  // 键盘抬起事件
  rteKeyUp(e) {
    switch (e.which || e.keyCode) {
      //DOWN ARROW
      case 40:
      //UP ARROW
      case 38:
      //SHIFT
      case 16:
      //CTRL
      case 17:
      //ALT
      case 18:
        break;

      //BACKSPACE
      case 8:
        if (this.query === '') {
          this.cleanUp(true);
        } else {
          this.lookup();
        }
        break;

      //TAB
      case 9:
      //ENTER
      case 13:
        const { data, tab } = this.dropdownExposed!.getActiveData();
        if (data) {
          this.select(data, tab);
          this.cleanUp(false);
        } else {
          this.cleanUp(true);
        }
        break;

      //ESC
      case 27:
        this.cleanUp(true);
        break;

      default:
        this.lookup();
    }
  }
  // 键盘按下事件
  rteKeyDown(e) {
    switch (e.which || e.keyCode) {
      //TAB
      case 9:
      //ENTER
      case 13:
      //ESC
      case 27:
        e.preventDefault();
        break;

      //UP ARROW
      case 38:
        e.preventDefault();
        if (this.$dropdown !== undefined) {
          this.dropdownExposed!.setActive(-1);
        }
        break;
      //DOWN ARROW
      case 40:
        e.preventDefault();
        if (this.$dropdown !== undefined) {
          this.dropdownExposed!.setActive(1);
        }
        break;
    }

    e.stopPropagation();
  }

  // 点击编辑区域
  rteClicked(e) {
    const $target = e.target as HTMLElement;
    if (this.hasFocus && $target.parentElement?.getAttribute('id') !== 'autocomplete-searchtext') {
      this.cleanUp(true);
    }
  }

  // 点击空白区域
  rteLostFocus() {
    if (this.hasFocus) {
      this.cleanUp(true);
    }
  }

  lookup(tab: AtMentionEnum = AtMentionEnum.User, keyword?: string) {
    const body = this.editor.getBody() as HTMLElement;
    const searchtext = body.querySelector('#autocomplete-searchtext') as HTMLElement;
    this.query = searchtext.outerText.trim().replace('\ufeff', '');
    if (this.$dropdown === undefined) {
      this.show();
    }
    clearTimeout(this.searchTimeout);
    if (this.isChangeTab) {
      this.dropdownExposed?.setLoading();
    }
    this.searchTimeout = setTimeout(
      function (this: any) {
        this.dropdownExposed!.setSearchValue(keyword ?? this.query);
        // Added delimiter parameter as last argument for backwards compatibility.
        const items = isFunction(this.options.source)
          ? this.options.source(
              { query: keyword ?? this.query, tab, isChangeTab: this.isChangeTab },
              this.process.bind(this),
              this.options.delimiter,
            )
          : this.options.source;
        if (items) {
          this.process(items);
        }
      }.bind(this),
      this.options.delay,
    );
  }

  process(data) {
    if (!this.hasFocus) {
      return;
    }
    this.isChangeTab = false;
    this.dropdownExposed!.setData(data);
  }

  // 清除下拉
  cleanUp(rollback) {
    this.unbindEvents();
    this.hasFocus = false;
    this.$dropdown = undefined;
    this.dropdownExposed!.setShow(false);
    this.dropdownExposed = undefined;
    if (rollback) {
      const text = this.query,
        $selection = this.editor.dom.select('span#autocomplete') as HTMLElement[];
      if (!$selection.length) {
        return;
      }
      const dom = document.createElement('p');
      dom.innerHTML = this.options.delimiter + text;
      const replacement = dom.firstChild;
      this.editor.dom.replace(replacement, $selection[0]);
      this.editor.selection.select(replacement);
      this.editor.selection.collapse();
    }
  }

  insert(item: UserOptionItemType, tab: AtMentionEnum = AtMentionEnum.User) {
    return `<a class="otd-at-mention" contenteditable="false" data-type="${tab}" data-id="${
      item.value
    }" style="color:${PRIMARY_COLOR};font-family: data-${item.value}-id">${
      this.options.delimiter + item[this.options.queryBy as string]
    }</a>`;
  }

  select(data: UserOptionItemType, tab: AtMentionEnum) {
    this.editor.focus();
    const selection = this.editor.dom.select('span#autocomplete')[0];
    this.editor.dom.remove(selection);
    this.editor.execCommand('mceInsertContent', false, this.insert(data, tab));
  }

  // 获取@的用户
  getMentionUser() {
    const node = this.editor.getBody();
    const userMentions = node.querySelectorAll(`.otd-at-mention[data-type="${AtMentionEnum.User}"]`);
    const data: UserOptionItemType[] = [];
    userMentions.forEach((item) => {
      data.push({
        value: item.dataset.id,
        label: item.innerText.slice(1),
      });
    });
    return {
      data,
    };
  }

  // 设置@的用户
  setMentionUser(item: UserOptionItemType) {
    this.editor.execCommand('mceInsertContent', false, this.insert(item));
  }
}

tinymce.PluginManager.add('mention', function (ed) {
  const autoCompleteData: Partial<AutoCompleteOptionType<string[]>> = ed.getParam('mentions');

  // If the delimiter is undefined set default value to ['@'].
  // If the delimiter is a string value convert it to an array. (backwards compatibility)
  autoCompleteData.delimiter =
    autoCompleteData.delimiter !== undefined
      ? !Array.isArray(autoCompleteData.delimiter)
        ? [autoCompleteData.delimiter]
        : autoCompleteData.delimiter
      : ['@'];

  // 判断上一个是否是空格
  // function prevCharIsSpace() {
  //   const start = ed.selection.getRng().startOffset,
  //     text = (ed.selection.getRng().startContainer as any).data || '',
  //     charachter = text.substr(start > 0 ? start - 1 : 0, 1);

  //   return charachter.trim().length ? false : true;
  // }

  const autoComplete = new AutoComplete(ed, autoCompleteData);

  ed.on('keypress', function (e) {
    const delimiter = autoCompleteData.delimiter?.find((item) => item === String.fromCharCode(e.which || e.keyCode));
    // && prevCharIsSpace()
    if (delimiter) {
      if (autoComplete.hasFocus !== undefined && !autoComplete.hasFocus) {
        e.preventDefault();
        // Clone options object and set the used delimiter.
        autoComplete.init(delimiter);
      }
    }
  });

  return {
    getMentionUser: autoComplete.getMentionUser.bind(autoComplete),
    setMentionUser: autoComplete.setMentionUser.bind(autoComplete),
  };
});
