<template>
  <SvgIcon :name="type" size="1em" v-if="CurrentValue?.isCustom" />
  <IconPark :type="type" theme="filled" v-else />
</template>
<script lang="ts" setup>
  import { computed } from 'vue';
  import SvgIcon from '../../SvgIcon.vue';
  import { IconPark } from '@icon-park/vue-next/es/all';
  import { IconMap } from './IconMap';

  const props = defineProps({
    type: {
      type: String,
      required: true,
    },
  });

  const CurrentValue = computed(() => IconMap.get(props.type));
</script>
<style lang="less" scoped></style>
