<template>
  <Button @click="startDrive">开始</Button>
  <div>
    <Button id="step1">步骤 1: 点击这里</Button>
    <Button id="step2">步骤 2: 点击这里</Button>
    <!-- <div id="step3">步骤 3: 悬浮到这里</div> -->
    <Menu
      v-model:openKeys="state.openKeys"
      v-model:selectedKeys="state.selectedKeys"
      mode="inline"
      :inline-collapsed="state.collapsed"
      :items="items"
      triggerSubMenuAction="click"
    ></Menu>
  </div>
</template>
<script lang="tsx" setup>
  import { MailOutlined, Button, Menu, useTourDriver } from '@otd/otd-ui';
  import { onMounted, reactive } from 'vue';

  const state = reactive({
    collapsed: true,
    selectedKeys: ['1'],
    openKeys: [],
    preOpenKeys: [],
  });
  const items = reactive<any>([
    {
      key: 'sub1',
      icon: <MailOutlined />,
      label: 'Navigation One',
      title: 'Navigation One',
      children: [
        {
          key: '5',
          label: 'Option 5',
          title: 'Option 5',
        },
      ],
    },
  ]);

  const { createDriver } = useTourDriver();

  // 初始化 driver
  const driverObj = createDriver({
    steps: [
      {
        element: '#step1',
        popover: {
          title: <strong style="font-size:12px">标题</strong>,
          description: <strong>这是步骤 1：请点击此按钮继续。</strong>,
          side: 'right',
        },
        trigger: 'click',
      },
      {
        element: '#step2',
        popover: { description: <strong>这是步骤 2：请点击此按钮继续。</strong>, side: 'right' },
        trigger: 'hover',
        delay: 500,
      },
      {
        // element: '#step3',
        element: '[data-menu-id="sub1"]',
        popover: { description: <strong>这是步骤 3：请将鼠标悬浮到这里。</strong>, side: 'top' },
        trigger: 'click',
      },
      {
        element: '[data-menu-id="5"] .ant-menu-title-content',
        popover: { description: <strong>这是步骤 4：请将鼠标悬浮到这里。</strong>, side: 'top' },
        trigger: 'click',
      },
    ],
    showProgress: true,
    showButtons: ['next', 'previous', 'close'], // 隐藏默认的“下一步”按钮
  });

  function startDrive() {
    driverObj.drive();
  }

  // 启动引导
  onMounted(() => {
    // startDrive();
  });
</script>
<style lang="less" scoped></style>
