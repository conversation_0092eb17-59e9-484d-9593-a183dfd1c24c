<template>
  <OtdTabFilter is-card :tab-options="tabOptions" :form-schemas="searchFormSchema" :filter-actions="filterActions">
    <template #add>
      <i class="otdIconfont otd-icon-add-2"></i>
    </template>
    <template #filter>
      <Button>1111</Button>
    </template>
  </OtdTabFilter>
</template>
<script lang="tsx" setup>
  import { OtdTabFilter, FilterActionType, TabItemType, FormSchema, Button } from '@otd/otd-ui';
  const tabOptions: TabItemType[] = [
    {
      value: 1,
      icon: 'otd-icon-bianjiwenjianjia',
      label: '列表',
    },
    {
      value: 2,
      icon: 'otd-icon-bianjiwenjianjia',
      label: '看板',
    },
    {
      value: 3,
      icon: 'otd-icon-bianjiwenjianjia',
      label: '工作量',
    },
  ];
  const searchFormSchema: FormSchema[] = new Array(10).fill(1).map((_, index) =>
    // 关键字
    ({
      field: 'filter',
      label: '关键字' + new Array(index).fill('-').join(''),
      component: 'Input',
    }),
  );
  const filterActions: FilterActionType[] = [
    {
      id: 1,
      title: '筛选',
      icon: 'otd-icon-shaixuan',
      action: () => {
        console.log(1);
      },
    },
    {
      id: 2,
      title: '列设置',
      icon: 'otd-icon-lieshezhi',
      action: () => {
        console.log(2);
      },
    },
    {
      id: 3,
      title: '刷新',
      icon: 'otd-icon-refresh',
      action: () => {
        console.log(3);
      },
    },
  ];
</script>
<style lang="less" scoped></style>
