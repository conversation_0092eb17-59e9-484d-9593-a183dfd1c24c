<template>
  <div class="otd-folder-list">
    <DirectoryTree
      ref="directory"
      v-model:expandedKeys="expandedKeys"
      v-model:selectedKeys="selectedKeys"
      :tree-data="data"
      :load-data="loadData"
      :loadedKeys="loadedKeys"
      :fieldNames="{ title: 'name', key: 'id', children: 'children' }"
      @load="handleLoad"
      @expand="handleExpand"
    >
      <!-- @select="handleSelect" -->
      <template #icon="scope">
        <i class="otdIconfont otd-icon-folder" :title="scope.name"></i>
      </template>
      <template #title="scope">
        <div class="tree-title-box" :title="scope.name">
          <span v-if="!folderClick && !scope.isLeaf" @click="handleSelect(scope)">
            {{ scope.name }}
          </span>
          <span @click="handleSelect(scope)" v-else>{{ scope.name }}</span>
          <!-- 文件操作 start -->
          <OtdMoreAction :data="scope" action-type="icon" :actions="actions" v-if="!scope.isAll && !hideAction" />
          <!-- 文件操作 end -->
        </div>
      </template>
    </DirectoryTree>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { DirectoryTree } from 'ant-design-vue';
  import { OtdMoreAction } from '/@/components/OtdMoreAction';
  import { getProps } from '/@/components/OtdFolderMenu/src/props';

  const props = defineProps(getProps());

  const emits = defineEmits(['update:expanded', 'update:selected', 'select', 'expand', 'refresh']);

  const selectedKeys = ref<string[]>([]);
  const expandedKeys = ref<string[]>([]);
  const loadedKeys = ref<string[]>([]);
  watch(
    () => props.expanded,
    (value) => {
      expandedKeys.value = (value as string[]) || [];
    },
    { deep: true, immediate: true },
  );
  watch(
    () => props.selected,
    (value) => {
      selectedKeys.value = value as string[];
    },
    { deep: true, immediate: true },
  );

  function handleSelect(data) {
    selectedKeys.value = [data.isLeaf ? data.resourceId : data.id];
    emits('update:selected', selectedKeys.value);
    emits('select', data);
  }
  const directory = ref();
  function handleExpand(_keys, e) {
    emits('update:expanded', expandedKeys.value);
    emits('expand', e.node);
  }

  function handleLoad(_key) {
    loadedKeys.value = _key;
  }

  defineExpose({
    directory,
    loadedKeys,
  });
</script>
<style lang="less" scoped>
  .otd-folder-list {
    :deep(.ant-tree-list) {
      .ant-tree-treenode {
        height: 30px;
        line-height: 30px;
        padding: 0;
        border-radius: 8px;
        &:hover {
          &::before {
            border-radius: 8px;
          }
        }
        &::before {
          bottom: 0;
        }
        &.ant-tree-treenode-selected {
          background-color: var(--otd-basic-active);
          .icon-wenjianjia1 {
            color: var(--otd-primary-color);
            &::before {
              content: '\eac4';
            }
          }
          &::before {
            content: unset;
          }
        }
        .ant-tree-switcher {
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 30px;
          color: var(--otd-basic-text);
          z-index: 5;
          font-size: 12px;
          &.ant-tree-switcher-noop {
            & + .ant-tree-node-content-wrapper {
              .ant-tree-iconEle {
                display: none;
              }
            }
          }
          .anticon-caret-down {
            display: inline-flex;
            align-items: center;
            width: 18px;
            height: 18px;
            font-size: 12px;
            border-radius: 6px;
            padding: 0 4px;
            cursor: pointer;
            &:hover {
              background-color: var(--otd-border-color);
            }
          }
        }
        .ant-tree-node-content-wrapper {
          display: flex;
          line-height: 30px;
          height: 30px;
          font-size: 14px;
          padding: 0;
          color: var(--otd-basic-text);
          overflow: hidden;
          .ant-tree-iconEle {
            line-height: 30px;
          }
          .ant-tree-title {
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .ant-tree-node-selected {
          background-color: var(--otd-basic-active) !important;
          border-radius: 8px;
          color: var(--otd-primary-color) !important;
          .ant-tree-switcher {
            color: var(--otd-primary-color);
          }
        }
        .ant-tree-switcher_open {
          &.ant-tree-node-content-wrapper {
            color: var(--otd-basic-text);
          }
        }
      }
    }
    .tree-title-box {
      position: relative;
      padding-right: 20px;
      overflow: hidden;
      display: flex;
      > span {
        width: 100%;
        display: inline-block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      :deep(.otd-more-action) {
        position: absolute;
        right: 4px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
</style>
