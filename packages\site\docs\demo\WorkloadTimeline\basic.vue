<template>
  <div style="height: 600px">
    <OtdWorkloadTimeline
      :dateRange="dateRange"
      :request="handleRequest"
      :drap-end="handleDrop"
      :resize-end="handleResizes"
      :move-end="handleMove"
    />
  </div>
</template>
<script lang="ts" setup>
  import { OtdWorkloadTimeline } from '@otd/otd-ui';
  import { nextTick, ref } from 'vue';

  const dateRange = ref();
  nextTick(() => {
    dateRange.value = ['2024-06-17', '2024-06-23'];
  });
  function handleRequest() {
    return Promise.resolve(
      new Array(2).fill(0).map((_, index) => ({
        units: [
          {
            unitId: '3a094b11-b14d-4822-b534-1919d4de88b2',
            unitName: '一级',
          },
          {
            unitId: 'b1c82b5f-20f7-954d-089e-3a0de2ad9638',
            unitName: 'IE',
          },
        ],
        userId: index,
        userName: 'limin',
        avatar: 'https://picsum.photos/200/300',
        taskItems:
          index === 0
            ? undefined
            : [
                {
                  relatedId: null,
                  relatedName: null,
                  relatedType: 4,
                  subRelatedType: null,
                  i18NRelatedType: null,
                  i18NSubRelatedType: null,
                  parentTaskId: '3a0f3589-cf28-c844-6786-741f9aed4145',
                  parentTaskItemTitle: '测试父子任务样式111',
                  preTaskItemId: null,
                  preTaskItemTitle: null,
                  title: '测试子任务333',
                  description: null,
                  priority: 1,
                  taskProgress: 99,
                  status: 10,
                  groupSort: 300,
                  statusSort: 3100,
                  responsibleOrganizeId: null,
                  checkerOrganizeId: null,
                  responsibleUserId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleUserName: 'limin',
                  responsibleUserAvatar:
                    '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleGroupId: '3a0b63ac-dfbc-bb17-3534-e2af0340c3f5',
                  responsibleGroup: {
                    name: '上线阶段',
                    color: null,
                    order: 1,
                    isDefault: false,
                    lastModificationTime: '2024-05-21T09:28:53.345811',
                    lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                    creationTime: '2023-05-25T14:02:05.118385',
                    creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                    id: '3a0b63ac-dfbc-bb17-3534-e2af0340c3f5',
                    extraProperties: {},
                  },
                  checkUsers: [
                    {
                      userId: '245875ae-10f9-f520-cdca-3a0de2a8c9e8',
                      name: '陈永琴',
                      avatar: null,
                      actualCheckDate: null,
                    },
                    {
                      userId: '670775e0-0fee-f02c-da65-3a0de2a8caf9',
                      name: '孙雪雯',
                      avatar: null,
                      actualCheckDate: null,
                    },
                  ],
                  followUsers: [
                    {
                      userId: '3a538479-2b56-788d-9f48-3a0de2a8c697',
                      name: '汤卫国',
                      avatar: null,
                      creationTime: '2024-05-20T11:19:00.023155',
                    },
                    {
                      userId: 'e17af8cd-f712-37e2-ffe9-3a0de2a8d3ab',
                      name: '韩立霞',
                      avatar: null,
                      creationTime: '2024-05-20T11:19:03.228739',
                    },
                    {
                      userId: 'de970639-704d-8de6-bf7f-3a0de2a8c7c9',
                      name: '胡洵洵',
                      avatar: null,
                      creationTime: '2024-05-20T11:19:01.147851',
                    },
                    {
                      userId: 'ca493acc-2948-a273-402e-3a0de2a8c8e0',
                      name: '智丹丹',
                      avatar: null,
                      creationTime: '2024-05-20T11:18:58.312513',
                    },
                    {
                      userId: '956e0876-cc70-cf80-0f91-3a0de2a8cd25',
                      name: '郝达芬',
                      avatar: null,
                      creationTime: '2024-05-20T11:18:56.648466',
                    },
                    {
                      userId: '8878362d-b73e-ed17-8864-3a0de2a8cc10',
                      name: '徐青峰',
                      avatar: null,
                      creationTime: '2024-05-20T11:19:01.687474',
                    },
                    {
                      userId: '670775e0-0fee-f02c-da65-3a0de2a8caf9',
                      name: '孙雪雯',
                      avatar: null,
                      creationTime: '2024-05-20T11:18:54.621543',
                    },
                    {
                      userId: '5873157c-943e-9b23-ed46-3a0de2a8ce36',
                      name: '张正花',
                      avatar: null,
                      creationTime: '2024-05-20T11:18:55.819571',
                    },
                    {
                      userId: '40960d80-cc85-fca6-ec6a-3a0de2a8d071',
                      name: '范育菁',
                      avatar: null,
                      creationTime: '2024-05-20T11:18:57.838716',
                    },
                    {
                      userId: '1475ae74-6669-3a7b-d97c-3a0de2a8d17c',
                      name: '朱成',
                      avatar: null,
                      creationTime: '2024-05-20T11:18:56.909639',
                    },
                    {
                      userId: '3a0f4fa9-cddb-f885-2ecc-2b0b466686e0',
                      name: 'rikka',
                      avatar: null,
                      creationTime: '2024-05-20T11:19:03.830552',
                    },
                    {
                      userId: '3a0e6d32-fa4d-f50d-bc46-7b4d9ad67446',
                      name: 'adw2',
                      avatar: null,
                      creationTime: '2024-05-20T11:18:56.335382',
                    },
                    {
                      userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      name: 'limin',
                      avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                      creationTime: '2023-12-01T11:12:55.714184',
                    },
                    {
                      userId: '245875ae-10f9-f520-cdca-3a0de2a8c9e8',
                      name: '陈永琴',
                      avatar: null,
                      creationTime: '2024-05-20T11:18:54.249322',
                    },
                  ],
                  planStartDate: '2024-06-20T00:00:00',
                  planStart: '2024-06-20',
                  planDoneDate: '2024-06-22T23:59:59',
                  planDone: '2024-06-22',
                  actualStartDate: '2023-12-13T16:52:15.243863',
                  actualDoneDate: null,
                  actualCheckDate: null,
                  actualCloseDate: null,
                  childCount: 9,
                  childCloseCount: 0,
                  canCreateChild: false,
                  creatorName: '超级管理员',
                  taskDocLinks: null,
                  taskItemTags: [
                    {
                      tagName: '1212121121',
                      color: '#2980B9',
                      tagAuth: 0,
                      tagType: 0,
                      creationTime: '2023-10-27T09:30:09.693263',
                      creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      id: '3a0e80ed-9f98-1c3f-76bd-d05afeae9255',
                    },
                    {
                      tagName: '1212',
                      color: '#808080',
                      tagAuth: 1,
                      tagType: 0,
                      creationTime: '2023-10-27T09:32:24.826535',
                      creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      id: '3a0e80ef-af75-54f2-69cc-42c34716c398',
                    },
                    {
                      tagName: '..',
                      color: '#FF5733',
                      tagAuth: 0,
                      tagType: 0,
                      creationTime: '2023-10-31T13:24:49.090136',
                      creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      id: '3a0e965d-e53d-a570-afc8-a3831c5e117f',
                    },
                    {
                      tagName: '1111',
                      color: '#B1F421',
                      tagAuth: 0,
                      tagType: 0,
                      creationTime: '2024-06-25T16:52:29.466204',
                      creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      id: '3a1360c5-8e74-020b-566f-30ad61c6c28b',
                    },
                    {
                      tagName: '222222',
                      color: '#9DB4CA',
                      tagAuth: 0,
                      tagType: 0,
                      creationTime: '2024-06-25T16:52:38.552131',
                      creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      id: '3a1360c5-b210-7925-d772-6af01b258ba0',
                    },
                  ],
                  autoCompletion: false,
                  planSpendDay: 2,
                  realSpendDay: 2,
                  planSpendHours: 16,
                  realSpendHours: 16,
                  lastModificationTime: '2024-06-24T17:16:03.144445',
                  lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  creationTime: '2023-12-01T11:12:55.71408',
                  creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  id: '3a0f358a-4993-7b4f-7e89-872127943f29',
                },
                {
                  relatedId: null,
                  relatedName: null,
                  relatedType: 4,
                  subRelatedType: null,
                  i18NRelatedType: null,
                  i18NSubRelatedType: null,
                  parentTaskId: '3a0f358c-de8a-22ef-47c4-b0601bd89292',
                  parentTaskItemTitle: '111111111子任务',
                  preTaskItemId: null,
                  preTaskItemTitle: null,
                  title: '子任务的子任务2',
                  description: null,
                  priority: 2,
                  taskProgress: 0,
                  status: 20,
                  groupSort: 75,
                  statusSort: 3700,
                  responsibleOrganizeId: null,
                  checkerOrganizeId: null,
                  responsibleUserId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleUserName: 'limin',
                  responsibleUserAvatar:
                    '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleGroupId: '3a0b63ac-dfbc-bb17-3534-e2af0340c3f5',
                  responsibleGroup: {
                    name: '上线阶段',
                    color: null,
                    order: 1,
                    isDefault: false,
                    lastModificationTime: '2024-05-21T09:28:53.345811',
                    lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                    creationTime: '2023-05-25T14:02:05.118385',
                    creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                    id: '3a0b63ac-dfbc-bb17-3534-e2af0340c3f5',
                    extraProperties: {},
                  },
                  checkUsers: [],
                  followUsers: [
                    {
                      userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      name: 'limin',
                      avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                      creationTime: '2023-12-01T11:16:28.263358',
                    },
                  ],
                  planStartDate: '2024-06-15T00:00:00',
                  planStart: '2024-06-15',
                  planDoneDate: '2024-06-20T23:59:59',
                  planDone: '2024-06-20',
                  actualStartDate: '2024-01-25T13:27:15.947484',
                  actualDoneDate: null,
                  actualCheckDate: null,
                  actualCloseDate: null,
                  childCount: 0,
                  childCloseCount: 0,
                  canCreateChild: false,
                  creatorName: '超级管理员',
                  taskDocLinks: null,
                  taskItemTags: [],
                  autoCompletion: false,
                  planSpendDay: null,
                  realSpendDay: null,
                  planSpendHours: null,
                  realSpendHours: null,
                  lastModificationTime: '2024-06-24T18:21:59.037198',
                  lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  creationTime: '2023-12-01T11:16:28.263231',
                  creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  id: '3a0f358d-87d2-70d4-0d29-5084ea17a925',
                },
                {
                  relatedId: null,
                  relatedName: null,
                  relatedType: 4,
                  subRelatedType: null,
                  i18NRelatedType: null,
                  i18NSubRelatedType: null,
                  parentTaskId: null,
                  parentTaskItemTitle: '',
                  preTaskItemId: null,
                  preTaskItemTitle: null,
                  title: '测试',
                  description: null,
                  priority: 0,
                  taskProgress: 0,
                  status: 30,
                  groupSort: 6000,
                  statusSort: 5300,
                  responsibleOrganizeId: null,
                  checkerOrganizeId: null,
                  responsibleUserId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleUserName: 'limin',
                  responsibleUserAvatar:
                    '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleGroupId: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
                  responsibleGroup: {
                    name: '默认分组2233',
                    color: null,
                    order: 0,
                    isDefault: true,
                    lastModificationTime: '2024-05-21T14:11:46.298822',
                    lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                    creationTime: '2023-05-02T16:08:32.730807',
                    creatorId: null,
                    id: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
                    extraProperties: {},
                  },
                  checkUsers: [],
                  followUsers: [
                    {
                      userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      name: 'limin',
                      avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                      creationTime: '2023-12-20T16:44:58.213301',
                    },
                  ],
                  planStartDate: '2024-06-13T00:00:00',
                  planStart: '2024-06-13',
                  planDoneDate: '2024-06-21T23:59:59',
                  planDone: '2024-06-21',
                  actualStartDate: '2024-06-21T09:12:21.860946',
                  actualDoneDate: null,
                  actualCheckDate: null,
                  actualCloseDate: null,
                  childCount: 3,
                  childCloseCount: 0,
                  canCreateChild: false,
                  creatorName: 'limin',
                  taskDocLinks: null,
                  taskItemTags: [],
                  autoCompletion: false,
                  planSpendDay: null,
                  realSpendDay: null,
                  planSpendHours: null,
                  realSpendHours: null,
                  lastModificationTime: '2024-06-21T09:12:21.86718',
                  lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  creationTime: '2023-12-20T16:44:58.212752',
                  creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  id: '3a0f9893-1b97-e44d-a650-4d94be93cfb9',
                },
                {
                  relatedId: null,
                  relatedName: null,
                  relatedType: 4,
                  subRelatedType: null,
                  i18NRelatedType: null,
                  i18NSubRelatedType: null,
                  parentTaskId: null,
                  parentTaskItemTitle: '',
                  preTaskItemId: null,
                  preTaskItemTitle: null,
                  title: '测试任务ccc',
                  description: null,
                  priority: 1,
                  taskProgress: 0,
                  status: 40,
                  groupSort: 17300,
                  statusSort: 70800,
                  responsibleOrganizeId: null,
                  checkerOrganizeId: null,
                  responsibleUserId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleUserName: 'limin',
                  responsibleUserAvatar:
                    '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleGroupId: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
                  responsibleGroup: {
                    name: '默认分组2233',
                    color: null,
                    order: 0,
                    isDefault: true,
                    lastModificationTime: '2024-05-21T14:11:46.298822',
                    lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                    creationTime: '2023-05-02T16:08:32.730807',
                    creatorId: null,
                    id: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
                    extraProperties: {},
                  },
                  checkUsers: [
                    {
                      userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      name: 'limin',
                      avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                      actualCheckDate: null,
                    },
                    {
                      userId: '670775e0-0fee-f02c-da65-3a0de2a8caf9',
                      name: '孙雪雯',
                      avatar: null,
                      actualCheckDate: null,
                    },
                  ],
                  followUsers: [
                    {
                      userId: '670775e0-0fee-f02c-da65-3a0de2a8caf9',
                      name: '孙雪雯',
                      avatar: null,
                      creationTime: '2024-06-25T10:50:35.88388',
                    },
                    {
                      userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      name: 'limin',
                      avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                      creationTime: '2024-06-25T10:50:35.883851',
                    },
                  ],
                  planStartDate: '2024-06-19T00:00:00',
                  planStart: '2024-06-19 00:00',
                  planDoneDate: '2024-06-27T00:00:00',
                  planDone: '2024-06-27 00:00',
                  actualStartDate: null,
                  actualDoneDate: null,
                  actualCheckDate: null,
                  actualCloseDate: null,
                  childCount: 0,
                  childCloseCount: 0,
                  canCreateChild: false,
                  creatorName: 'limin',
                  taskDocLinks: null,
                  taskItemTags: [],
                  autoCompletion: false,
                  planSpendDay: null,
                  realSpendDay: null,
                  planSpendHours: null,
                  realSpendHours: null,
                  lastModificationTime: null,
                  lastModifierId: null,
                  creationTime: '2024-06-25T10:50:35.883595',
                  creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  id: '3a135f7a-3bdc-e8d4-7659-8c93c83f6beb',
                },
                {
                  relatedId: null,
                  relatedName: null,
                  relatedType: 4,
                  subRelatedType: null,
                  i18NRelatedType: null,
                  i18NSubRelatedType: null,
                  parentTaskId: null,
                  parentTaskItemTitle: '',
                  preTaskItemId: null,
                  preTaskItemTitle: null,
                  title: '测试任务',
                  description: null,
                  priority: 1,
                  taskProgress: 0,
                  status: 50,
                  groupSort: 18000,
                  statusSort: 71700,
                  responsibleOrganizeId: null,
                  checkerOrganizeId: null,
                  responsibleUserId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleUserName: 'limin',
                  responsibleUserAvatar:
                    '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleGroupId: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
                  responsibleGroup: {
                    name: '默认分组2233',
                    color: null,
                    order: 0,
                    isDefault: true,
                    lastModificationTime: '2024-05-21T14:11:46.298822',
                    lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                    creationTime: '2023-05-02T16:08:32.730807',
                    creatorId: null,
                    id: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
                    extraProperties: {},
                  },
                  checkUsers: [],
                  followUsers: [
                    {
                      userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      name: 'limin',
                      avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                      creationTime: '2024-06-25T16:17:32.751605',
                    },
                  ],
                  planStartDate: '2024-06-11T20:45:00',
                  planStart: '2024-06-11 20:45',
                  planDoneDate: '2024-06-21T00:00:00',
                  planDone: '2024-06-21 00:00',
                  actualStartDate: null,
                  actualDoneDate: null,
                  actualCheckDate: null,
                  actualCloseDate: null,
                  childCount: 0,
                  childCloseCount: 0,
                  canCreateChild: false,
                  creatorName: 'limin',
                  taskDocLinks: null,
                  taskItemTags: [],
                  autoCompletion: false,
                  planSpendDay: null,
                  realSpendDay: null,
                  planSpendHours: null,
                  realSpendHours: null,
                  lastModificationTime: null,
                  lastModifierId: null,
                  creationTime: '2024-06-25T16:17:32.75145',
                  creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  id: '3a1360a5-9043-6602-d6b7-f510937ee251',
                },
                {
                  relatedId: null,
                  relatedName: null,
                  relatedType: 4,
                  subRelatedType: null,
                  i18NRelatedType: null,
                  i18NSubRelatedType: null,
                  parentTaskId: null,
                  parentTaskItemTitle: '',
                  preTaskItemId: null,
                  preTaskItemTitle: null,
                  title: '测试任务',
                  description: null,
                  priority: 1,
                  taskProgress: 0,
                  status: 60,
                  groupSort: 18000,
                  statusSort: 71700,
                  responsibleOrganizeId: null,
                  checkerOrganizeId: null,
                  responsibleUserId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleUserName: 'limin',
                  responsibleUserAvatar:
                    '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleGroupId: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
                  responsibleGroup: {
                    name: '默认分组2233',
                    color: null,
                    order: 0,
                    isDefault: true,
                    lastModificationTime: '2024-05-21T14:11:46.298822',
                    lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                    creationTime: '2023-05-02T16:08:32.730807',
                    creatorId: null,
                    id: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
                    extraProperties: {},
                  },
                  checkUsers: [],
                  followUsers: [
                    {
                      userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      name: 'limin',
                      avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                      creationTime: '2024-06-25T16:17:32.751605',
                    },
                  ],
                  planStartDate: '2024-06-11T20:45:00',
                  planStart: '2024-06-11 20:45',
                  planDoneDate: '2024-06-21T00:00:00',
                  planDone: '2024-06-21 00:00',
                  actualStartDate: null,
                  actualDoneDate: null,
                  actualCheckDate: null,
                  actualCloseDate: null,
                  childCount: 0,
                  childCloseCount: 0,
                  canCreateChild: false,
                  creatorName: 'limin',
                  taskDocLinks: null,
                  taskItemTags: [],
                  autoCompletion: false,
                  planSpendDay: null,
                  realSpendDay: null,
                  planSpendHours: null,
                  realSpendHours: null,
                  lastModificationTime: null,
                  lastModifierId: null,
                  creationTime: '2024-06-25T16:17:32.75145',
                  creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  id: '3a1360a5-9043-6602-d6b7-f510937ee251',
                },
                {
                  relatedId: null,
                  relatedName: null,
                  relatedType: 4,
                  subRelatedType: null,
                  i18NRelatedType: null,
                  i18NSubRelatedType: null,
                  parentTaskId: null,
                  parentTaskItemTitle: '',
                  preTaskItemId: null,
                  preTaskItemTitle: null,
                  title: '测试任务',
                  description: null,
                  priority: 1,
                  taskProgress: 0,
                  status: 70,
                  groupSort: 18000,
                  statusSort: 71700,
                  responsibleOrganizeId: null,
                  checkerOrganizeId: null,
                  responsibleUserId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleUserName: 'limin',
                  responsibleUserAvatar:
                    '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                  responsibleGroupId: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
                  responsibleGroup: {
                    name: '默认分组2233',
                    color: null,
                    order: 0,
                    isDefault: true,
                    lastModificationTime: '2024-05-21T14:11:46.298822',
                    lastModifierId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                    creationTime: '2023-05-02T16:08:32.730807',
                    creatorId: null,
                    id: '3a0aedae-62a7-da05-a889-4a6c93203ee5',
                    extraProperties: {},
                  },
                  checkUsers: [],
                  followUsers: [
                    {
                      userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                      name: 'limin',
                      avatar: '/api/resourceFile/downloadUserAvatarContent?id=3a08bb39-64b3-9116-983e-e9327b78bd47',
                      creationTime: '2024-06-25T16:17:32.751605',
                    },
                  ],
                  planStartDate: '2024-06-11T20:45:00',
                  planStart: '2024-06-11 20:45',
                  planDoneDate: '2024-06-21T00:00:00',
                  planDone: '2024-06-21 00:00',
                  actualStartDate: null,
                  actualDoneDate: null,
                  actualCheckDate: null,
                  actualCloseDate: null,
                  childCount: 0,
                  childCloseCount: 0,
                  canCreateChild: false,
                  creatorName: 'limin',
                  taskDocLinks: null,
                  taskItemTags: [],
                  autoCompletion: false,
                  planSpendDay: null,
                  realSpendDay: null,
                  planSpendHours: null,
                  realSpendHours: null,
                  lastModificationTime: null,
                  lastModifierId: null,
                  creationTime: '2024-06-25T16:17:32.75145',
                  creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
                  id: '3a1360a5-9043-6602-d6b7-f510937ee251',
                },
              ],
      })),
    );
  }

  function handleDrop({ record }) {
    return Promise.resolve(record);
  }
  function handleResizes({ record }) {
    return Promise.resolve(record);
  }
  function handleMove({ record }) {
    return Promise.resolve(record);
  }
</script>
<style lang="less" scoped></style>
