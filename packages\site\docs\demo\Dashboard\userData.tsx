import { OtdTable } from '@otd/otd-ui';
import { RangePicker } from 'ant-design-vue';
import { ref } from 'vue';
import dayjs, {Dayjs} from 'dayjs';

export function useData() {
    const collectDashboard = ref<Record<string, any>>({});
    // 收集器
    function registerDashboard(key: string, dashboard) {
        collectDashboard.value[key] = dashboard;
    }
    const disabledDate = (current: Dayjs) => {
        return current && current >= dayjs().startOf('day');
    };
    const dateRangeEnum = {
        ThisWeek: '本周',
        LastWeek: '上周',
        ThisMonth: '本月',
        LastMonth: '上月',
    };
    // 预设时间范围快捷选择
    const dateRangeMap: Record<string, [Dayjs, Dayjs]> = {
        [dateRangeEnum.ThisWeek]: [dayjs().startOf('week'), dayjs().endOf('week')],
        [dateRangeEnum.LastWeek]: [
            dayjs().add(-1, 'week').startOf('week'),
            dayjs().add(-1, 'week').endOf('week'),
        ],
        [dateRangeEnum.ThisMonth]: [dayjs().startOf('month'), dayjs().endOf('month')],
        [dateRangeEnum.LastMonth]: [
            dayjs().add(-1, 'month').startOf('month'),
            dayjs().add(-1, 'month').endOf('month'),
        ],
    };
    const chartList = {
        // 关闭任务榜单
        TaskClosedThisWeek: {
            id: 'TaskClosedRanking',
            class: 'w-600px',
            component: OtdTable,
            title: '关闭任务榜单',
            option: {},
            isLock: false,
            hideFilter: false,
            componentProps: {
                canResize: true,
                pagination: false,
                bordered: true,
                indexColumnProps: {
                    width: 70,
                },
                columns: [
                    {
                        title: '负责人',
                        dataIndex: 'userName',
                    },
                    {
                        title: '数量',
                        dataIndex: 'count',
                    },
                ],
            },
            filter:{
                dateRange: [dayjs().add(-7, 'day').startOf('day'), dayjs().add(-1, 'day').endOf('day')],
            },
            filterRender: (filterForm, onChange) => {
                return (
                    <div class='otd-column-gap'>
                        <RangePicker
                            v-model:value={filterForm.dateRange}
                            disabled-date={disabledDate}
                            ranges={dateRangeMap}
                            allowClear={false}
                            onChange={onChange}
                        />
                    </div>
                );
            },
            request(filterValue) {
                console.log(filterValue)
                return Promise.resolve([])
            },
        },
    };
    return {
        registerDashboard, chartList,
    };
}