<template>
  <div :style="{ width: '200px' }">
    <OtdTree
      ref="treeRef"
      v-model:expandedKeys="expandedKeys"
      checkable
      :tree-data="treeData"
      :content-menu-actions="contentMenuActions"
    >
    </OtdTree>
  </div>
</template>
<script lang="ts" setup>
  import { OtdTree, MoreActionItem, message } from '@otd/otd-ui';
  import { onMounted, ref } from 'vue';

  const treeRef = ref();

  const treeData = [
    {
      title: '0-0',
      key: '0-0',
      children: [
        {
          title: '0-0-0',
          key: '0-0-0',
          children: [
            { title: '0-0-0-0', key: '0-0-0-0' },
            { title: '0-0-0-1', key: '0-0-0-1' },
            { title: '0-0-0-2', key: '0-0-0-2' },
          ],
        },
        {
          title: '0-0-1',
          key: '0-0-1',
          children: [
            { title: '0-0-1-0', key: '0-0-1-0' },
            { title: '0-0-1-1', key: '0-0-1-1' },
            { title: '0-0-1-2', key: '0-0-1-2' },
          ],
        },
      ],
    },
  ];

  onMounted(() => {
    treeRef.value.setCheckedKeys(['0-0-0', '0-0-0-0', '0-0-0-1', '0-0-0-2']);
  });

  const expandedKeys = ref<string[]>(['0-0-0', '0-0-1']);

  const contentMenuActions: MoreActionItem[] = [
    {
      id: 1,
      name: '编辑',
      icon: 'otd-icon-a-cateditsize24',
      action: (data) => {
        console.log(data);
        message.success('编辑');
      },
    },
    {
      id: 2,
      name: '删除',
      color: 'red',
      icon: 'otd-icon-a-catdeletesize24',
      action: () => {
        message.error('删除');
      },
    },
  ];
</script>
