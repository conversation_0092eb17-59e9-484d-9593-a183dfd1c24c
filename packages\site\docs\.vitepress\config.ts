import { version } from '../../package.json';
import { defineConfig } from 'vitepress';
import { vitepressDemo } from 'vite-plugin-vitepress-demo';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { resolve } from 'path';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';

function pathResolve(dir: string) {
  return resolve(process.cwd(), '../components', dir);
}

export function configSvgIconsPlugin(isBuild: boolean) {
  const svgIconsPlugin = createSvgIconsPlugin({
    iconDirs: [pathResolve('src/assets/svgs'), pathResolve('src/assets/images/emoticon')],
    svgoOptions: isBuild,
    // default
    symbolId: 'icon-[dir]-[name]',
  });
  return svgIconsPlugin;
}

export default defineConfig({
  vite: {
    resolve: {
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        {
          find: '@otd/otd-ui',
          replacement: 'otd-ui',
        },
        // /@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
      ],
    },
    plugins: [
      vueJsx(),
      vitepressDemo({
        glob: ['**/demo/**/*.vue'], // 指定需要处理的文件
      }),
      configSvgIconsPlugin(false),
    ] as any,
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {
            namespace: 'otd',
          },
          javascriptEnabled: true,
        },
      },
    },
  },
  appearance: true, // 夜间模式切换
  title: 'OTD UI',
  description: 'Vue基础组件库',
  lang: 'zh-CN',
  lastUpdated: true,
  head: [['link', { rel: 'icon', href: '/favico.png' }]],
  themeConfig: {
    siteTitle: 'OTD UI',
    logo: '/logo.svg',
    nav: [
      { text: '指南', link: '/' },
      { text: '组件', link: '/components/Table.html' },
      {
        text: version,
        items: [
          {
            text: '变更日志',
            link: '/changelog/CHANGELOG.md',
          },
        ],
      },
    ],
    sidebar: {
      '/components/': [
        {
          text: '基础组件',
          items: [
            { text: 'Board 看板', link: '/components/Board' },
            { text: 'CollaborationDocument 协作文档', link: '/components/CollaborationDocument' },
            { text: 'Collaborative 协作清单', link: '/components/Collaborative' },
            { text: 'Color 颜色拾取器', link: '/components/Color' },
            { text: 'Comment 评论', link: '/components/Comment' },
            { text: 'ConfigProvider 全局设置', link: '/components/ConfigProvider' },
            { text: 'Dashboard 仪表盘', link: '/components/Dashboard' },
            { text: 'DatePicker 日期选择', link: '/components/DatePicker' },
            { text: 'Emoticon 表情符号', link: '/components/Emoticon' },
            { text: 'Excel 表格转换', link: '/components/ExcelTransfer' },
            { text: 'Excel Excel操作', link: '/components/ExcelAction' },
            { text: 'FloatAction 悬浮操作', link: '/components/FloatAction' },
            { text: 'FolderMenu 文件夹目录', link: '/components/FolderMenu' },
            { text: 'History 历史记录', link: '/components/History' },
            { text: 'HistoryCard 历史卡片', link: '/components/HistoryCard' },
            { text: 'Layout 框架布局', link: '/components/Layout' },
            { text: 'LineUp 队列', link: '/components/LineUp' },
            { text: 'Loading 加载', link: '/components/Loading' },
            { text: 'MoreAction 下拉操作', link: '/components/MoreAction' },
            { text: 'Poster 海报', link: '/components/Poster' },
            { text: 'Preview 预览', link: '/components/Preview' },
            { text: 'Priority 优先级', link: '/components/Priority' },
            { text: 'Progress 进度', link: '/components/Progress' },
            { text: 'Scrollbar 滚动盒子', link: '/components/Scrollbar' },
            { text: 'SeniorFilter 高级筛选', link: '/components/SeniorFilter' },
            { text: 'ShowClosed 展示已关闭', link: '/components/ShowClosed' },
            { text: 'Status 状态', link: '/components/Status' },
            { text: 'Subtask 子任务', link: '/components/Subtask' },
            { text: 'SynchronizeAccounts 账号同步', link: '/components/SynchronizeAccounts' },
            { text: 'TabFilter 标签页筛选', link: '/components/TabFilter' },
            { text: 'Table 表格', link: '/components/Table' },
            { text: 'Tag 标签', link: '/components/Tag' },
            { text: 'TaskTemplate 任务模板', link: '/components/TaskTemplate' },
            { text: 'TaskType 任务类型', link: '/components/TaskType' },
            { text: 'Timeline 时间线', link: '/components/Timeline' },
            { text: 'Tinymce 富文本', link: '/components/Tinymce' },
            { text: 'TrackTime 工时', link: '/components/TrackTime' },
            { text: 'Tour 漫游式引导', link: '/components/Tour' },
            { text: 'Tree 树形控件', link: '/components/Tree' },
            { text: 'TrendList 趋势列表', link: '/components/TrendList' },
            { text: 'UploadFile 上传附件', link: '/components/UploadFile' },
            { text: 'UserSearch 角色搜索', link: '/components/UserSearch' },
            { text: 'WorkloadTimeline 工作量时间线', link: '/components/WorkloadTimeline' },
          ],
        },
      ],
    },
  },
});
