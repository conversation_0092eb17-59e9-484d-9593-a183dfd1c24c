<template>
  <OtdTabFilter
    ref="filterRef"
    :form-config="formSetting"
    :filter-actions="filterActions"
    filter-placement="right"
    bordered
    @submit="handleSubmit"
  />
  <OtdTabFilter :form-schemas="searchFormSchema" :filter-actions="filterActions" />
</template>
<script lang="tsx" setup>
  import { OtdTabFilter, FilterActionType, FormSchema, BasicFormProps } from '@otd/otd-ui';
  import { computed, onMounted, ref } from 'vue';

  const searchFormSchema: FormSchema[] = [
    {
      field: 'filter',
      label: '关键字',
      component: 'Input',
    },
    {
      field: 'time',
      label: '时间',
      component: 'RangePicker',
    },
    {
      field: 'select',
      label: '选择',
      component: 'Select',
      componentProps: {
        options: [
          { label: '标签1', value: 1 },
          { label: '标签2', value: 2 },
        ],
      },
    },
  ];
  const formSetting = computed<BasicFormProps>(() => ({
    autoSubmitOnEnter: true,
    autoSubmitOnChange: true,
    stopInputSubmitOnChange: true,
    schemas: searchFormSchema,
  }));
  const filterRef = ref();
  onMounted(() => {
    console.log(filterRef.value);
  });

  function handleSubmit(data) {
    console.log(data);
  }
  const filterActions: FilterActionType[] = [
    {
      id: 1,
      title: '筛选',
      icon: 'otd-icon-shaixuan',
      action: () => {
        console.log(1);
      },
    },
    {
      id: 2,
      title: '列设置',
      icon: 'otd-icon-lieshezhi',
      action: () => {
        console.log(2);
      },
    },
    {
      id: 3,
      title: '刷新',
      icon: 'otd-icon-refresh',
      action: () => {
        console.log(3);
      },
    },
  ];
</script>
<style lang="less" scoped></style>
