<template>
  <OtdUserSearch showText :getList="GetList" clearable />
  <OtdUserSearch showText clearable mode="multiple" :getList="GetList" />
  <OtdUserSearch isText hide-avatar show-arrow size="large" :getList="GetList" />
  <OtdUserSearch isText hide-avatar show-arrow mode="multiple" :getList="GetList" />
</template>
<script lang="ts" setup>
  import { OtdUserSearch } from '@otd/otd-ui';

  function GetList() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          items: [
            {
              name: '张三',
              id: 1,
              extraProperties: {
                Avatar:
                  'https://th.bing.com/th?id=OIP.MLH6YlnCVSWscmMC6N0CVgAAAA&w=250&h=250&c=8&rs=1&qlt=90&o=6&pid=3.1&rm=2',
              },
            },
            { name: '李四', id: 2, extraProperties: { Avatar: '' } },
            {
              name: '王五',
              id: 3,
              extraProperties: {
                Avatar:
                  'https://th.bing.com/th?id=OIP.4ebXWevcr6daWJ_rIvhgygHaHa&w=80&h=80&c=1&vt=10&bgcl=bd1082&r=0&o=6&pid=5.1',
              },
            },
            {
              name: '杨辉何',
              id: 4,
              extraProperties: {
                Avatar:
                  'https://th.bing.com/th?id=OIP.54qlbLNAZ64K94c_DCT-qAAAAA&w=166&h=166&c=8&rs=1&qlt=90&o=6&pid=3.1&rm=2',
              },
            },
            {
              name: '吴李高',
              id: 5,
              extraProperties: {
                Avatar:
                  'https://th.bing.com/th?id=OIP.ebDwpQ8AJ8ykQbZ-2VDoUwAAAA&w=250&h=250&c=8&rs=1&qlt=90&o=6&pid=3.1&rm=2',
              },
            },
            {
              name: '赵哈哈',
              id: 6,
              extraProperties: {
                Avatar:
                  'https://th.bing.com/th?id=OIP.ny2RhljTWP6TSo7y3eDCGAAAAA&w=166&h=171&c=8&rs=1&qlt=90&o=6&pid=3.1&rm=2',
              },
            },
            { name: '黄伟莉', id: 7, extraProperties: { Avatar: '' } },
            {
              name: '司徒四',
              id: 8,
              extraProperties: {
                Avatar: 'https://th.bing.com/th?id=OSK.********************************&w=80&h=80&c=7&o=6&pid=SANGAM',
              },
            },
            {
              name: '诸葛亮',
              id: 9,
              extraProperties: {
                Avatar:
                  'https://th.bing.com/th?id=OIP.aHmodTmqioy9Q5jXqenHwAAAAA&w=250&h=250&c=8&rs=1&qlt=90&o=6&pid=3.1&rm=2',
              },
            },
            {
              name: '李娜',
              id: 10,
              extraProperties: {
                Avatar:
                  'https://th.bing.com/th?id=OIP.gTqB_T2f7SHWtEYOWIyoKAAAAA&w=250&h=250&c=8&rs=1&qlt=90&o=6&pid=3.1&rm=2',
              },
            },
            { name: '上官赵四', id: 11, extraProperties: { Avatar: '' } },
            { name: '徐建华', id: 12, extraProperties: { Avatar: '' } },
          ],
        });
      }, 300);
    });
  }
</script>
<style lang="less" scoped></style>
