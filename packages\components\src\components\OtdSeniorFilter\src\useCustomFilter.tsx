import type { SeniorFilterEmitType, SeniorFilterPropsType } from '/@/components';
import { computed, getCurrentInstance, reactive, ref, unref } from 'vue';
import { buildUUID } from '/@/utils/uuid';
import { useI18n } from '/@/hooks/web/useI18n';
import { Select, Input } from 'ant-design-vue';
import { Recordable } from '/#/global';
import { unionWith } from 'lodash-es';

export enum FilterConditionEnum {
  IS = 'is',
}

export function getFilterCondition() {
  const { t } = useI18n();
  const { IS } = FilterConditionEnum;
  const FilterConditionEnumMap = {
    [IS]: { label: t('common.is'), value: IS, multiple: false },
  };
  return {
    FilterConditionEnumMap,
  };
}

export function useCustomFilter() {
  const { props, emit } = getCurrentInstance() as unknown as {
    emit: SeniorFilterEmitType;
    props: SeniorFilterPropsType;
  };
  const { t } = useI18n();
  const { FilterConditionEnumMap } = getFilterCondition();
  const SaveFilterRef = ref();
  // 筛选内容
  const filterContent = reactive<{ logic: string; data: Recordable[] }>({ logic: 'and', data: [] });
  const filterContentValue = computed(() => setFilterContent(unref(modelValue) ?? []));
  const optionsMap = computed(() => new Map(props.options?.map((item) => [item.id, item])));
  const modelValue = computed({
    get: () => props.value,
    set: (value) => emit('update:value', value),
  });
  const defaultFilterValue = { field: undefined, condition: undefined, value: undefined };
  const defaultCondition = [FilterConditionEnumMap[FilterConditionEnum.IS]];

  // 创建筛选项
  function createFilterItem(filter = {}) {
    return { id: buildUUID(), ...filter };
  }

  // 清空筛选条件
  function handleClearAll(isEmit = true) {
    props.options?.forEach((item) => {
      if (item.valueProps?.defaultData) {
        item.valueProps.defaultData = null;
      }
    });
    filterContent.data = [createFilterItem()];
    SaveFilterRef.value.saveFilter = null;
    if (isEmit) {
      handleEmitChange();
    }
  }

  function handleEmitChange() {
    const result = setFilterResult();
    emit('change', { type: 'filter', value: result });
  }

  // 添加新筛选条件
  function handleAddNewFilter() {
    filterContent.data.push(createFilterItem(defaultFilterValue));
  }
  // 保存筛选条件
  function handleSaveFilter() {
    SaveFilterRef.value.openSave();
  }

  // 删除筛选条件
  function handelDeleteFilter(index, row) {
    // 处理其它条件 Included,Not Included
    // if (row.condition !== 'is') {
    //   row?.field?.option.searchStatusHandler(row, 'is');
    // }

    if (row.field?.option?.valueProps?.defaultData) {
      row.field.option.valueProps.defaultData = null;
    }

    const record = filterContent.data.splice(index, 1)[0];
    const seat = filterContent.data.findIndex((item) => item.id === record.id);
    if (seat > -1) {
      filterContent.data.splice(seat, 1);
    }
    if (filterContent.data.length <= 0) {
      filterContent.data = [createFilterItem()];
      SaveFilterRef.value.saveFilter = null;
    }

    handleEmitChange();
  }

  // 处理选择字段
  function handelSelectField(item) {
    const options = getOptions(item, 'conditions', defaultCondition);
    item.condition = options?.[0]?.value;
    item.value = undefined;
  }

  // 获取配置中的选择项
  function getOptions(item, field: string, defaultValue?) {
    const option = item.field?.option;
    if (option?.mergeCondition) {
      return defaultValue.concat(option?.[field]);
    }
    return option?.[field] ?? defaultValue;
  }

  // 获取筛选组件
  function GetComponent({ item, onHandler, mode }) {
    const { valueComponent, valueProps, valueApi, type } = item.field?.option;
    if (!item.field?.option?.init) {
      item.field.option.init = true;
      valueApi?.();
    }
    return valueComponent ? (
      <valueComponent {...valueProps} v-model:value={item.value} onChange={onHandler} />
    ) : type === 'input' ? (
      <Input
        class="otd-input w-240px"
        placeholder={t('common.value')}
        v-model:value={item.value}
        onBlur={onHandler}
        onPressEnter={onHandler}
        type={valueProps?.number ? 'number' : 'text'}
      />
    ) : (
      <Select
        class="otd-select w-240px"
        placeholder={t('common.value')}
        v-model:value={item.value}
        notFoundContent={t('common.chooseText') + t('common.seniorFilter.field')}
        show-search
        mode={mode}
        options={getOptions(item, 'valueOptions')}
        onChange={onHandler}
      />
    );
  }

  // 处理筛选选择
  function handleChangeFilter(item) {
    const index = filterContent.data.findIndex((filter) => filter.id === item.id);
    index >= 0 ? (filterContent.data[index] = item) : filterContent.data.push(item);
    handleEmitChange();
  }

  // 处理其它条件 Included,Not Included
  function handelSelectCondition(_item) {
    // item.field.option.searchStatusHandler(item);
    // item.value = undefined;
  }

  // 处理筛选列表选项点击
  function handleFilterItemClick(row?) {
    setFilterContent(JSON.parse(row.text));
    setTimeout(() => {
      handleEmitChange();
    }, 100);
  }

  // 设置过滤结果
  function setFilterResult() {
    const result: Recordable[] = [];
    const first = filterContent.data[0];
    if (filterContent.data.length > 0 && first.value)
      filterContent.data.map(({ field, condition, value }) => {
        const { valueHandler, rangeKey, id } = field.option;
        if (rangeKey) {
          rangeKey.map((item, index) => {
            const content = valueHandler?.(value[index], index) ?? value[index];
            result.push({ field: item, value: content, id, condition, defaultValue: value });
          });
        } else {
          const content = valueHandler?.(value) ?? value;
          result.push({ field: field.value, value: content, id, condition, defaultValue: value });
        }
      });
    modelValue.value = result;
    return result;
  }

  // 设置筛选内容
  function setFilterContent(filters: Recordable[]) {
    const targetArr = unionWith(filters, ({ id, field }) => {
      const option = optionsMap.value.get(id)!;
      return option.rangeKey ? option.rangeKey.slice(1).includes(field) : false;
    }).map(({ id, field, condition, defaultValue }) => {
      const option = optionsMap.value.get(id)!;
      if (option.valueProps) {
        option.valueProps.defaultData = JSON.parse(JSON.stringify(defaultValue));
      }
      return createFilterItem({
        field: { value: field, label: option.label, key: field, option },
        condition,
        value: defaultValue,
      });
    });
    filterContent.data = targetArr.length ? targetArr : [createFilterItem()];
    return filterContent;
  }

  function getModeContent(item, options) {
    const data = options.find((option) => option.value === item.condition);
    // item.value = undefined
    return data.multiple ? 'multiple' : undefined;
  }

  return {
    filterContentValue,
    defaultFilterValue,
    modelValue,
    defaultCondition,
    SaveFilterRef,
    getOptions,
    handleClearAll,
    handleAddNewFilter,
    handleSaveFilter,
    handelDeleteFilter,
    handelSelectField,
    GetComponent,
    handleChangeFilter,
    handelSelectCondition,
    setFilterContent,
    handleFilterItemClick,
    getModeContent,
  };
}
