import type { DropMenu } from "/@/components/BasicDropdown";
import type { LocaleType, LangType } from "/#/config";
import { LocaleEnum } from "/@/enums/appEnum";

const { ZH_CN, EN_US } = LocaleEnum;

export const LANGMAP: { [key in LocaleType]?: LangType } = {
  [ZH_CN]: "zh-cn",
  [EN_US]: "en-us",
};

// locale list
export const localeList: DropMenu[] = [
  {
    text: "简体中文",
    event: ZH_CN,
  },
  {
    text: "English",
    event: EN_US,
  },
];
