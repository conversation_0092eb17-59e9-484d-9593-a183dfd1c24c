import { PropType } from 'vue';
import { ISetSubtaskInputDetail } from './type';
import { TableColumnPropsType } from '/@/components/OtdTable';
import { mutable } from '/@/utils/props';

export const getProps = () => ({
  value: {
    type: Array as PropType<ISetSubtaskInputDetail[]>,
  },
  isShowClosed: {
    type: Boolean,
    default: false,
  },
  headerText: {
    type: String,
  },
  tableColumn: {
    type: Array as PropType<TableColumnPropsType[]>,
    default: [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
  },
  parentTaskId: {
    type: String,
  },
  responsibleUserId: {
    type: String,
  },
  generateSubtask: {
    type: Function as PropType<(data?) => Promise<{ title: string }[]>>,
  },
  detail: {
    type: Object as PropType<Record<string, any>>,
    default: {},
  },
  updateSubtask: {
    type: Function as PropType<(data, key: string, newValue?: any, judged?: boolean) => Promise<any>>,
    required: true,
  },
});
const emit = ['update:value', 'delete', 'to-detail', 'sync', 'confirm-ai', 'getIsShowClosed'] as const;
export const getEmits = () => mutable(emit);
