<template>
  <!-- 历史记录 -->

  <div style="height: 600px">
    <OtdHistory
      :loading="loading"
      :data="data"
      label="关键字"
      placeholder="请输入关键词"
      popoverTitle="操作类型"
      popoverLabel="全部"
      :simple="isSimple"
      :fields="fieldObj"
    >
      <template #userTitle="{ data }">
        <div class="flex">
          <img class="img" style="width: 28px; height: 28px; border-radius: 50%" :src="data.creatorAvatar" alt="" />
          <p style="margin-left: 10px">{{ data.creatorName }}</p>
        </div>
      </template>
      <template #userContent>
        <p>工号：OTD034</p>
        <p>部门：研发部</p>
        <p>手机号：+86-18626118505</p>
        <p>企业邮箱：<EMAIL></p>
      </template>

      <template #contentTitle>内容标题 </template>
      <template #contentContent> 内容的内容 </template>
    </OtdHistory>
  </div>
  <h5 style="margin-bottom: 20px">点击下方按钮切换交互</h5>
  <div style="margin-bottom: 20px">
    <RadioGroup v-model:value="isSimple" name="radioGroup">
      <Radio :value="false">普通模式</Radio>
      <Radio :value="true">简易模式</Radio>
    </RadioGroup>
  </div>
</template>
<script lang="ts" setup>
  import { OtdHistory, Radio, RadioGroup } from '@otd/otd-ui';
  import { ref } from 'vue';
  const isSimple = ref(false);
  const loading = ref(true);
  const data = ref<any[]>([]);
  const fieldObj = ref({
    creatorAvatar: 'creatorAvatar',
    creatorName: 'creatorName',
    content: 'content',
    contentDetail: 'contentDetail',
  });

  setTimeout(() => {
    data.value = [
      {
        relatedId: '3a136b74-8c44-fee7-471e-cb117e449ccc',
        relatedTitle: '1212233',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Checker',
        actionType: 'HandOver',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-08-01T17:27:15.771573',
        prevValue: '{"Color":null,"Value":"3a141dea-9bd4-8171-3bd6-7fc20c46a863","Label":"提桶"}',
        newValue: '{"Color":null,"Value":"3a141deb-0423-97ea-f368-6f97987580fb","Label":"大饼"}',
        data: null,
      },
      {
        relatedId: '3a136b74-8c44-fee7-471e-cb117e449ccc',
        relatedTitle: '1212233',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Responsible',
        actionType: 'HandOver',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-08-01T17:27:12.955454',
        prevValue: '{"Color":null,"Value":"3a141dea-9bd4-8171-3bd6-7fc20c46a863","Label":"提桶"}',
        newValue: '{"Color":null,"Value":"3a141deb-0423-97ea-f368-6f97987580fb","Label":"大饼"}',
        data: null,
      },
      {
        relatedId: '3a136b74-8c44-fee7-471e-cb117e449ccc',
        relatedTitle: '1212233',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Creator',
        actionType: 'HandOver',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-08-01T17:26:45.038543',
        prevValue: '{"Color":null,"Value":"3a08bb39-64b3-9116-983e-e9327b78bd47","Label":"limin"}',
        newValue: '{"Color":null,"Value":"3a141dea-9bd4-8171-3bd6-7fc20c46a863","Label":"提桶"}',
        data: null,
      },
      {
        relatedId: '3a136b74-8c44-fee7-471e-cb117e449ccc',
        relatedTitle: '1212233',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Follower',
        actionType: 'HandOver',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-08-01T17:26:40.844381',
        prevValue: '{"Color":null,"Value":"3a08bb39-64b3-9116-983e-e9327b78bd47","Label":"limin"}',
        newValue: '{"Color":null,"Value":"3a141dea-9bd4-8171-3bd6-7fc20c46a863","Label":"提桶"}',
        data: null,
      },
      {
        relatedId: '3a136b74-8c44-fee7-471e-cb117e449ccc',
        relatedTitle: '1212233',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Follower',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-08-01T13:51:10.353648',
        prevValue: null,
        newValue: '[{"Color":null,"Value":"3a141dea-9bd4-8171-3bd6-7fc20c46a863","Label":"提桶"}]',
        data: null,
      },

      {
        relatedId: '3a13dc09-9056-ce10-e942-f8f0a50b0194',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'PreTask',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T15:20:25.435367',
        prevValue: '{"Color":null,"Value":"3a13dc09-9cce-c1ca-686c-f503048dabb5","Label":"33"}',
        newValue: '{"Color":null,"Value":"3a13dc09-9cce-c1ca-686c-f503048dabb5","Label":"33"}',
        data: null,
      },
      {
        relatedId: '3a13dc09-9056-ce10-e942-f8f0a50b0194',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'PreTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T15:20:24.4056',
        prevValue: '{"Color":null,"Value":"3a13dc09-cc27-0c6d-3095-c7e9ffd74bb2","Label":"55"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a13dc09-9056-ce10-e942-f8f0a50b0194',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'PreTask',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T15:20:22.954863',
        prevValue: '{"Color":null,"Value":"3a13dc09-cc27-0c6d-3095-c7e9ffd74bb2","Label":"55"}',
        newValue: '{"Color":null,"Value":"3a13dc09-cc27-0c6d-3095-c7e9ffd74bb2","Label":"55"}',
        data: null,
      },
      {
        relatedId: '3a13dc09-9056-ce10-e942-f8f0a50b0194',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'PreTask',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T15:20:12.87795',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a13dc09-9cce-c1ca-686c-f503048dabb5","Label":"33"}',
        data: null,
      },
      {
        relatedId: '3a13dbfb-907c-0965-a608-c295475daecf',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Checker',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T15:04:46.659506',
        prevValue: null,
        newValue:
          '[{"Color":null,"Value":"245875ae-10f9-f520-cdca-3a0de2a8c9e8","Label":"陈永琴"},{"Color":null,"Value":"670775e0-0fee-f02c-da65-3a0de2a8caf9","Label":"孙雪雯"},{"Color":null,"Value":"5873157c-943e-9b23-ed46-3a0de2a8ce36","Label":"张正花"},{"Color":null,"Value":"3a0e6d32-fa4d-f50d-bc46-7b4d9ad67446","Label":"adw2"}]',
        data: null,
      },
      {
        relatedId: '3a13dbcf-0e3b-46ab-28e7-fa410c913548',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Delete',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T14:21:48.233036',
        prevValue: '{"Color":null,"Value":"3a13dbcf-0e3b-46ab-28e7-fa410c913548","Label":"222"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a13dbcf-0e3b-46ab-28e7-fa410c913548',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Create',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T14:16:10.449414',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a13dbcf-0e3b-46ab-28e7-fa410c913548","Label":"222"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'AutoCompletion',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:19:02.273269',
        prevValue: '{"Color":null,"Value":"True","Label":null}',
        newValue: '{"Color":null,"Value":"False","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'AutoCompletion',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:19:01.348039',
        prevValue: '{"Color":null,"Value":"False","Label":null}',
        newValue: '{"Color":null,"Value":"True","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'TaskProgress',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:19:00.175666',
        prevValue: '{"Color":null,"Value":"0","Label":null}',
        newValue: '{"Color":null,"Value":"50","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Status',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:18:50.426422',
        prevValue: '{"Color":null,"Value":"20","Label":"Stopped"}',
        newValue: '{"Color":null,"Value":"30","Label":"OnGoing"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Status',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:18:49.326534',
        prevValue: '{"Color":null,"Value":"10","Label":"UnStarted"}',
        newValue: '{"Color":null,"Value":"20","Label":"Stopped"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'WorkSpend',
        actionType: 'PlanTaskHourDelete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:09:10.3789',
        prevValue: JSON.stringify([
          { Color: null, Value: '2024-07-17', Label: 'WorkDate' },
          { Color: null, Value: '14.3', Label: 'WorkHour' },
          { Color: null, Value: 'False', Label: 'IsShowWorkByDay' },
          { Color: null, Value: 'limin', Label: 'UserName' },
        ]),
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'WorkSpend',
        actionType: 'PlanTaskHourEditTime',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:09:07.469011',
        prevValue: JSON.stringify([
          { Color: null, Value: '2024-07-19', Label: 'WorkDate' },
          { Color: null, Value: '14.3', Label: 'WorkHour' },
          { Color: null, Value: 'False', Label: 'IsShowWorkByDay' },
          { Color: null, Value: 'limin', Label: 'UserName' },
        ]),
        newValue: JSON.stringify([
          { Color: null, Value: '2024-07-19', Label: 'WorkDate' },
          { Color: null, Value: '22.0', Label: 'WorkHour' },
          { Color: null, Value: 'False', Label: 'IsShowWorkByDay' },
          { Color: null, Value: '张三', Label: 'UserName' },
        ]),
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'WorkSpend',
        actionType: 'RealTaskHourEditRemark',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T16:12:04.986518',
        prevValue: null,
        newValue: JSON.stringify([
          { Color: null, Value: '2024-07-19', Label: 'WorkDate' },
          { Color: null, Value: 'limin', Label: 'UserName' },
          { Color: null, Value: '2222555', Label: 'Remark' },
        ]),
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'WorkSpend',
        actionType: 'PlanTaskHourCreate',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:09:02.232249',
        prevValue: null,
        newValue: JSON.stringify([
          { Color: null, Value: '2024-07-19', Label: 'WorkDate' },
          { Color: null, Value: '14.3', Label: 'WorkHour' },
          { Color: null, Value: 'False', Label: 'IsShowWorkByDay' },
          { Color: null, Value: 'limin', Label: 'UserName' },
        ]),
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'WorkSpend',
        actionType: 'PlanTaskHourCreate',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:09:02.208301',
        prevValue: null,
        newValue: JSON.stringify([
          { Color: null, Value: '2024-07-18', Label: 'WorkDate' },
          { Color: null, Value: '14.3', Label: 'WorkHour' },
          { Color: null, Value: 'False', Label: 'IsShowWorkByDay' },
          { Color: null, Value: 'limin', Label: 'UserName' },
        ]),
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'WorkSpend',
        actionType: 'PlanTaskHourCreate',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:09:02.180093',
        prevValue: null,
        newValue: JSON.stringify([
          { Color: null, Value: '2024-07-17', Label: 'WorkDate' },
          { Color: null, Value: '14.3', Label: 'WorkHour' },
          { Color: null, Value: 'False', Label: 'IsShowWorkByDay' },
          { Color: null, Value: 'limin', Label: 'UserName' },
        ]),
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'WorkSpend',
        actionType: 'RealTaskHourDelete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:08:54.103771',
        prevValue: JSON.stringify([
          { Color: null, Value: '2024-07-18', Label: 'WorkDate' },
          { Color: null, Value: '14.3', Label: 'WorkHour' },
          { Color: null, Value: 'False', Label: 'IsShowWorkByDay' },
          { Color: null, Value: 'limin', Label: 'UserName' },
        ]),
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'WorkSpend',
        actionType: 'RealTaskHourEditTime',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:08:50.8661',
        prevValue: JSON.stringify([
          { Color: null, Value: '2024-07-19', Label: 'WorkDate' },
          { Color: null, Value: '16.5', Label: 'WorkHour' },
          { Color: null, Value: 'False', Label: 'IsShowWorkByDay' },
          { Color: null, Value: 'limin', Label: 'UserName' },
        ]),
        newValue: JSON.stringify([
          { Color: null, Value: '2024-07-19', Label: 'WorkDate' },
          { Color: null, Value: '22.0', Label: 'WorkHour' },
          { Color: null, Value: 'False', Label: 'IsShowWorkByDay' },
          { Color: null, Value: '张三', Label: 'UserName' },
        ]),
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'WorkSpend',
        actionType: 'RealTaskHourCreate',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:08:46.349972',
        prevValue: null,
        newValue: JSON.stringify([
          { Color: null, Value: '2024-07-19', Label: 'WorkDate' },
          { Color: null, Value: '16.5', Label: 'WorkHour' },
          { Color: null, Value: 'False', Label: 'IsShowWorkByDay' },
          { Color: null, Value: 'limin', Label: 'UserName' },
        ]),
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'WorkSpend',
        actionType: 'RealTaskHourCreate',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:08:46.319602',
        prevValue: null,
        newValue: JSON.stringify([
          { Color: null, Value: '2024-07-18', Label: 'WorkDate' },
          { Color: null, Value: '16.5', Label: 'WorkHour' },
          { Color: null, Value: 'False', Label: 'IsShowWorkByDay' },
          { Color: null, Value: 'limin', Label: 'UserName' },
        ]),
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Comment',
        actionType: 'CommentEmojiReply',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:08:27.498825',
        prevValue: null,
        newValue: JSON.stringify([
          {
            Color: null,
            Value:
              "<p>ewrwerwerw<a class='otd-at-mention' style='color: #003de8; font-family: data-3a08bb39-64b3-9116-983e-e9327b78bd47-id;' contenteditable='false' data-type='User' data-id='3a08bb39-64b3-9116-983e-e9327b78bd47'>@limin</a></p>",
            Label: 'Content',
          },
          { Color: null, Value: '3a13dab5-2e88-8316-08ba-1088c216eb4e', Label: 'CommentId' },
          { Color: null, Value: 'Like', Label: 'Emoji' },
        ]),
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Comment',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T16:38:00.777705',
        prevValue:
          '{"Color":null,"Value":"3a13dc50-abdd-80be-43bf-d4cef2e47544","Label":"<p>22222<img src=\\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJgAAACDCAYAAABx09J5AAACT0lEQVR4Ae3SMQ0AAAzDsPEn3aHI5wLoEflmCoQFLvx2rcAAgyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAt8AkW+vxnXpasAAAAAElFTkSuQmCC\\" /></p>"}',
        newValue:
          '{"Color":null,"Value":"3a13dc50-abdd-80be-43bf-d4cef2e47544","Label":"<p>222224444<img src=\\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJgAAACDCAYAAABx09J5AAACT0lEQVR4Ae3SMQ0AAAzDsPEn3aHI5wLoEflmCoQFLvx2rcAAgyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAtAFia1zlgDKQFAEvzOgeMgbQAYGle54AxkBYALM3rHDAG0gKApXmdA8ZAWgCwNK9zwBhICwCW5nUOGANpAcDSvM4BYyAt8AkW+vxnXpasAAAAAElFTkSuQmCC\\" /></p>"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Comment',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:08:16.76125',
        prevValue: null,
        newValue:
          '{"Color":null,"Value":"3a13dab5-2e88-8316-08ba-1088c216eb4e","Label":"<p>ewrwerwerw<a class=\\"otd-at-mention\\" style=\\"color: #003de8; font-family: data-3a08bb39-64b3-9116-983e-e9327b78bd47-id;\\" contenteditable=\\"false\\" data-type=\\"User\\" data-id=\\"3a08bb39-64b3-9116-983e-e9327b78bd47\\">@limin</a></p>"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'File',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:07:59.740803',
        prevValue: null,
        newValue:
          '{"Color":null,"Value":"/api/resourceFile/download?id=3a13dab4-ec0f-ee4f-7862-58c7ae0c918f","Label":"1-200514232F3"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'File',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:07:56.873261',
        prevValue:
          '{"Color":null,"Value":"/api/resourceFile/download?id=3a13dab4-d2e2-2cb9-1f05-c40e6d499070","Label":"1"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'File',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:07:53.350141',
        prevValue: null,
        newValue:
          '{"Color":null,"Value":"/api/resourceFile/download?id=3a13dab4-d2e2-2cb9-1f05-c40e6d499070","Label":"1"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:07:37.719574',
        prevValue: '{"Color":null,"Value":"3a13dab4-20f4-f383-3f4e-21fbdce33006","Label":"555555"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:07:07.649929',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a13dab4-20f4-f383-3f4e-21fbdce33006","Label":"555555"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:06:57.831356',
        prevValue: '{"Color":null,"Value":"66666","Label":null}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T11:29:44.665355',
        prevValue: '{"Color":null,"Value":"66666","Label":"666"}',
        newValue: '{"Color":null,"Value":"666664444","Label":"666"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:06:57.824622',
        prevValue: null,
        newValue: '{"Color":null,"Value":"66666","Label":"666"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:06:55.962265',
        prevValue: null,
        newValue: '{"Color":null,"Value":"66666","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:06:53.508808',
        prevValue: '{"Color":null,"Value":"666","Label":"666"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:06:51.99868',
        prevValue: '{"Color":null,"Value":"4444","Label":"444"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:35.234628',
        prevValue: '{"Color":null,"Value":"666","Label":null}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:35.223477',
        prevValue: '{"Color":null,"Value":"4444","Label":"444"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:35.216024',
        prevValue: null,
        newValue: '{"Color":null,"Value":"666","Label":"666"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:35.20842',
        prevValue: null,
        newValue: '{"Color":null,"Value":"4444","Label":"444"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:34.301666',
        prevValue: '{"Color":null,"Value":"4444","Label":"444"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:34.295577',
        prevValue: null,
        newValue: '{"Color":null,"Value":"666","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:34.288848',
        prevValue: null,
        newValue: '{"Color":null,"Value":"4444","Label":"444"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:31.154619',
        prevValue: '{"Color":null,"Value":"4444","Label":null}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:31.14545',
        prevValue: null,
        newValue: '{"Color":null,"Value":"4444","Label":"444"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Document',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:29.343651',
        prevValue: null,
        newValue: '{"Color":null,"Value":"4444","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'UpdateDescription',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:24.884343',
        prevValue: null,
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'UpdateDescription',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:20.452745',
        prevValue: null,
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'UpdateDescription',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:05:09.997794',
        prevValue: null,
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'UpdateDescription',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:57.241448',
        prevValue: null,
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Collaboration',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:47.512477',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a136f10-95b9-0f74-1c0b-e0c242717d70","Label":"122222"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Collaboration',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:46.666205',
        prevValue: '{"Color":null,"Value":"3a136c6d-7b41-b907-9a0b-e05f395f40c0","Label":"112"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Collaboration',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:45.716589',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a136c6d-7b41-b907-9a0b-e05f395f40c0","Label":"112"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Collaboration',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:45.703231',
        prevValue: '{"Color":null,"Value":"3a136f10-95b9-0f74-1c0b-e0c242717d70","Label":"122222"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Collaboration',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:44.657205',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a136f10-95b9-0f74-1c0b-e0c242717d70","Label":"122222"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'PlanDoneDate',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:42.06129',
        prevValue: null,
        newValue: '{"Color":null,"Value":"24/07/18 23:59","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'PlanDoneDate',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:39.070689',
        prevValue: '{"Color":null,"Value":"24/07/20 00:45","Label":null}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'PlanStartDate',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:38.306079',
        prevValue: '{"Color":null,"Value":"24/07/16 00:45","Label":null}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'PlanDoneDate',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:37.148382',
        prevValue: '{"Color":null,"Value":"24/07/20 23:59","Label":null}',
        newValue: '{"Color":null,"Value":"24/07/20 00:45","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'PlanStartDate',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:35.962359',
        prevValue: '{"Color":null,"Value":"24/07/16 00:00","Label":null}',
        newValue: '{"Color":null,"Value":"24/07/16 00:45","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'PlanDoneDate',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:34.457406',
        prevValue: null,
        newValue: '{"Color":null,"Value":"24/07/20 23:59","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'PlanStartDate',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:33.949295',
        prevValue: null,
        newValue: '{"Color":null,"Value":"24/07/16 00:00","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Follower',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:30.135976',
        prevValue: null,
        newValue: '[{"Color":null,"Value":"3a08bb39-64b3-9116-983e-e9327b78bd47","Label":"limin"}]',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Follower',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:29.212907',
        prevValue: '[{"Color":null,"Value":"3a08bb39-64b3-9116-983e-e9327b78bd47","Label":"limin"}]',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Follower',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:28.76568',
        prevValue: '[{"Color":null,"Value":"670775e0-0fee-f02c-da65-3a0de2a8caf9","Label":"孙雪雯"}]',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Follower',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:28.366964',
        prevValue: '[{"Color":null,"Value":"245875ae-10f9-f520-cdca-3a0de2a8c9e8","Label":"陈永琴"}]',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Follower',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:27.406112',
        prevValue: null,
        newValue: '[{"Color":null,"Value":"670775e0-0fee-f02c-da65-3a0de2a8caf9","Label":"孙雪雯"}]',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Follower',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:26.981008',
        prevValue: null,
        newValue: '[{"Color":null,"Value":"245875ae-10f9-f520-cdca-3a0de2a8c9e8","Label":"陈永琴"}]',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Checker',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:24.634556',
        prevValue: null,
        newValue: '[{"Color":null,"Value":"245875ae-10f9-f520-cdca-3a0de2a8c9e8","Label":"陈永琴"}]',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Checker',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:23.6758',
        prevValue: '[{"Color":null,"Value":"670775e0-0fee-f02c-da65-3a0de2a8caf9","Label":"孙雪雯"}]',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Checker',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:22.991352',
        prevValue: '[{"Color":null,"Value":"245875ae-10f9-f520-cdca-3a0de2a8c9e8","Label":"陈永琴"}]',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Checker',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:22.25229',
        prevValue: null,
        newValue: '[{"Color":null,"Value":"670775e0-0fee-f02c-da65-3a0de2a8caf9","Label":"孙雪雯"}]',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Checker',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:21.533037',
        prevValue: null,
        newValue: '[{"Color":null,"Value":"245875ae-10f9-f520-cdca-3a0de2a8c9e8","Label":"陈永琴"}]',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Responsible',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:19.48707',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a08bb39-64b3-9116-983e-e9327b78bd47","Label":"limin"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Responsible',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:18.619542',
        prevValue: '{"Color":null,"Value":"3a08bb39-64b3-9116-983e-e9327b78bd47","Label":"limin"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Responsible',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:17.495221',
        prevValue: '{"Color":null,"Value":"245875ae-10f9-f520-cdca-3a0de2a8c9e8","Label":"陈永琴"}',
        newValue: '{"Color":null,"Value":"3a08bb39-64b3-9116-983e-e9327b78bd47","Label":"limin"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Responsible',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:16.104882',
        prevValue: '{"Color":null,"Value":"3a08bb39-64b3-9116-983e-e9327b78bd47","Label":"limin"}',
        newValue: '{"Color":null,"Value":"245875ae-10f9-f520-cdca-3a0de2a8c9e8","Label":"陈永琴"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Priority',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:13.52475',
        prevValue: null,
        newValue: '{"Color":null,"Value":"2","Label":"High"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Priority',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:11.026114',
        prevValue: '{"Color":null,"Value":"3","Label":"Urgent"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Priority',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:04:09.254441',
        prevValue: '{"Color":null,"Value":"1","Label":"Normal"}',
        newValue: '{"Color":null,"Value":"3","Label":"Urgent"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Tags',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T13:20:10.39964',
        prevValue: '{"Color":"#00f","Value":"3a13db99-7f07-3b26-f499-83090ba34e5f","Label":"3333333"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'Tags',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:03:22.366672',
        prevValue: null,
        newValue: '{"Color":"#00f","Value":"3a13dab0-b0c8-899b-a1e8-3ce16f2cdb4f","Label":"444444"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'UpdateTitle',
        actionType: 'Edit',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-19T09:03:14.836821',
        prevValue: '{"Color":null,"Value":"sd ","Label":null}',
        newValue: '{"Color":null,"Value":"sd 33","Label":null}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:18:04.676653',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a13d6aa-73ed-abc4-cc96-68bdea696661","Label":"测试一下有头像的人"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:17:55.380559',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a13d6aa-4faa-fd1b-3ab7-6c1ccfb94672","Label":"crm-项目重构"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:17:55.238173',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a13d6aa-4f1b-eec5-0f18-13ee4edac6a2","Label":"crm-资质列表的编辑的附件"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:17:55.065895',
        prevValue: null,
        newValue:
          '{"Color":null,"Value":"3a13d6aa-4e6b-0362-d93e-fd348f9fed97","Label":"新太阳-商机处的表单label需要更改"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:15:52.800124',
        prevValue: '{"Color":null,"Value":"3a13d6a7-209e-b78e-3143-3a1b732b9951","Label":"crm-资质列表的编辑的附件"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:15:50.965747',
        prevValue:
          '{"Color":null,"Value":"3a13d6a7-2006-d17e-5bea-40904c5b99e6","Label":"新太阳-商机处的表单label需要更改"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:14:26.730625',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a13d6a7-209e-b78e-3143-3a1b732b9951","Label":"crm-资质列表的编辑的附件"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:14:26.579681',
        prevValue: null,
        newValue:
          '{"Color":null,"Value":"3a13d6a7-2006-d17e-5bea-40904c5b99e6","Label":"新太阳-商机处的表单label需要更改"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:14:18.579805',
        prevValue: '{"Color":null,"Value":"3a13d6a1-5c7d-2d66-bffb-0a0cef3f38b0","Label":"测试一下有头像的人"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:14:17.045015',
        prevValue: '{"Color":null,"Value":"3a13d6a1-5bd1-91e1-0e32-9ce237194d4c","Label":"crm-项目重构"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:14:15.286264',
        prevValue: '{"Color":null,"Value":"3a13d6a1-5b10-a321-78c5-19abe639fd15","Label":"crm-资质列表的编辑的附件"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:14:13.28446',
        prevValue:
          '{"Color":null,"Value":"3a13d69f-c8f6-1587-2c45-010787151e7a","Label":"新太阳-商机处的表单label需要更改"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:08:08.843102',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a13d6a1-5c7d-2d66-bffb-0a0cef3f38b0","Label":"测试一下有头像的人"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:08:08.66867',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a13d6a1-5bd1-91e1-0e32-9ce237194d4c","Label":"crm-项目重构"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:08:08.480779',
        prevValue: null,
        newValue: '{"Color":null,"Value":"3a13d6a1-5b10-a321-78c5-19abe639fd15","Label":"crm-资质列表的编辑的附件"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Create',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:06:25.571893',
        prevValue: null,
        newValue:
          '{"Color":null,"Value":"3a13d69f-c8f6-1587-2c45-010787151e7a","Label":"新太阳-商机处的表单label需要更改"}',
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:06:14.46969',
        prevValue: '{"Color":null,"Value":"3a13d680-cc5a-f94b-97e8-609af2278372","Label":"测试一下有头像的人"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:06:12.779315',
        prevValue: '{"Color":null,"Value":"3a13d680-cbd1-e6c2-d999-e3f02e742cc4","Label":"crm-项目重构"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:06:11.102388',
        prevValue: '{"Color":null,"Value":"3a13d680-cb36-1be2-5d74-fcfe7c86e908","Label":"crm-资质列表的编辑的附件"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: null,
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: 'Delete',
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T14:06:09.228985',
        prevValue:
          '{"Color":null,"Value":"3a13d680-ca3e-5884-34c9-c1e2c8e48cde","Label":"新太阳-商机处的表单label需要更改"}',
        newValue: null,
        data: null,
      },
      {
        relatedId: '3a136b47-2918-43e6-abb6-efddf3ecf892',
        content: '添加了子任务 测试一下有头像的人',
        module: 'TMS',
        objectName: 'TaskItem',
        type: 'ChildTask',
        actionType: null,
        creatorName: 'limin',
        creatorId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
        creationTime: '2024-07-18T13:32:34.798897',
        prevValue: null,
        newValue: null,
        data: null,
      },
    ];
    loading.value = false;
  }, 500);
</script>
<style lang="less" scoped></style>
