<template>
  <OtdSynchronizeAccounts ref="SynchronizeAccountsRef" :getList="getList" :batchUpdateUser="batchUpdateUser" />
  <Button preIcon="ant-design:plus-circle-outlined" type="primary" @click="handleUpload"> 同步账号 </Button>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { OtdSynchronizeAccounts, Button } from '@otd/otd-ui';
  import { message } from 'ant-design-vue';
  const SynchronizeAccountsRef = ref();

  function handleUpload() {
    SynchronizeAccountsRef.value.show();
  }

  function getList() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '3a1239f6-7127-a8bd-e941-ff374ed4b158',
            orgUnits: [
              {
                id: '3a1239f6-72fd-77bb-c526-7e060fcfca97',
                displayName: '企微测试1',
              },
            ],
            name: '曌云裳',
            email: '<EMAIL>',
            phoneNumber: '**********',
            isActive: true,
            creationTime: '2024-04-29T10:57:54.621186',
          },
          {
            id: '3a12ab63-39bd-2468-266b-492c202a7843',
            orgUnits: [
              {
                id: '3a12ab5f-5bef-7924-7db8-3f704f2ab78d',
                displayName: '岁月轮',
              },
              {
                id: '3a12ab5f-5f68-d17a-e68e-e2c2ef395475',
                displayName: '剑决',
              },
            ],
            name: '柳生剑影',
            email: '<EMAIL>',
            phoneNumber: '',
            isActive: true,
            creationTime: '2024-05-21T11:33:49.249497',
          },
          {
            id: '3a12ab63-413c-ba96-d73d-3be0a4b7ddf4',
            orgUnits: [
              {
                id: '3a12ab5f-5db3-741b-f451-3a6102027ac3',
                displayName: '飞天仙流',
              },
            ],
            name: '赤宵练',
            email: '<EMAIL>',
            phoneNumber: '135555',
            isActive: true,
            creationTime: '2024-05-21T11:33:51.166621',
          },
          {
            id: '3a12ab63-48ba-676d-11b0-aa94d469bd66',
            orgUnits: [],
            name: '天草二十六',
            email: '<EMAIL>',
            phoneNumber: '12222222222',
            isActive: true,
            creationTime: '2024-05-21T11:33:53.081847',
          },
          {
            id: '3a12ab63-5057-4303-4879-876855685787',
            orgUnits: [
              {
                id: '3a12ab5f-6124-75f1-3b6d-596733d13d59',
                displayName: '无禁御手',
              },
            ],
            name: '易水心',
            email: '<EMAIL>',
            phoneNumber: '1133355',
            isActive: true,
            creationTime: '2024-05-21T11:33:55.032293',
          },
          {
            id: '3a132285-ac4a-5019-65d6-60de966e22ef',
            orgUnits: [
              {
                id: '3a12ab5f-59b8-0865-0c7e-8f67429baa51',
                displayName: '雪拥冰梅',
              },
            ],
            name: '楼无痕',
            email: 'lwh@123com',
            phoneNumber: '144444444',
            isActive: true,
            creationTime: '2024-06-13T14:46:15.530126',
          },
          {
            id: '3a1322b2-d00f-2f8a-66d3-d8cb62311f22',
            orgUnits: [
              {
                id: '3a12ab5f-5bef-7924-7db8-3f704f2ab78d',
                displayName: '岁月轮',
              },
            ],
            name: '天草二十六',
            email: '<EMAIL>',
            phoneNumber: '11111111111',
            isActive: true,
            creationTime: '2024-06-13T15:35:33.755108',
          },
          {
            id: '3a132701-d00a-f476-fa3a-01e235579c9e',
            orgUnits: [
              {
                id: '3a12ab5f-6124-75f1-3b6d-596733d13d59',
                displayName: '无禁御手',
              },
            ],
            name: '伊达我流',
            email: '<EMAIL>',
            phoneNumber: '1333333333',
            isActive: true,
            creationTime: '2024-06-14T11:40:19.968167',
          },
          {
            id: '3a133604-4fc0-3987-c3aa-230bf8941b9a',
            orgUnits: [],
            name: 'test111',
            email: '1@qywx.com1',
            phoneNumber: '',
            isActive: true,
            creationTime: '2024-06-17T09:37:21.961273',
          },
        ]);
      }, 1000);
    });
  }

  function batchUpdateUser(saveList) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log(saveList);
        resolve('');
        message.success('操作成功');
      }, 500);
    });
  }
</script>

<style scoped lang="less"></style>
