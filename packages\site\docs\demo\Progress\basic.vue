<template>
  <!-- 任务进度 -->
  <OtdProgress
    v-model:value="progress"
    tip="设置任务进度"
    title="任务进度"
    :disabled="false"
    @confirm="handleSetProgress"
  />
</template>
<script lang="ts" setup>
  import { OtdProgress } from '@otd/otd-ui';
  import { ref } from 'vue';
  const progress = ref(60);

  // 设置任务进度
  function handleSetProgress(val: number) {
    console.log(val);
  }
</script>
<style lang="less" scoped></style>
