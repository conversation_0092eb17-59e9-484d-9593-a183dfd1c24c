<template>
  <div
    class="otd-emoticon-icon"
    :class="{ 'is-icon': !!icon }"
    :style="{
      '--size': size + 'px',
      '--x': offset[0] + '%',
      '--y': offset[1] + '%',
      backgroundImage: icon ? `url(${icon})` : undefined,
    }"
  ></div>
</template>
<script lang="ts" setup>
  import { PropType } from 'vue';

  defineProps({
    offset: {
      type: Array as unknown as PropType<[number, number]>,
      default: () => [0, 0],
    },
    icon: {
      type: String,
    },
    size: {
      type: Number,
      default: 30,
    },
  });
</script>
<style lang="less" scoped>
  .otd-emoticon-icon {
    background-image: url('../../../assets/images/emoticon/basic.png');
    background-size: 1284% 1142%;
    background-position: var(--x) var(--y);
    background-repeat: no-repeat;
    width: var(--size);
    min-width: var(--size);
    height: var(--size);
    line-height: 1;
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    &.is-icon {
      background-size: 100%;
    }
  }
</style>
