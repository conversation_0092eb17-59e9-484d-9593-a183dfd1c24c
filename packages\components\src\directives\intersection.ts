import { App, Directive, unref } from 'vue';

const intersectionDirective: Directive = {
  mounted(el, binding) {
    el.__observer = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === 'attributes' && mutation.attributeName?.startsWith('data-')) {
          const attributeName = mutation.attributeName;
          const name = attributeName.split('-')[1];
          const newValue = el.getAttribute(attributeName);
          unref(binding.value)[`${name}Row`] = Math.max(Number(newValue), 0);
        }
      }
    });
    el.__observer.observe(el, {
      attributes: true, // Observe attribute changes
      attributeFilter: ['data-start', 'data-end'], // Observe only data- attributes
    });
  },
  beforeUnmount(el) {
    el.__observer?.disconnect();
  },
};

export function setupIntersectionDirective(app: App) {
  app.directive('intersection', intersectionDirective);
}

export default intersectionDirective;
