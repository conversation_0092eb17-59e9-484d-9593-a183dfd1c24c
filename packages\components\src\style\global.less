html,
body {
  height: 100%;
  width: 100%;
  overflow: visible;
  overflow-x: hidden;
}

body {
  color: var(--otd-basic-text);
}

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-corner {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--otd-scroll-bg);
  border-radius: 8px;
  box-shadow: inset 0 0 6px rgb(0 0 0 / 20%);
}

::-webkit-scrollbar-thumb:hover {
  background-color: #b6b7b9;
}

// 折叠板样式
@collapse: ~'@{namespace}-table-collapse';

.@{collapse} {
  border-width: 0;
  background-color: unset;

  .@{collapse}-panel {
    border-bottom: unset;

    .ant-collapse-header-text {
      position: relative;
    }
  }
}

.@{collapse}-panel {
  &+& {
    margin-top: 16px;
  }
}

// 箭头样式
@arrow: ~'@{namespace}-arrow';

.@{arrow} {
  float: left;
  padding: 4px;
  cursor: pointer;
  line-height: 1;
  height: fit-content;
  min-width: 20px;
  min-height: 1px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  transform: scale(0.8);

  &.otd-icon-a-Statusdefault2,
  &.otd-icon-a-Statusdefault,
  &.otd-icon-a-Statusactive {
    font-size: 13px !important;
    color: var(--otd-icon-text) !important;
    // background-color: var(--otd-icon-bg);
    border-radius: var(--otd-small-radius);

    &:hover {
      background-color: var(--otd-border-gray);
    }

    &::before {
      display: inline-flex;
      // transition: 0.3s transform;
    }

    &.@{arrow}-active {
      &::before {
        transform: rotate(90deg);
      }
    }
  }
}

//状态
.@{namespace}-status-popover {
  .ant-popover-inner {
    box-shadow: none;
    padding: 10px 16px 12px 16px;
    border: 1px solid #e0e0e0;
  }

  .ant-popover-arrow {
    display: none;
  }
}

// 鼠标浮动样式
.placeholder-hover {
  padding: 4px;
  border-radius: var(--otd-small-radius);
  cursor: pointer;
  line-height: 16px;
  display: inline-flex;
  width: fit-content;
  align-items: center;
  border: 1px solid transparent;
  column-gap: 4px;

  &:hover {
    background-color: var(--otd-gray3-hover);
  }
}

// 禁用鼠标浮动样式
.placeholder-disabled {
  cursor: not-allowed;
  background-color: transparent;

  .placeholder-hover {
    cursor: not-allowed;
    background-color: transparent;
  }
}

// 下拉菜单
.otd-dropdown {
  min-width: fit-content;

  &.ant-tree-select-dropdown {
    border-radius: var(--otd-border-radius);
    padding: 8px;

    .ant-select-tree-node-content-wrapper {
      padding: 0 8px;
      border-radius: var(--otd-border-radius);

      &.ant-select-tree-node-selected {
        background-color: var(--otd-primary-main);
        color: var(--otd-white-text);
      }
    }
  }

  .ant-dropdown-menu {
    border-radius: var(--otd-border-radius);
  }

  .ant-dropdown-menu-item-disabled {
    color: var(--otd-dropdown-menu-item-disabled) !important;
  }

  .ant-dropdown-menu {
    padding: 4px 4px 6px 4px;

    .ant-dropdown-menu-item {
      min-width: 160px;
      padding: 8px;
      line-height: 20px;

      &:hover {
        background-color: var(--otd-gray-hover);
      }

      &.ant-dropdown-menu-item-selected {
        background-color: var(--otd-basic-active);
        color: var(--otd-basic-text);
      }

      .ant-dropdown-menu-item-icon {
        font-size: 18px;
        width: 18px;
      }
    }
  }
}

// 气泡卡片样式
@popover: ~'@{namespace}-popover';

.@{popover}.ant-popover,
.@{popover}.ant-dropdown {
  min-width: fit-content !important;

  .@{popover}__search {
    padding: 4px 10px 8px 10px;
    line-height: 20px;
    border-bottom: 1px solid var(--otd-border-color);

    .ant-input-affix-wrapper {
      line-height: 20px;
      padding: 0;
    }

    .ant-input {
      height: 20px;
    }
  }

  &.in-tab {

    .ant-popover-inner,
    .ant-dropdown-menu {
      padding: 2px 0 0 0;
    }
  }

  &.no-padding {
    .ant-popover-inner-content {
      padding: 0;
    }
  }

  .ant-popover-inner {
    border-radius: var(--otd-border-radius);
    padding: 10px;
    box-shadow: var(--otd-popover-shadow);
  }

  .ant-menu-title-content {
    display: flex;
    align-items: center;
  }

  &.hide-icon {
    .anticon-exclamation-circle {
      display: none;
    }

    .ant-popover-message-title {
      padding-left: 0;
    }
  }
}

.add-action-item,
.add-action-item .ant-dropdown-menu-title-content {
  display: flex;
  align-items: center;
  column-gap: 6px;

  .otdIconfont {
    display: inline-block;
    text-align: center;
  }
}

.action-popover.ant-popover {
  z-index: 1050;

  .ant-popover-content {
    box-shadow: unset;
  }
}

.otd-tag-handle-popover {
  width: 180px;
  padding: 14px;

  .ant-popover-arrow {
    display: none;
  }
}

.otd-tab.ant-tabs {
  color: var(--otd-gray4-color);

  &.no-bottom {
    .ant-tabs-nav::before {
      content: unset;
    }
  }

  .ant-tabs-nav {
    .ant-tabs-nav-wrap {
      .ant-tabs-nav-list:not(.ant-tabs-left) {
        padding: 6px 10px 12px 10px;

        .ant-tabs-tab {
          font-size: 16px;
          line-height: 22px;
          padding: 0;

          &+.ant-tabs-tab {
            margin-left: 32px;
          }
        }

        .ant-tabs-ink-bar {
          border-radius: var(--otd-large-radius);
          height: 4px;
          background-color: var(--otd-primary-main);
          transform: translateY(-100%);
        }
      }
    }
  }
}

.selectTag {
  &:hover a {
    opacity: 1;
  }

  a {
    opacity: 0;
  }
}

.otd-tags-content {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  overflow: hidden;
  gap: 6px;

  &__selected {
    padding: 0 4px;
    flex-wrap: nowrap;

    .otd-tags-content__item {
      flex: 1;
    }
  }

  .tag-more-add {
    border-radius: 8px;
    background-color: var(--otd-border-color);
    height: 24px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 2px;
  }

  & &__input.ant-input {
    height: 22px;
    border-width: 0 !important;
    box-shadow: none;
    color: var(--otd-primary-text);
    padding: 0 10px;
  }

  &.is-vertical {
    flex-direction: column;
    align-items: flex-start;

    .virtual-scroll__item {
      width: 100%;
      display: flex;
    }
  }

  &__item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    max-width: min-content;

    .otd-more-action .show-action {
      margin-right: 0;
    }

    &-container {
      display: flex;
      justify-content: center;
      width: fit-content;
      height: 24px;
      line-height: 24px;
      min-width: 50px;
      max-width: 100%;
      padding-right: 8px;
      padding-left: 8px;
      border-radius: 50%;
      color: var(--action-color, var(--otd-white-text));
      font-size: 13px;
      cursor: pointer;
      user-select: none;
      background-color: var(--action-bg, lighten(#90949d, 20%));
      border-radius: 8px;

      &.is-private {
        &::before {
          font-family: otdIconfont;
          content: '\e62b';
          margin-right: 2px;
          line-height: 2;
        }
      }
    }

    &.is-full {
      width: 100%;
      border-top: 1px solid var(--otd-border-color);
      border-radius: 0;
      height: 48px;
      display: flex;
      align-items: center;
      padding-left: 10px;
      padding-right: 36px;
      max-width: unset;

      .text-title {
        color: var(--otd-icon-text);
      }

      .otd-more-action {
        .hide-action {
          display: flex;
        }
      }

      .otd-more-action {
        right: 10px;
      }
    }

    &-text {
      display: inline-block;
      // margin-right: 4px;
    }

    &-close {
      margin-left: 4px;
      display: flex;
      align-items: center;
      height: 18px;
    }

    &:hover {
      .otd-more-action {
        .hide-action {
          display: flex;
        }
      }
    }

    .otd-more-action {
      border-radius: 4px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 4px;

      .hide-action {
        display: none;
      }

      .add-icon {
        padding: 0 2px;
        line-height: 18px;
        border-radius: 4px;

        &:hover {
          background-color: var(--otd-gray-hover) !important;
        }

        .iconfont {
          color: var(--otd-white-text);
        }
      }
    }

    .add-icon {
      font-size: 16px;
      border-radius: 6px;
      padding: 2px !important;
      line-height: 1;
      cursor: pointer;
      margin-right: 0px;
      background-color: var(--action-bg);

      &:hover {
        background-color: var(--otd-gray-hover);
      }

      &.is-white:hover {
        background-color: var(--otd-white-text);
      }
    }
  }
}

.otd-menu-item-open {
  padding: 6px 0;

  &.ant-popover-open {
    background-color: var(--otd-basic-bg);
  }
}

.ant-dropdown-open,
.ant-popover-open,
.ant-dropdown-open {

  .otd-layout-action,
  &.placeholder-hover,
  .placeholder-hover {
    background-color: var(--otd-gray-hover);
  }
}

// layout操作按钮样式
.otd-layout-action {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 6px;
  border-radius: var(--otd-border-radius);
  color: var(--otd-basic-text);

  &:hover,
  &.is-active {
    background-color: var(--otd-gray-hover);
  }

  .otdIconfont {
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
  }
}

// 卡片列表样式
.otd-card-list__group.ant-list {
  border-width: 0;

  .ant-spin-container {
    >div:first-of-type {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .ant-list-item {
      padding: 8px;
      border-radius: var(--otd-border-radius);
      border: 1px solid var(--otd-border-color);
      box-shadow: var(--otd-box-shadow);

      .ant-list-item-meta {
        .otd-card-list__item {
          &-title {
            display: flex;
            align-items: center;
            height: 26px;

            &__content {
              display: flex;
              flex: 1;
              align-items: center;
              overflow: hidden;
            }

            &__text {
              font-weight: 400;
              font-size: 16px;
              color: var(--otd-basic-text);
              line-height: 26px;
              position: relative;
              width: fit-content;
              margin-bottom: 0;
            }

            .otd-unread {
              display: inline-block;
              margin-left: 8px;
              min-width: 10px;
              height: 10px;
              border-radius: var(--otd-circle-radius);
              background-color: var(--otd-error-color);
            }
          }

          &-content {
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            /* 这里是超出几行省略 */
            overflow: hidden;
            font-weight: 400;
            font-size: 14px;
            color: var(--otd-basic-text);
            line-height: 18px;
            margin: 8px 0;

            a {
              word-break: break-all;
              color: var(--otd-primary-text);
            }
          }

          &-date {
            font-weight: 400;
            font-size: 14px;
            color: var(--otd-gray3-color);
            line-height: 26px;
          }
        }
      }
    }
  }
}

.otd-input-number.ant-input-number {
  border-radius: 6px;

  .ant-input-number-handler-wrap,
  &+.ant-input-number-group-addon {
    border-radius: 0 6px 6px 0;
  }

  &.ant-input-number-focused,
  &:hover {
    box-shadow: unset;

    &+.ant-input-number-group-addon {
      border: 1px solid #2188c4;
      border-left-width: 0;
    }
  }
}

.otd-search-popover.ant-popover {
  .ant-popover-inner {
    border-radius: var(--otd-border-radius);
    padding: 4px 0;

    .otd-popover__content {
      position: relative;
      padding-top: 10px;

      .scrollbar {
        height: 240px;

        .scrollbar__view {
          min-height: 100%;
          padding: 1px 6px 0;

          .ant-empty {
            margin-top: 45%;
            transform: translateY(-50%);
          }
        }
      }
    }
  }
}

.otd-option-item {
  display: flex;
  align-items: center;
  column-gap: 4px;
  padding: 6px 8px;
  cursor: pointer;
  border-radius: var(--otd-default-radius);

  &.is-active,
  &:hover {
    background-color: var(--otd-gray-hover);
  }
}

/*start 协作清单*/
.otd-collaborative {
  .otd-more-action .add-icon.is-large {
    padding: 0 !important;
  }

  .ant-tabs-nav-list {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .ant-tabs-nav::before {
    border: none;
  }

  width: 300px;

  .ant-menu-light {
    background-color: transparent;
  }

  .ant-popover-inner {
    border-radius: var(--otd-border-radius);
    padding: 10px 0px 4px;
  }

  .ant-menu-light.ant-menu-horizontal>.ant-menu-item-selected::after {
    border-bottom-width: 3px;
  }

  .ant-menu-horizontal {
    border: none;
  }

  .ant-input {
    background-color: transparent;
  }

  .ant-input-affix-wrapper {
    border: none;
    box-shadow: none;
    background-color: transparent;
  }

  .ant-menu-item {
    height: 30px;
    line-height: 30px;

    .ant-menu-title-content {
      color: var(--otd-gray4-color);
    }
  }

  .ant-menu-item-selected {
    .ant-menu-title-content {
      color: var(--otd-primary-color);
    }
  }

  .ant-divider-horizontal {
    margin: 0;
  }
}

/*end 协作清单*/

.otd-card.ant-card {
  padding: 1px;
  background-color: var(--otd-basic-bg);

  &:not(.ant-card-bordered) {
    box-shadow: var(--otd-box-shadow);
  }

  .ant-card-head {
    padding: 14px 6px 4px 16px;
    border-bottom-width: 0;
    min-height: 40px;

    .ant-card-head-wrapper {
      height: 100%;

      .ant-card-head-title {
        height: 100%;
        font-size: 14px;
        font-weight: 500;
        line-height: 16px;
      }
    }
  }

  .ant-card-body {
    padding: 20px;

    .otd-count-to {
      color: var(--otd-primary-color);
      font-size: 70px;
      font-weight: bold;
      line-height: 90px;
      margin-top: -10px;
    }
  }
}

.otd-table-action {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 22px;
  height: 100%;

  .otd-more-action {
    display: none !important;
    float: right;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
  }

  .otd-more-action__open {
    display: flex !important;
  }
}

.otd-table-action,
.ant-table-row,
.ant-table-thead tr {
  &:hover {
    .otd-more-action {
      display: flex !important;
    }
  }
}

/** 任务详情页无数据按钮  */
.otd-action-btn {
  display: flex;
  color: var(--otd-gray3-color);
  border: 1px solid var(--otd-border-gray);
  padding: 7px;

  i {
    font-size: 14px;
  }

  span {
    font-size: 14px;
  }
}

// 文本输入框
.otd-text-input.ant-input {
  border-color: transparent;
  background-color: transparent !important;
  box-shadow: unset !important;
  line-height: 1;

  &.ant-input-disabled {
    background-color: inherit;
    color: inherit;
    cursor: inherit;
    border-color: transparent;
  }

  &::placeholder {
    font-size: 14px;
  }

  &.focus-border {
    &:focus {
      &+.otd-more-action {
        display: none !important;
      }
    }
  }
}

.otd-edit-cell-son {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 4px;
}

.otd-tiny {
  &.is-border {
    .tox-tinymce {
      border: 1px solid var(--otd-border-gray);
    }
  }

  .tox-tinymce {
    border-width: 0;
    border-radius: var(--otd-default-radius);
    overflow: unset;

    .tox-editor-container {
      border-radius: var(--otd-default-radius);
      background-color: var(--otd-basic-bg);
      padding: 6px 8px;

      // padding-top: 2px;
      .tox-edit-area {
        border-top-width: 0;

        .tox-edit-area__iframe {
          background-color: var(--otd-basic-bg);
        }
      }

      .tox-editor-header {
        border-bottom: 1px solid var(--otd-border-gray);
        padding-bottom: 3px;

        .tox-toolbar-overlord {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;

          .tox-toolbar {
            background-color: var(--otd-basic-bg);
          }

          .tox-toolbar,
          .tox-toolbar__primary {
            margin-bottom: unset;

            &:last-of-type {
              flex: 1;
              justify-content: flex-end;
            }

            border-top-width: 0;

            // justify-content: space-between;
            .tox-toolbar__group {
              border-right-width: 0;
              align-items: center;

              &+.tox-toolbar__group {
                &::before {
                  content: '|';
                  margin-top: -4px;
                  color: var(--otd-header-bg);
                  margin-right: 2px;
                }
              }

              .tox-tbtn {
                width: 28px;
                height: 28px;

                svg {
                  fill: var(--otd-basic-text);
                }

                &+.tox-tbtn {
                  margin-left: 4px;
                }

                &.tox-tbtn--select {
                  width: auto;
                  height: fit-content;
                  background-color: var(--otd-primary-color);
                  color: var(--otd-body-text);
                  font-size: 12px;
                  line-height: 1;
                  padding: 6px 6px;
                  margin-right: 4px;

                  &:hover {
                    filter: opacity(0.8);
                  }

                  .tox-tbtn__select-label {
                    cursor: pointer;
                  }
                }

                cursor: pointer;

                .tox-icon {
                  transform: scale(0.9);
                }
              }
            }
          }
        }
      }
    }

    &.tox-tinymce--disabled {
      cursor: not-allowed;

      .tox-editor-container {
        background-color: var(--otd-disabled-bg);

        .tox-toolbar-overlord {
          .tox-tbtn--disabled {
            background-color: var(--otd-disabled-bg);
          }
        }
      }

      .tox-edit-area {
        .tox-edit-area__iframe {
          cursor: not-allowed;
          background-color: var(--otd-disabled-bg);
          border-color: var(--otd-border-color);
        }
      }
    }
  }
}

.otd-data-bubble {
  width: fit-content;
  line-height: 1;
  position: relative;
}

.otd-tag-line {
  padding: 6px 4px;
}

.otd-action-clearable__label {
  position: relative;
  padding-right: 14px;

  &:hover .otd-action-clearable {
    display: block;
  }
}

.otd-text-clearable__label {
  .otd-action-clearable {
    top: 50%;
    transform: translate(-4px, -50%);
  }
}

.otd-action-clearable {
  display: none;
  position: absolute;
  right: 0;
  top: 0;
  transform: translate(2px, -25%);
  border-radius: 50%;
  font-size: 14px;
  cursor: pointer;
  color: var(--otd-gray4-color);

  &:hover {
    color: var(--otd-scroll-bg);
  }
}

.otd-description-editor {
  img {
    display: inline-block;

    &:not([width]) {
      width: 120px;
    }
  }

  p {
    margin: 0;
  }
}

// 高级筛选-------start
.otd-filter-placeholder {
  display: flex;
  align-items: center;
  line-height: 22px;
}

.otd-tiny-description {
  word-break: break-word;

  * {
    vertical-align: bottom;
  }

  .attachment {
    display: inline-flex;

    img {
      width: 20px;
      margin-right: 4px;
    }

    a {
      color: var(--otd-primary-main);
      font-size: 14px;
    }
  }
}

.otd-noborder-select {
  border-radius: var(--otd-default-radius);

  &:not(.ant-select-customize-input) {

    &:hover,
    &.ant-select-open {
      background-color: var(--otd-gray3-hover);
    }

    .ant-select-selector {
      height: 30px;
      padding: 0 6px;

      .ant-select-selection-item,
      .ant-select-selection-placeholder {
        line-height: 29px;
      }
    }
  }

  &__custom {
    cursor: pointer;
    padding: 5px 12px;
    width: 100%;
    display: flex;
    border-radius: var(--otd-small-radius);

    &:hover {
      background-color: var(--otd-gray3-hover);
    }
  }
}

.otd-text-select.ant-select {
  &.hide-arrow {
    .ant-select-selection-item {
      padding-right: 0 !important;
    }

    .ant-select-arrow {
      display: none;
    }
  }

  &.leading-none {
    &:not(.ant-select-customize-input) {
      .ant-select-selector {
        height: auto;

        &::after {
          line-height: 1;
        }

        .ant-select-selection-search-input {
          height: auto;
        }

        .ant-select-selection-placeholder {
          line-height: 1;
        }
      }
    }
  }

  &:not(.ant-select-customize-input) {
    .ant-select-selector {
      border: 0;
      background-color: transparent;
      box-shadow: unset !important;
      padding: 0 6px;
    }

    .ant-select-selection-item,
    .ant-select-arrow {
      color: var(--otd-primary-text);
    }
  }

  &.ant-select-disabled {
    &:not(.ant-select-customize-input) {
      .ant-select-selector {
        background-color: transparent;
      }

      .ant-select-selection-item,
      .ant-select-arrow {
        color: var(--otd-gray3-color);
      }
    }
  }
}

.otd-dropdown-item-box {
  .add-icon.add-action-item.is-icon {
    border-width: 0;
    padding: 0;
    justify-content: flex-start;
    box-shadow: unset;
  }
}

@drawerHideHeader: ~'otd-drawer-hide-header';

.@{drawerHideHeader}.ant-drawer-content {
  position: relative;
  overflow: unset;

  .ant-drawer-header {
    display: none;
  }

  .@{drawerHideHeader}__close {
    position: absolute;
    top: 10px;
    left: -10px;
    transform: translateX(-100%);
    padding: 11px;
    line-height: 1;
    border: 1px solid var(--otd-border-color);
    border-radius: var(--otd-circle-radius);
    box-shadow: var(--otd-box-shadow);
    background-color: var(--otd-basic-bg);
    z-index: 5;
    cursor: pointer;

    .anticon-close {
      transition: transform 0.3s;
    }

    &:hover {
      .anticon-close {
        transform: rotate(90deg);
      }
    }
  }
}

.otd-radius-border {
  width: fit-content;
  cursor: pointer;
  padding: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--otd-large-radius);
  border: 1px solid var(--otd-border-color);
  background-color: var(--otd-basic-bg);
}