<template>
  <Popover
    v-model:open="visible"
    trigger="click"
    placement="bottomRight"
    :arrow="false"
    overlay-class-name="otd-popover"
    :overlay-style="{ width: '240px', minWidth: 'auto !important' }"
  >
    <template #content>
      <div class="otd-history-filter">
        <h2 class="otd-history-filter__header">
          {{ popoverTitle }}
          <Checkbox v-model:checked="checkedAll" :indeterminate="indeterminate" @change="handleCheckAll">
            {{ popoverLabel }}
          </Checkbox>
        </h2>
        <OtdScrollbar style="max-height: 240px">
          <CheckboxGroup class="w-full" v-model:value="currenLightAction">
            <HistoryFilterItem
              v-for="(item, key) in getHistoryOptions"
              :key="key"
              :data="{ ...item, value: key }"
              :field-names="{ label: 'label', value: 'value' }"
              is-check
            />
          </CheckboxGroup>
        </OtdScrollbar>
      </div>
    </template>
    <div class="otd-history-filter__trigger otd-box-center placeholder-hover">
      <i class="otdIconfont otd-icon-xiangqing"></i>
      <slot name="name"></slot>
    </div>
  </Popover>
</template>
<script lang="ts" setup>
  import { computed, onMounted, PropType, ref, unref } from 'vue';
  import { getProps } from '../../props';
  import { Checkbox, CheckboxGroup, Popover } from 'ant-design-vue';
  import HistoryFilterItem from './HistoryFilterItem.vue';
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { useHistoryTimeline } from '../useHistoryTimeline';

  const props = defineProps({
    value: {
      type: Array as PropType<string[]>,
    },
    checkedAll: {
      type: Boolean,
      default: false,
    },
    ...getProps(),
  });
  const emit = defineEmits(['update:value', 'update:checkedAll']);
  const { DefaultHistoryOptions } = useHistoryTimeline();
  const getHistoryOptions = computed(() =>
    Object.assign({}, props.historyOptions ?? DefaultHistoryOptions, props.moreHistoryOptions),
  );
  const getHistoryOptionsData = computed(() =>
    Object.values(Object.assign({}, props.historyOptions ?? DefaultHistoryOptions, props.moreHistoryOptions)),
  );
  const visible = ref(false);

  const indeterminate = ref(false);
  const currenLightAction = computed({
    get: () => props.value,
    set: (value) => {
      emit('update:value', value);
      if (value) {
        indeterminate.value = !!value.length && value.length < unref(getHistoryOptionsData).length;
        checkedAll.value = value.length === unref(getHistoryOptionsData).length;
      }
    },
  });
  const checkedAll = computed({
    get: () => props.checkedAll,
    set: (value) => emit('update:checkedAll', value),
  });
  onMounted(() => {
    if (unref(checkedAll)) {
      currenLightAction.value = Object.keys(unref(getHistoryOptions));
    }
  });

  // 全选
  function handleCheckAll({ target }) {
    currenLightAction.value = target.checked ? Object.keys(unref(getHistoryOptions)) : [];
    indeterminate.value = false;
  }
</script>
<style lang="less" scoped>
  .otd-history-filter {
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 14px;
    }
    &__trigger {
      height: fit-content;
    }
  }
</style>
