import { OtdMoreAction, type MoreActionItem } from '/@/components/OtdMoreAction';
import type { TableColumnPropsType } from '/@/components/OtdTable';
import type { Recordable } from '/#/global';
import type { TrackTimeEmitsType, TrackTimePropsType, TrackTimeTableDataType } from './type';
import { ComponentInternalInstance, getCurrentInstance, reactive, unref } from 'vue';
import { useTable } from '/@/components/OtdTable';
import { useModal } from '/@/components/BasicModal';
import { useI18n } from '/@/hooks/web/useI18n';
import { formatToDate } from '/@/tool';
import { useMessage } from '/@/hooks/web/useMessage';
import { ERROR_COLOR } from '/@/setting';
import { HourTypeEnum } from './props';
import { Key } from 'ant-design-vue/es/vc-table/interface';
import { NOTDATA } from '/@/settings/const';

export function useTrackTime() {
  const { props, emit, proxy } = getCurrentInstance() as ComponentInternalInstance & {
    props: TrackTimePropsType;
    emit: TrackTimeEmitsType;
  };
  const { t } = useI18n();
  const { deleteConfirm } = useMessage();
  // 主表格列
  const MainTableColumns: TableColumnPropsType[] = [
    // 工时类型
    {
      title: t('common.trackTime.WorkHourType'),
      dataIndex: 'name',
      width: 200,
      customRender: ({ record }) => {
        return (
          <div class="otd-table-action">
            <span>{record.name}</span>
            <OtdMoreAction
              actions={[
                // 添加
                {
                  id: 1,
                  name: t('common.add'),
                  icon: 'otd-icon-add-2',
                  action: (data) => {
                    openModalEntry(true, { data: { hourType: data.key }, record: data });
                  },
                },
              ]}
              expandNumber={1}
              data={record}
              hideExpandName
              size="medium"
              disabled={props.disabled}
            />
          </div>
        );
      },
    },
    // 总时长
    {
      title: t('common.trackTime.TotalDuration'),
      dataIndex: 'time',
    },
  ];
  // 主表格数据映射
  const MainDataSourceMap: Recordable<TrackTimeTableDataType> = {
    // 计划工时
    planTime: {
      key: HourTypeEnum.Plan,
      name: t('common.trackTime.plannedTrackingTime'),
      time: 0,
    },
    // 实际工时
    actualTime: {
      key: HourTypeEnum.Real,
      name: t('common.trackTime.actualTrackingTime'),
      time: 0,
    },
  };
  // 子表格列
  const ExpandTableColumns: TableColumnPropsType[] = [
    // 负责人
    {
      title: t('common.user'),
      dataIndex: 'userName',
      width: 120,
      customRender: ({ record }) => {
        return (
          <div class="otd-table-action">
            <span>{record.userName ?? NOTDATA}</span>
            <OtdMoreAction
              actions={actionList}
              expandNumber={2}
              data={{ data: record }}
              hideExpandName
              size="medium"
              disabled={props.disabled}
            />
          </div>
        );
      },
    },
    // 工时类别
    {
      title: `${t('common.trackTime.WorkHourCategory')}`,
      dataIndex: 'category',
      width: 120,
      customRender: ({ value }) => value ?? NOTDATA,
    },
    // 工时
    {
      title: `${t('common.trackTime.trackTime')} (${t('common.hours')})`,
      dataIndex: 'workHour',
      width: 120,
    },
    // 时间
    {
      title: t('common.time'),
      dataIndex: 'workDate',
      width: 120,
      customRender: ({ record }) => (record.workDate ? formatToDate(record.workDate) : NOTDATA),
    },
  ];

  // 表格数据
  const tableDataSource = reactive<TrackTimeTableDataType[]>([]);
  const defaultExpandedRowKeys = reactive<Key[]>([HourTypeEnum.Real]);

  const [registerMainTable] = useTable({
    columns: MainTableColumns,
    dataSource: tableDataSource,
    pagination: false,
    defaultExpandedRowKeys: defaultExpandedRowKeys,
  });
  const [registerExpandTable] = useTable({ columns: ExpandTableColumns, pagination: false });

  const [registerEntry, { openModal: openModalEntry }] = useModal();
  const [registerDetail, { openModal: openModalDetail, getVisible: getVisibleDetail }] = useModal();

  // 设置工时表格数据源
  function setTrackTimeSource() {
    const { planTime, actualTime } = props.data;
    tableDataSource.length = 0;
    if (planTime) {
      tableDataSource.push(MainDataSourceMap.planTime);
      MainDataSourceMap.planTime.time = planTime.totalTime;
      MainDataSourceMap.planTime.expandChildren = planTime.timeRecord;
    }
    if (actualTime) {
      tableDataSource.push(MainDataSourceMap.actualTime);
      MainDataSourceMap.actualTime.time = actualTime.totalTime;
      MainDataSourceMap.actualTime.expandChildren = actualTime.timeRecord;
    }
  }

  // 操作
  const actionList: MoreActionItem[] = [
    // 编辑
    {
      id: 1,
      name: t('common.editText'),
      icon: 'otd-icon-a-cateditsize24',
      action: ({ data }) => {
        openModalEntry(true, { data });
      },
    },
    // 删除
    {
      id: 2,
      color: ERROR_COLOR,
      name: t('common.delText'),
      icon: 'otd-icon-a-catdeletesize24',
      action: ({ data }) => {
        deleteConfirm(() => {
          emit('delete', { id: data.id, reload: () => handleUpdateRecord(data), type: data.hourType });
        });
      },
    },
  ];

  function handleUpdateRecord(data?) {
    emit('reload', data);
    if (unref(getVisibleDetail)) {
      const { hourDetail = undefined } = proxy?.$refs as any;
      hourDetail?.reload();
    }
  }

  return [
    { registerEntry, registerDetail, registerMainTable, registerExpandTable },
    {
      actionList,
      setTrackTimeSource,
      openModalDetail,
      handleUpdateRecord,
    },
  ] as const;
}
