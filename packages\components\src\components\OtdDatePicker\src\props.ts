import { PropType } from 'vue';
import { DateValueType } from './type';
import { mutable } from '/@/tool';

export const getProps = () => ({
  value: {
    type: Array as unknown as PropType<[DateValueType, DateValueType]>,
  },
  remind: {
    type: String,
  },
  repeat: {
    type: String,
  },
  repeatSkipCnHoliday: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
  },
  isSimple: {
    type: Boolean,
    default: false,
  },
  startPlaceholder: {
    type: String,
  },
  endPlaceholder: {
    type: String,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  notTipColor: {
    type: [Boolean, Object] as PropType<boolean | { start: boolean; end: boolean }>,
    default: false,
  },
  hideStartTime: {
    type: Boolean,
    default: false,
  },
  hideEndTime: {
    type: Boolean,
    default: false,
  },
  hideRemind: {
    type: Boolean,
    default: false,
  },
  hideRepeat: {
    type: Boolean,
    default: false,
  },
});
const emit = ['update:value', 'change', 'update:remind', 'update:repeat'] as const;
export const getEmits = () => mutable(emit);
