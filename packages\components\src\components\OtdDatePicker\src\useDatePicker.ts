import type { DatePickerEmitsType, DatePickerPropsType, DateValueType } from './type';
import dayjs, { type Dayjs } from 'dayjs';
import { computed, defineComponent, getCurrentInstance, nextTick, ref, unref } from 'vue';
import { isToTime, judgeDateBefore } from '/@/tool';
import { useI18n } from '/@/hooks/web/useI18n';

export const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});

export const DateTimeFormat = 'YYYY/MM/DD HH:mm';
export const DateFormat = 'YYYY/MM/DD';

export function getDateFormat(date: Dayjs, time?: string) {
  return isToTime(date) ? date.format(DateTimeFormat) : [date.format(DateFormat), time].join(' ');
}

export function useDatePicker() {
  const { t } = useI18n();
  const { props, emit } = getCurrentInstance() as unknown as {
    props: DatePickerPropsType;
    emit: DatePickerEmitsType;
  };
  const panelValue = ref();
  const dateIndex = ref<0 | 1>();
  // 到期提醒
  const remindRule = computed({
    get: () => props.remind,
    set: (value) => emit('update:remind', value),
  });
  // 重复设置
  const repeatRule = computed({
    get: () => props.repeat,
    set: (value) => emit('update:repeat', value),
  });
  // 日期快捷选项
  const rangePresets = [
    { label: t('common.datePicker.Today'), value: dayjs().startOf('day') },
    { label: t('common.datePicker.Later'), value: dayjs().set('hour', 18).set('minute', 0).set('second', 0) },
    { label: t('common.datePicker.Tomorrow'), value: dayjs().add(1, 'day').startOf('day') },
    { label: t('common.datePicker.ThisWeekend'), value: dayjs().endOf('week').startOf('day') },
    { label: t('common.datePicker.NextWeek'), value: dayjs().startOf('week').add(1, 'week').startOf('day') },
    { label: t('common.datePicker.NextWeekend'), value: dayjs().endOf('week').add(1, 'week').startOf('day') },
    { label: t('common.datePicker.2Weeks'), value: dayjs().startOf('week').add(2, 'week').startOf('day') },
    { label: t('common.datePicker.4Weeks'), value: dayjs().startOf('week').add(4, 'week').startOf('day') },
  ];
  const currentDateRange = ref<[DateValueType, DateValueType]>([undefined, undefined]);

  // 时间改变事件
  function handleChange(isUpdate = true) {
    const date = judgeDateBefore(unref(currentDateRange));
    if (isUpdate) {
      if (!date?.[1]) {
        repeatRule.value = undefined;
        remindRule.value = undefined;
      }
      emit('update:value', date);
    }
    nextTick(() => {
      emit('change', date, {
        remind: unref(remindRule),
        repeat: unref(repeatRule),
      });
    });
  }

  function setDateRange(date) {
    const [start] = currentDateRange.value;
    if (unref(dateIndex) === 0) {
      currentDateRange.value = [date, null];
    } else {
      if (start && start.isAfter(date)) {
        currentDateRange.value = [date, date];
      } else {
        currentDateRange.value[unref(dateIndex) ?? 1] = date;
      }
    }
    dateIndex.value = 1;
  }

  // 选择面板时间范围
  function handleChangePanelRange(data) {
    currentDateRange.value = data.map((item) => item && dayjs(`${item?.format(DateFormat)}`));
    dateIndex.value = 1;
    handleChange();
  }

  // 选择快捷时间
  function handleQuickDate(item) {
    setDateRange(item.value);
    handleChange();
  }

  return {
    panelValue,
    remindRule,
    repeatRule,
    rangePresets,
    currentDateRange,
    dateIndex,
    handleChange,
    handleChangePanelRange,
    handleQuickDate,
  };
}
