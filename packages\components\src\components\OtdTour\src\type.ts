import { Config, DriveStep, Popover, State } from 'driver.js';
import { VNode } from 'vue';

type Merge<M, N> = Omit<M, Extract<keyof M, keyof N>> & N;

export type PopoverDOM = Merge<
  Popover,
  Partial<{
    title: string | VNode;
    description: string | VNode;
  }>
>;

export type DriveStepType = Merge<
  DriveStep,
  Partial<{
    popover: PopoverDOM;
    trigger: 'click' | 'hover';
    delay: number;
  }>
>;

export type DriverHookType = (
  element: Element | undefined,
  step: DriveStepType,
  opts: { config: TourDriverConfigType; state: State },
) => void;

export type TourDriverConfigType = Merge<
  Config,
  Partial<{
    steps: DriveStepType[];
    onHighlightStarted: DriverHookType;
    onHighlighted: DriverHookType;
    onDeselected: DriverHookType;
  }>
>;
