<template>
  <Upload
    :show-upload-list="false"
    :before-upload="handleBeforeUpload"
    :custom-request="handleUpload"
    multiple
    v-bind="props.uploadProps"
    :file-list="fileList"
    :on-success="handleUploadSuccess"
  >
    <div class="placeholder-hover otd-upload-btn">
      <div class="add-icon">
        <i class="otdIconfont otd-icon-add-2"></i>
        <span>{{ props.placeholder || t('common.uploadFile.addAttachment') }}</span>
      </div>
      <div v-if="props.fileList.length > 0">
        <span class="otdIconfont">|</span>
        <span>{{ t('common.uploadFile.attachmentCount', { count: props.fileList.length }) }}</span>
      </div>
    </div>
  </Upload>
</template>
<script lang="ts" setup>
  import { Upload } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getProps, getEmits } from '/@/components/OtdUploadFile/src/props';
  import { useUploadAttachment } from './UseUploadAttachment';

  const { t } = useI18n();
  const props = defineProps(getProps());
  const emit = defineEmits(getEmits());

  const { handleBeforeUpload, handleUpload, handleUploadSuccess } = useUploadAttachment();
</script>

<style scoped lang="less">
  .otd-upload-btn {
    color: var(--otd-task-detail-txt);
    border: none;
    align-items: center;
    .add-icon {
      display: flex;
      align-items: center;
      column-gap: 6px;
    }
    &:hover {
      background-color: var(--otd-gray-hover) !important;
    }
    i {
      font-size: 16px;
    }
    > div {
      & + div {
        margin-left: 10px;
        .otdIconfont {
          margin-right: 10px;
        }
      }
    }
  }
</style>
