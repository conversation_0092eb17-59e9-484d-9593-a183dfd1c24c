import { PropType } from 'vue';
import { MenuType } from '../../OtdMenu';
import { Recordable } from '/#/global';
import { MoreActionItem } from '/@/components/OtdMoreAction';
import { TriggerSubMenuAction } from 'ant-design-vue/es/menu/src/interface';

export const layoutProps = () => ({
  logoUrl: {
    type: String,
  },
  hiddenLayout: {
    type: Boolean,
    default: false,
  },
  selectedKeys: {
    type: Array as PropType<string[]>,
    default: undefined,
  },
  menuItems: {
    type: Array as PropType<MenuType[]>,
    default: () => [],
  },
  userInfo: {
    type: Object as PropType<Recordable>,
    default: () => ({}),
  },
  userDropdownAction: {
    type: Array as PropType<MoreActionItem[]>,
    default: () => [],
  },
  showQuickAction: {
    type: Boolean,
    default: false,
  },
  customQuickActions: {
    type: Array as PropType<MoreActionItem[]>,
    default: () => [],
  },
  triggerSubMenuAction: {
    type: String as PropType<TriggerSubMenuAction>,
    default: 'hover',
  },
});
