import dayjs, { Dayjs } from 'dayjs';

// 判断坐标是否在区域范围内
export function isPointInRegion(
  point: [number, number],
  coordinate: { start: [number, number]; end: [number, number] },
) {
  const { start, end } = coordinate;
  const [x, y] = point;
  if (
    x >= Math.min(start[0], end[0]) &&
    x <= Math.max(start[0], end[0]) &&
    y >= Math.min(start[1], end[1]) &&
    y <= Math.max(start[1], end[1])
  ) {
    return true;
  } else {
    return false;
  }
}

// 获取单元格居中坐标
export function getCellCenterSeat(
  ctx,
  strDate: string | { contentHeight: number; contentWidth: number },
  { x, y, width, height },
) {
  let info = { contentHeight: 0, contentWidth: 0 };
  if (typeof strDate === 'string') {
    const { actualBoundingBoxDescent: contentHeight, width: contentWidth } =
      ctx.measureText(strDate);
    info = { contentHeight, contentWidth };
  } else {
    info = strDate;
  }
  const { contentHeight, contentWidth } = info;
  const textX = x + (width - contentWidth) / 2;
  const textY = y + (height + contentHeight) / 2;
  return [textX, textY, contentWidth, contentHeight];
}

// 获取两个时间范围的交集数量
export function calculateIntersectionDays(
  start: [Dayjs, Dayjs],
  end: [Dayjs, Dayjs],
  type: dayjs.OpUnitType = 'd',
) {
  // 定义时间范围1
  const range1 = { start: start[0], end: start[1] };

  // 定义时间范围2
  const range2 = { start: end[0], end: end[1] };

  // 计算交集的起始时间
  const intersectionStart = range1.start.isAfter(range2.start, type) ? range1.start : range2.start;

  // 计算交集的结束时间
  const intersectionEnd = range1.end.isBefore(range2.end, type) ? range1.end : range2.end;
  // 检查是否存在交集
  if (intersectionStart.isBefore(intersectionEnd, type)) {
    // 计算交集的天数
    const intersectionDays = intersectionEnd.diff(intersectionStart, 'day') + 1;
    return intersectionDays;
  } else if (intersectionStart.isSame(intersectionEnd, type)) {
    return 1;
  } else {
    // 没有交集
    return 0;
  }
}
// 输入一个颜色，返回暗一点的颜色
export function darkenColor(color, factor) {
  // 解析颜色值
  const hex = color.replace(/^#/, '');
  const rgb = parseInt(hex, 16);

  // 计算新的RGB值，并限制在合理范围内
  const r = Math.min(255, Math.round(((rgb >> 16) & 0xff) * factor));
  const g = Math.min(255, Math.round(((rgb >> 8) & 0xff) * factor));
  const b = Math.min(255, Math.round((rgb & 0xff) * factor));

  // 将新的RGB值转换为十六进制颜色
  const darkenedHex = ((r << 16) | (g << 8) | b).toString(16).padStart(6, '0');

  // 返回新的颜色值
  return `#${darkenedHex}`;
}
// 输入一个颜色，返回亮一点的颜色
export function lightenColor(color, factor) {
  // 解析颜色值
  const hex = color.replace(/^#/, '');
  const rgb = parseInt(hex, 16);

  // 计算新的RGB值，并限制在合理范围内
  const r = Math.min(255, Math.round(((rgb >> 16) & 0xff) + (255 - ((rgb >> 16) & 0xff)) * factor));
  const g = Math.min(255, Math.round(((rgb >> 8) & 0xff) + (255 - ((rgb >> 8) & 0xff)) * factor));
  const b = Math.min(255, Math.round((rgb & 0xff) + (255 - (rgb & 0xff)) * factor));

  // 将新的RGB值转换为十六进制颜色
  const lightenedHex = ((r << 16) | (g << 8) | b).toString(16).padStart(6, '0');

  // 返回新的颜色值
  return `#${lightenedHex}`;
}
