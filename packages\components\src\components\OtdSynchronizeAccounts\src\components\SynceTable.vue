<template>
  <Modal
    :destroyOnClose="true"
    v-model:open="visible"
    width="60%"
    class="otd-synchronize-accounts-modal"
    :title="t('common.SynchronizeAccounts.userManagement_synced')"
    @ok="visible = false"
  >
    <div class="otd-synchronize-accounts-modal__content">
      <OtdTable :data-source="tableData" @register="register" size="small" />
    </div>
  </Modal>
</template>

<script setup lang="tsx">
  import type { TableColumnPropsType } from '/@/components/OtdTable/src/OtdTable';
  import { Modal } from 'ant-design-vue';
  import { ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { OtdTable, useTable } from '/@/components/OtdTable/index';
  const visible = ref(false);
  const { t } = useI18n();
  const tableData = ref<any[]>([]);

  function show(data) {
    tableData.value = data;
    visible.value = true;
  }

  const tableColumns: TableColumnPropsType[] = [
    {
      title: t('common.SynchronizeAccounts.userManagement_name'),
      dataIndex: 'name',
      align: 'left',
    },
    {
      title: t('common.SynchronizeAccounts.userManagement_email'),
      dataIndex: 'email',
      align: 'left',
    },
    {
      title: t('common.SynchronizeAccounts.userManagement_phone'),
      dataIndex: 'phoneNumber',
      align: 'left',
    },
    {
      title: t('common.SynchronizeAccounts.userManagement_department'),
      dataIndex: 'name',
      align: 'left',
      customRender: ({ record }) => {
        return <div>{record.orgUnits.map((item) => item.displayName).join(',')}</div>;
      },
    },
  ];

  // table配置
  const [register] = useTable({
    columns: tableColumns,
    showTableSetting: false,
    bordered: true,
    showIndexColumn: true,
    scroll: {
      y: 400,
    },
    pagination: false,
  });

  defineExpose({ show });
</script>

<style lang="less" scoped>
  .otd-synchronize-accounts-modal {
    &__content {
      padding: 5px;
    }
  }
</style>
