import type { Recordable } from '/#/global';
import { provide, inject } from 'vue';
import { useLockStorage } from '/@/storage/lockStorage';
import { OtdLayoutConfigType } from '/@/components/OtdConfigProvider';

export const GlobalConfig = Symbol('Otd-Config');

export type GlobalConfigType = {
  getLockStorage: ReturnType<typeof useLockStorage>;
  getUserInfo?: Recordable;
} & OtdLayoutConfigType;

export function useGlobalConfig() {
  const getLockStorage = useLockStorage();
  function setGlobalProvide(config: OtdLayoutConfigType) {
    provide<GlobalConfigType>(GlobalConfig, {
      getLockStorage,
      ...config,
      getUserInfo: config.userInfo!,
    });
  }
  function getGlobalProvide() {
    return inject(GlobalConfig, {}) as GlobalConfigType;
  }
  return {
    setGlobalProvide,
    getGlobalProvide,
  };
}
