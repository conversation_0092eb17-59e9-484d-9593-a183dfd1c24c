<template>
  <Modal
    :afterClose="closeModal"
    :destroyOnClose="true"
    v-model:open="visible"
    width="60%"
    class="otd-synchronize-accounts-modal"
    :title="t('common.SynchronizeAccounts.synchronize_accounts')"
  >
    <div class="otd-synchronize-accounts-modal-content">
      <div class="otd-synchronize-accounts-modal__title-box">
        <div class="otd-synchronize-accounts-modal__title-box__info">
          <div>
            <div class="otd-synchronize-accounts-modal__title-box__info__title">
              {{ t('common.SynchronizeAccounts.userManagement_binding') }}
            </div>
            <div class="otd-synchronize-accounts-modal__title-box__info__detail">
              <div>
                {{ t('common.SynchronizeAccounts.userManagement_binding1') }}
                <span>{{ t('common.SynchronizeAccounts.userManagement_binding4') }} </span>
                {{ t('common.SynchronizeAccounts.userManagement_binding2') }}
                <span>{{ t('common.SynchronizeAccounts.userManagement_binding5') }} </span>
                {{ t('common.SynchronizeAccounts.userManagement_binding3') }}
              </div>
            </div>
          </div>
          <div class="otd-synchronize-accounts-modal__title-box__info__actions">
            <OtdImpExcel @success="loadDataSuccess">
              <Button
                class="otd-synchronize-accounts-modal__title-box__info__actions__imt-btn"
                type="primary"
                :loading="importLoading"
              >
                {{ t('common.SynchronizeAccounts.userManagement_inducts') }}
              </Button>
            </OtdImpExcel>

            <Button
              type="primary"
              :disabled="syncedList.length === 0"
              :loading="importLoading"
              @click="($refs as any).SynceTableRef.show(syncedList)"
            >
              {{ t('common.SynchronizeAccounts.userManagement_synced') }}
            </Button>
          </div>
        </div>
      </div>

      <OtdTable
        class="otd-synchronize-accounts-modal-content__table"
        :data-source="systemTableList"
        v-loading="tableLoading"
        @register="register"
      >
        <template #bodyCell="{ column, record, index }">
          <div v-if="column.dataIndex === 'phoneNumber'">
            <OtdEditCellItem>
              <AutoComplete
                @change="record.isPhoneError = false"
                :disabled="record.notSync"
                :dropdown-match-select-width="200"
                v-model:value="record.newPhone"
                class="w-full"
                :fieldNames="{ label: 'name', value: 'phoneNumber' }"
                :options="handleMapInit(record, 'phoneNumber')"
                @select="(_, option) => handleSelectChange(option, record)"
              />
            </OtdEditCellItem>
          </div>
          <div v-if="column.dataIndex === 'email'">
            <OtdEditCellItem>
              <AutoComplete
                @change="record.isEmailError = false"
                :disabled="record.notSync"
                :dropdown-match-select-width="200"
                v-model:value="record.newEmail"
                class="w-full"
                :fieldNames="{ label: 'name', value: 'email' }"
                :options="handleMapInit(record, 'email')"
                @select="(_, option) => handleSelectChange(option, record)"
              />
            </OtdEditCellItem>
          </div>
          <div v-if="column.dataIndex === 'operation'">
            <div class="wx-info-box__user_content">
              <div class="wx-info-box__user_content__operation">
                <div v-if="record.notSync" class="wx-info-box__user_content__operation__tag">{{
                  t('common.SynchronizeAccounts.userManagement_synced')
                }}</div>
                <div v-else-if="record.matchingSuccess" class="wx-info-box__user_content__operation__tag">
                  {{ t('common.SynchronizeAccounts.userManagement_nameMatching') }}
                </div>
                <div v-if="record.isPhoneError" class="wx-info-box__user_content__operation__tag error-tag">
                  {{ t('common.SynchronizeAccounts.userManagement_phoneClash') }}
                </div>
                <div v-if="record.isEmailError" class="wx-info-box__user_content__operation__tag error-tag">
                  {{ t('common.SynchronizeAccounts.userManagement_emailClash') }}
                </div>
                <div
                  class="wx-info-box__user_content__operation__remove-btn placeholder-hover"
                  @click="handleRemoveWXUser(index)"
                >
                  {{ t('common.SynchronizeAccounts.userManagement_remove') }}
                </div>
              </div>
            </div>
          </div>
        </template>
      </OtdTable>
    </div>

    <template #footer>
      <Button @click="visible = false">{{ t('common.cancelText') }}</Button>
      <Button :disabled="isUpload" key="submit" type="primary" :loading="saveLoading" @click="handleSave">
        {{ t('common.okText') }}
      </Button>
    </template>
  </Modal>

  <SynceTable ref="SynceTableRef" />
</template>

<script lang="ts" setup>
  import { Modal, Button, AutoComplete } from 'ant-design-vue';
  import { UseSynchronizeAccounts } from './UseSynchronizeAccounts';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { OtdTable, useTable } from '/@/components/OtdTable/index';
  import { OtdImpExcel } from '/@/components/OtdExcelAction';
  import SynceTable from './components/SynceTable.vue';
  import { OtdEditCellItem } from '/@/components/OtdEditCell';
  import { ref } from 'vue';
  import { getProps } from './props';
  defineProps(getProps());
  const {
    visible,
    importLoading,
    systemTableList,
    syncedList,
    isUpload,
    saveLoading,
    tableLoading,
    tableColumns,
    getUnuseedExcel,
    closeModal,
    handleSave,
    handleSelectChange,
    getTableListAsync,
    handleRemoveWXUser,
    loadDataSuccess,
  } = UseSynchronizeAccounts();

  function handleMapInit(record, key) {
    return getUnuseedExcel(record.name, key);
  }
  const { t } = useI18n();
  const SynceTableRef = ref();
  function show() {
    getTableListAsync();
    isUpload.value = true;
    visible.value = true;
  }

  // table配置
  const [register] = useTable({
    columns: tableColumns,
    showTableSetting: false,
    bordered: true,
    showIndexColumn: true,
    scroll: { y: 400 },
    pagination: false,
  });

  defineExpose({ show });
</script>

<style scoped lang="less">
  .otd-synchronize-accounts-modal {
    .otd-synchronize-accounts-modal-content {
      padding: 20px;
      &__table {
        margin-top: 20px;
      }
    }
    &__title-box {
      display: flex;
      flex-direction: column;
      &__info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        &__title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        &__detail {
          display: flex;
          column-gap: 10px;
          & div {
            font-size: 15px;
            color: #7d7d7d;
          }
          & span {
            margin: 0 5px;
            color: var(--otd-basic-text);
            font-weight: bold;
          }
        }
        &__actions {
          display: flex;
          &__imt-btn {
            margin-right: 5px;
          }
        }
      }
    }
  }

  .system-info-popover {
    padding: 10px;
    width: 260px;
    & div {
      margin-bottom: 10px;
      & span {
        color: #7d7d7d;
      }
    }
  }

  .system-info-box {
    width: fit-content;
    cursor: pointer;
    &__user_content {
      display: flex;
      align-items: center;
      &__info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-left: 10px;
        height: 35px;
        justify-content: space-between;
      }
    }
  }

  .wx-info-box__user_content {
    display: flex;
    align-items: center;
    &__operation {
      display: flex;
      //column-gap: 40px;
      align-items: center;
      column-gap: 20px;
      &__tag {
        height: 25px;
        line-height: 25px;
        background: #d9f6da;
        text-align: center;
        border-radius: 15px;
        color: #747b74;
        padding: 0 8px;
      }
      &__remove-btn {
        color: #7d7d7d;
        font-size: 15px;
      }
    }
  }

  .error-tag {
    background-color: var(--otd-error-color);
    color: var(--otd-white-text);
  }

  .ant-modal-root .ant-modal-wrap {
    z-index: 800;
  }
</style>
