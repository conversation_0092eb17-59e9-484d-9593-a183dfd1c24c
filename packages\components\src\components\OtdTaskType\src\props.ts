import { PropType } from 'vue';
import { TaskTypeOptionType } from './type';
import { IconsType } from '/@/components/BasicIcon';

export const getProps = () => ({
  value: { type: [String, Number] },
  options: { type: Array as PropType<TaskTypeOptionType[]> },
  title: { type: String },
  defaultValue: { type: String as PropType<IconsType> },
  help: { type: String },
  placeholder: { type: String },
  disabled: { type: Boolean, default: false },
  mode: { type: String as PropType<'select' | 'icon'> },
  isIcon: { type: Boolean, default: false },
});
