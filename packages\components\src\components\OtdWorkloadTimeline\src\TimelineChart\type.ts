import { Square } from './ToolLibrary';
import { DirectionType, ThemeType } from './ToolLibrary/type';
import { Recordable } from '/#/global';

export type TimelineConfigType = {
  view: string;
  // 时间线日期范围
  dateRange: [string, string];
  // 时间线数据
  timelineData: Recordable[];
  // 单元格最小宽度
  cellMinWidth?: number;
  // 单元格最小高度
  cellMinHeight?: number;
  itemsKey?: string;
  themeMode?: ThemeType;
  theme?: {
    // 主要颜色
    mainColor?: string;
    // 表头背景颜色
    headerBackgroundColor?: string;
    // 单元格背景颜色
    cellBackgroundColor?: string;
    // 特殊单元格背景颜色
    cellSpaceBackgroundColor?: string;
    // 单元格边框颜色
    cellBorderColor?: string;
    // 时间线背景颜色
    timelineColor?: string;
    // 时间线背景颜色处理器
    timelineColorHandler?: (data) => string;
    // 优先级背景颜色处理器
    priorityHandler?: (data) => string;
    // 拖拽背景颜色
    dragBackgroundColor?: string;
    // 选中时间线的边框颜色
    activeTimelineBorderColor?: string;
  };
  // 表头单元格最小宽度
  headerMinHeight?: number;
  // 时间线高度
  timelineHeight?: number;
  // 时间线间隔
  timelineSpace?: number;

  container?: () => HTMLElement;
  isCanEditer?: (data: { record: Recordable }) => boolean;
  onClick?: (data) => void;
  onDrop?: (data: { record: Recordable; rowData: Recordable; time: string; index: number }) => Promise<Recordable>;
  onResizeEnd?: (data: {
    record: Recordable;
    rowData: Recordable;
    time: string;
    type: DirectionType;
  }) => Promise<Recordable>;
  onMoveEnd?: (data: { record: Recordable; rowData: Recordable; time: string[] }) => Promise<Recordable>;
};

export type GridInfoType = {
  padding: [number, number, number, number];
  content: Record<number, Record<number, Square>>;
};

export type ToolCoordinateType = {
  scrollbar?: ScrollbarCoordinate;
};

export type Coordinate = {
  // 开始坐标
  start: [number, number];
  // 结束坐标
  end: [number, number];
  // 宽
  width: number;
  // 高
  height: number;
  // 行
  row: number;
  // 列
  column: number;
};

export type ScrollbarConfigType = {
  // 滚动条宽度
  scrollBarWidth?: number;
  // 滚动条轨道
  scrollBar?: number;
  // 滚动条颜色
  scrollbarColor?: string;
  // 滚动条悬浮颜色
  scrollbarHoverColor?: string;
  borderColor?: string;
};

export type ScrollbarCoordinate = {
  horizontal?: Coordinate;
  vertical?: Coordinate;
};

export type ScrollbarInfoType = {
  scrollY: string;
  scrollX: string;
  scrollYDom?: Element;
  scrollXDom?: Element;
};
