import { PropType } from 'vue';
import { mutable } from '/@/utils/props';
import { Recordable } from '/#/global';

export const getProps = () => ({
  loading: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array as PropType<Recordable[]>,
    default: () => [],
  },
  value: {
    type: String,
  },
  placeholder: {
    type: String,
    default: '',
  },
  searchPlaceholder: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  createTip: {
    type: String,
    default: '',
  },
  isEditor: {
    type: Function as PropType<(data) => boolean>,
    default: () => true,
  },
});
const emit = ['update:value', 'select', 'create', 'rename', 'delete', 'archive', 'cancelArchive', 'clear'] as const;
export const getEmits = () => mutable(emit);
