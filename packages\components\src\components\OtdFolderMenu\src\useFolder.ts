import type { MoreActionItem } from '/@/components/OtdMoreAction';
import { getCurrentInstance, ComponentInternalInstance, ref } from 'vue';
import { message } from 'ant-design-vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { FolderEnum } from '/@/utils/types';
import { useMessage } from '/@/hooks/web/useMessage';
import { DataNode } from 'ant-design-vue/es/tree';
import { FolderPropsType } from './type';
import { ERROR_COLOR } from '/@/setting';
import { useModal } from '/@/components/BasicModal';

type InternalInstance = ComponentInternalInstance & {
  props: FolderPropsType;
  proxy: {
    selected: any;
    expanded: any;
  };
};
export function useFolder() {
  const { createConfirm } = useMessage();
  const { emit, props } = getCurrentInstance() as InternalInstance;
  const { t } = useI18n();
  const treeData = ref<DataNode[] | undefined>(undefined);
  const Folder = props.folderRequest;

  const [registerCreateFolder, { openModal: handleCreateFolder }] = useModal();
  const [registerMoveFolder, { openModal: handleMoveFolder }] = useModal();

  // 文件夹操作菜单
  const operationList: MoreActionItem[] = [
    {
      id: 'add', // 创建文件夹
      icon: 'otd-icon-add-2',
      name: t('common.folder.newFolder'),
      action: ({ data }) => {
        handleCreateFolder(true, { parentDirectory: data.id });
      },
    },
    {
      id: 'update', // 更新文件夹
      icon: 'otd-icon-a-cateditsize24',
      name: t('common.folder.updateFolder'),
      action: (data?) => {
        handleCreateFolder(true, { data });
      },
    },
    {
      id: 'move', // 移动到
      icon: 'otd-icon-yidong',
      name: t('common.folder.moveTo'),
      action: (data?) => {
        handleMoveFolder(true, { data });
      },
    },
    {
      id: 'delete', // 删除文件夹
      color: ERROR_COLOR,
      icon: 'otd-icon-a-catdeletesize24',
      name: t('common.folder.removeFolder'),
      action: (data?) => {
        handleRemoveFolder(data);
      },
    },
  ];

  // 删除文件夹
  function handleRemoveFolder(node?) {
    createConfirm({
      iconType: 'warning',
      title: t('common.tip'),
      content: t('common.askDelete'),
      onOk: async () => {
        await Folder?.delete?.(node.data.id);
        message.success(t('common.operationSuccess'));
        handleRefrush(node.data.parentDirectory ? node.parentNode : undefined);
      },
    });
  }

  // 处理刷新
  function handleRefrush(data) {
    onLoadData(data);
    emit('reload', data);
  }

  // 获取文件夹目录
  function getFolderListData(parentDirectory: string | undefined, isInit = true, parentNode?: DataNode) {
    const request = props.resource ? Folder?.getResourceList : Folder?.getList;
    return request?.(setParentFolderId(parentDirectory)).then((res) => {
      const result = (res.items || []).map((item: DataNode) => {
        item.key = item.id;
        item.isLeaf = item.type !== FolderEnum[FolderEnum.Directory];
        item.parentNode = parentNode;
        return item;
      });
      if (isInit) {
        treeData.value = result;
        if (!parentDirectory && !props.notRoot) {
          treeData?.value?.unshift({
            id: '-1',
            key: '-1',
            name: t('layout.rootDirectory'),
            isLeaf: true,
            isAll: true,
          });
        }
      }
      return result;
    });
  }

  function setParentFolderId(parentDirectory) {
    return parentDirectory === -1 ? undefined : parentDirectory || undefined;
  }

  // 创建文件夹
  function createFolder(formState) {
    formState.parentDirectory = setParentFolderId(formState.parentDirectory);
    return Folder?.create?.(formState);
  }

  // 更新文件夹
  function updateFolder(formState) {
    formState.parentDirectory = setParentFolderId(formState.parentDirectory);
    return Folder?.update?.(formState);
  }

  // 移动到
  function moveFolder(parentDirectory, chooseFolderId) {
    return Folder?.move?.(setParentFolderId(parentDirectory), chooseFolderId);
  }

  // 异步加载文件夹
  function onLoadData(node) {
    if (node?.isLeaf) {
      return Promise.resolve();
    }
    return new Promise((resolve) => {
      getFolderListData(node?.id, !node, node)?.then((data) => {
        if (node) {
          node.dataRef.children = data || [];
          node.dataRef.isLeaf = false;
        }
        treeData.value = [...(treeData.value ?? [])];
        resolve(null);
      });
    });
  }

  return {
    registerCreateFolder,
    registerMoveFolder,
    operationList,
    handleCreateFolder,
    createFolder,
    updateFolder,
    moveFolder,
    handleRefrush,
    treeData,
    onLoadData,
    getFolderListData,
  };
}
