# 上传附件

<p style="font-size:26px">代码演示</p>

## 基础

<demo src="../demo/UploadFile/basic.vue" title="基础"></demo>

### <span id='options'>options</span>

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| tableColumn | 表格列配置 参考[表格](/components/Table.html)组件 | [] | -- | -- | 1.0 |
| allocation | 表格属性配置 参考[表格](/components/Table.html)组件 | {} | -- | -- | 1.0 |
| uploadProps | 附件属性配置 参考[上传附件](https://www.antdv.com/components/upload-cn/#Upload-)组件 | {} | -- | -- | 1.0 |
| file-list(v-model) | 表格数据源 | [FileList](#FileList)[] | -- | -- | 1.0 |
| placeholder | 无数据时的 placeholder | String | -- | 添加 | 1.0 |
| headerText | 标题 | String | -- | -- | 1.0 |
| disabled | 是否禁用 | Boolean | -- | false | 1.0 |
| uploadFile | 上传附件 | Function | -- | (fileData,getProcess(percent,fileData,cancel)) => {} | 1.0 |
| removeFile | 删除事件 | Function | -- | (file) => {} | 1.0 |

### <span id='FileList'>FileList</span>

| 参数         | 说明                 | 类型   | 可选值 | 默认值 | 版本 |
| ------------ | -------------------- | ------ | ------ | ------ | ---- |
| name         | 带文件类型文件名     | String | --     | --     | 1.0  |
| uid          | 唯一值               | String | --     | --     | 1.0  |
| size         | 文件大小             | Number | --     | --     | 1.0  |
| fileName     | 不带文件类型的文件名 | String | --     | --     | 1.0  |
| url          | 下载链接             | String | --     | --     | 1.0  |
| preview      | 预览链接             | String | --     | --     | 1.0  |
| creationTime | 创建时间             | Date   | --     | --     | 1.0  |
| username     | 上传用户             | String | --     | --     | 1.0  |
