import { TooltipPlacement } from 'ant-design-vue/es/tooltip';
import { PropType } from 'vue';
import { UserOptionItemType } from './type';
import { mutable } from '/@/utils/props';
import { QueryType } from '/#/global';
import { getProps as getSearchProps } from '/@/components/BasicSearchPopover/src/props';

export const getProps = () => ({
  ...getSearchProps(),
  value: {
    type: [Object, Array] as PropType<UserOptionItemType | UserOptionItemType[]>,
  },
  isIcon: {
    type: Boolean,
    default: false,
  },
  isText: {
    type: Boolean,
    default: false,
  },
  hideAvatar: {
    type: Boolean,
    default: false,
  },
  showArrow: {
    type: Boolean,
    default: false,
  },
  // 是否是简易模式
  isSimple: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
  },
  showText: {
    type: Boolean,
    default: false,
  },
  // 隐藏文本
  hideText: {
    type: String,
  },
  // 弹出位置
  placement: {
    type: String as PropType<TooltipPlacement>,
    default: 'bottomLeft',
  },
  // 搜索文本
  searchText: {
    type: String,
  },
  // 已选择用户样式class
  triggerClass: {
    type: String,
  },
  bordered: {
    type: Boolean,
    default: false,
  },
  // icon模式下的图标
  icon: {
    type: String,
    default: 'otd-icon-a-addassignee',
  },
  // 最小展示数量，超出隐藏
  maxShowUser: {
    type: Number,
    default: 3,
  },
  // 尺寸
  size: {
    type: String as PropType<'large' | 'middle' | 'small' | 'mini' | 'default'>,
    default: 'default',
  },
  getList: {
    type: Function as PropType<(data?) => Promise<UserOptionItemType[]>>,
  },
  // 是否为远程搜索
  remote: {
    type: Boolean,
    default: false,
  },
  // 远程搜索方法
  remoteMethod: {
    type: Function as PropType<(data?: QueryType) => Promise<UserOptionItemType[]>>,
  },
});
const emit = ['update:value'] as const;
export const getEmits = () => mutable(emit);
