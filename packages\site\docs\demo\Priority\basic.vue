<template>
  <OtdPriority
    class="title-priority"
    v-model:value="priority"
    tip-label="优先级"
    placeholder="请选择任务级别"
    clearable
    @change="(value) => handelChangePriority(value)"
    @clear="handelChangePriority(undefined)"
  />
  <OtdPriority
    v-model:value="priority"
    tip-label="优先级"
    @change="(value) => handelChangePriority(value)"
    @clear="handelChangePriority(undefined)"
  />
  <OtdPriority
    v-model:value="priority"
    tip-label="优先级"
    is-simple
    @change="(value) => handelChangePriority(value)"
    @clear="handelChangePriority(undefined)"
  />
</template>
<script lang="ts" setup>
  import { OtdPriority } from '@otd/otd-ui';
  import { ref } from 'vue';

  const priority = ref(undefined);
  function handelChangePriority(value) {
    console.log(value);
  }
</script>
<style lang="less" scoped></style>
