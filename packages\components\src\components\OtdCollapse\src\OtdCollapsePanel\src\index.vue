<template>
  <div class="otd-collapse-panel" :class="{ 'otd-collapse-panel-active': isActive }">
    <div class="otd-collapse-panel__header">
      <div class="otd-collapse-panel__header__sticky">
        <div class="otd-collapse-panel__header-arrow" @click="handleExpand">
          <i class="otdIconfont otd-icon-a-Statusdefault2 otd-arrow" :class="{ 'otd-arrow-active': isActive }"></i>
        </div>
        <div class="otd-collapse-panel__header-title">
          <slot name="header">
            {{ props.header }}
          </slot>
        </div>
      </div>
    </div>
    <div class="otd-collapse-panel__body">
      <slot />
      <slot name="footer" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed, unref } from 'vue';
  import { getCollapseConfig } from '../../useCollapse';

  const props = defineProps({
    value: {
      type: [String, Number],
      required: true,
    },
    header: {
      type: String,
    },
  });

  const emit = defineEmits(['expand']);

  const CollapseConfig = getCollapseConfig();
  const isActive = computed(() => {
    return unref(CollapseConfig?.activeKey)?.includes(props.value);
  });

  function handleExpand() {
    emit('expand', props.value);
    CollapseConfig?.setPanelActive(props.value);
  }
</script>
<style lang="less" scoped>
  @prefix: ~'otd-collapse-panel';
  .@{prefix} {
    border-radius: var(--otd-border-radius);
    background-color: var(--otd-basic-bg);
    &__header {
      background-color: var(--otd-basic-bg);

      border-radius: var(--otd-border-radius);
      position: sticky;
      top: 0;
      z-index: 5;
      &__sticky {
        padding: 12px 16px;
        height: 50px;
        width: fit-content;
        display: flex;
        align-items: center;
        column-gap: 6px;
        position: sticky;
        left: 0;
        z-index: 5;
      }
    }
    &__body {
      display: none;
    }
    &-active {
      .@{prefix}__header {
        border-bottom: 1px solid var(--otd-border-color);
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }
      .@{prefix}__body {
        display: block;
      }
    }
  }
</style>
