<template>
  <!-- 预览 -->

  <OtdPreview :content="content">
    <div v-html="content"></div>
  </OtdPreview>
</template>
<script lang="ts" setup>
  import { OtdPreview } from '@otd/otd-ui';
  import { ref } from 'vue';
  const content = ref(`<p>这是评论带图片
      <img src="https://tse4-mm.cn.bing.net/th/id/OIP-C.GC_ugX-TzPVR26SSxI1kZwHaE9?w=233&h=180&c=7&r=0&o=5&pid=1.7" alt="" width="175" height="165" />
      <img src="https://tse2-mm.cn.bing.net/th/id/OIP-C.7sAjIeoQYWnXV_QnuYs1jQHaEK?w=312&h=180&c=7&r=0&o=5&pid=1.7" alt="" width="175" height="165" />
    </p>`);
</script>
<style lang="less" scoped></style>
