import { ref, getCurrentInstance, ComponentInternalInstance, nextTick } from 'vue';
import { TableProps } from 'ant-design-vue';
import { Recordable } from '/#/global';

export function useSelectTemplate() {
  const { emit, proxy, props } = getCurrentInstance() as ComponentInternalInstance;
  const visible = ref(false);
  const confirmLoading = ref(false);
  const selectedKeys = ref([]);
  const expandedKeys = ref([]);
  const chooseTemplate = ref<string | undefined>(undefined);
  const loading = ref(false);
  const subtaskContent = ref([]);
  const selectRows = ref<Recordable[]>([]);
  const allTaskMap = new Map<string, Recordable>();
  const rowSelection: TableProps['rowSelection'] = {
    checkStrictly: false,
    onSelect: (_, __, selectedRows) => {
      selectRows.value = selectedRows;
    },
    onSelectAll: (_, selectedRows) => {
      selectRows.value = selectedRows;
    },
  };
  //获取模板扁平结构
  function getAllChildren(items: Recordable[]) {
    let children: Recordable[] = [];
    items.forEach((item) => {
      children.push(item);
      if (item.childTaskItems) {
        children = children.concat(getAllChildren(item.childTaskItems));
      }
      allTaskMap.set(item.id, item);
    });
    return children;
  }
  // 调用函数

  // 文件选择事件
  function handleSelect(data) {
    if (data.isLeaf) {
      chooseTemplate.value = data.id;
      loading.value = true;

      (props as any)
        .getTaskList?.(data)
        .then((res) => {
          subtaskContent.value = res;
          allTaskMap.clear();
          getAllChildren(subtaskContent.value);
          nextTick(() => {
            const { templateRef } = proxy?.$refs as any;
            templateRef.expandAll();
          });
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      chooseTemplate.value = undefined;
      subtaskContent.value = [];
    }
  }

  // 关闭弹窗后
  function handleAfterClose() {
    selectedKeys.value = [];
    expandedKeys.value = [];
    chooseTemplate.value = undefined;
    subtaskContent.value = [];
    selectRows.value = [];
    allTaskMap.clear();
    const { templateRef } = proxy?.$refs as any;
    templateRef.clearSelectedRowKeys();
    confirmLoading.value = false;
    visible.value = false;
  }

  // 取消事件
  function handleCancel() {
    emit('cancel');
    handleAfterClose();
  }
  // 确认事件
  function handleConfirm() {
    confirmLoading.value = true;
    const selects = selectRows.value;
    selects.forEach((item: any, index) => {
      // 如果有父级,且父级不存在,把父级添加上
      if (item.parentTaskId && !selects.find((task: any) => task.id === item.parentTaskId)) {
        const task = allTaskMap.get(item.parentTaskId);
        selects.splice(index, 0, task!);
      }
      // 如果有前置任务，且前置任务不在勾选列，则将前置任务的id设为null
      if (item.preTaskItemId) {
        const isExist = selects.some((task: any) => task.id === item.preTaskItemId);
        !isExist && (item.preTaskItemId = null);
      }
    });
    emit('confirm', selects, { resolve: handleAfterClose });
    return;
  }

  return {
    visible,
    confirmLoading,
    selectedKeys,
    expandedKeys,
    chooseTemplate,
    loading,
    subtaskContent,
    handleSelect,
    handleAfterClose,
    handleCancel,
    handleConfirm,
    selectRows,
    rowSelection,
  };
}
