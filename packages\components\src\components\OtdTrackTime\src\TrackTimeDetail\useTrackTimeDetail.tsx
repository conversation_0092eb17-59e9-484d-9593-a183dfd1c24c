import { getCurrentInstance, reactive, ref, unref } from 'vue';
import { useModalInner } from '/@/components/BasicModal';
import { useI18n } from '/@/hooks/web/useI18n';
import { Progress } from 'ant-design-vue';
import { ColumnsType } from 'ant-design-vue/lib/table';
import { OtdMoreAction } from '/@/components/OtdMoreAction';
import { OtdAvatar } from '/@/components/OtdAvatar';
import { useMessage } from '/@/hooks/web/useMessage';
import { Recordable } from '/#/global';
import { HourTypeEnum } from '../props';
import { ITaskHourDetailDto } from '../type';

export function useTrackTimeDetail() {
  const { props, emit } = getCurrentInstance()!;
  const { t } = useI18n();
  const { deleteConfirm } = useMessage();
  const [register, { changeLoading }] = useModalInner(() => {
    loadHourDetail();
  });

  const hourDetailSource = reactive<ITaskHourDetailDto>({});
  const hourDetail = reactive<ITaskHourDetailDto>({});

  const tableColumns: ColumnsType = [
    // 工时执行者
    {
      title: t('common.trackTime.WorkHourExecutor'),
      dataIndex: 'userName',
      align: 'left',
      width: '150px',
      customRender: ({ value, record }) => (
        <div class="otd-box-left">
          <OtdAvatar url={record.userAvatar} />
          <span class="ml-6px">{value}</span>
        </div>
      ),
    },
    // 计划工时
    {
      title: t('common.trackTime.plannedTrackingTime'),
      dataIndex: 'planSpendHours',
      width: '120px',
      customRender: ({ record }) => (
        <span>
          {getCacheValue(record, 'planSpendHours')} {t('common.hours')}
        </span>
      ),
    },
    // 实际工时
    {
      title: t('common.trackTime.actualTrackingTime'),
      dataIndex: 'realSpendHours',
      width: '120px',
      customRender: ({ record }) => (
        <span>
          {getCacheValue(record, 'realSpendHours')} {t('common.hours')}
        </span>
      ),
    },
    // 进度
    {
      title: `${t('common.trackTime.actual')}/${t('common.trackTime.planned')}`,
      width: '150px',
      customRender: ({ record }) => {
        const actual = getCacheValue(record, 'realSpendHours');
        const planned = getCacheValue(record, 'planSpendHours');
        return (
          <div class="otd-track-time-detail__action">
            <Progress
              strokeWidth={6}
              percent={computedPercent(actual, planned)}
              show-info={false}
              status={Number(actual) > Number(planned) ? 'exception' : undefined}
            />
            <OtdMoreAction
              data={record}
              disabled={props.disabled as boolean}
              actionType="icon"
              actions={[
                // 删除
                {
                  id: 2,
                  color: '#ED6F6F',
                  name: t('common.delText'),
                  icon: 'icon-shanchu',
                  strict: true,
                  action: (data) => {
                    deleteConfirm(() => {
                      emit('delete-detail', {
                        data,
                        reload: () => {
                          loadHourDetail();
                          emit('reload', data);
                        },
                      });
                    });
                  },
                },
              ]}
            />
          </div>
        );
      },
    },
  ];

  const hourCacheMap = {
    [HourTypeEnum.Plan]: 'planSpendHoursCache',
    [HourTypeEnum.Real]: 'realSpendHoursCache',
  };

  function setPrecision(first, second, pre = 10) {
    const result = first * pre + second * pre;
    return result / pre;
  }

  function getCacheValue(item, key) {
    return unref(currentCategory) ? item[`${key}Cache`] : item[key];
  }

  function setHourDetail() {
    let planSpendHoursCache = 0,
      realSpendHoursCache = 0;
    if (unref(currentCategory)) {
      const users = hourDetailSource.users?.map((item) => {
        item.planSpendHoursCache = 0;
        item.realSpendHoursCache = 0;
        item.taskHoursCache = item.taskHours?.filter((record) => {
          const result = record.categoryCode === unref(currentCategory);
          if (result) {
            item[hourCacheMap[record.hourType!]]! = setPrecision(
              item[hourCacheMap[record.hourType!]],
              record.workHour ?? 0,
            );
          }
          return result;
        });
        planSpendHoursCache = setPrecision(planSpendHoursCache, item.planSpendHoursCache);
        realSpendHoursCache = setPrecision(realSpendHoursCache, item.realSpendHoursCache);
        return item;
      });

      return {
        ...hourDetailSource,
        planSpendHoursCache,
        realSpendHoursCache,
        users,
      };
    } else {
      return hourDetailSource;
    }
  }

  const currentCategory = ref<string | undefined>(undefined);
  function handleChangeCategory() {
    Object.assign(hourDetail, setHourDetail());
  }

  function loadHourDetail() {
    changeLoading(true);
    (props as Recordable)?.loadDetail?.().then((res) => {
      Object.assign(hourDetailSource, res);
      Object.assign(hourDetail, setHourDetail());
      changeLoading(false);
    });
  }

  function computedPercent(current, max) {
    return Number(((Number(current) / Number(max)) * 100).toFixed(2));
  }

  function getHourType(type) {
    const hourTypeMap = {
      [HourTypeEnum.Plan]: t('common.trackTime.planned'),
      [HourTypeEnum.Real]: t('common.trackTime.actual'),
    };
    return hourTypeMap[type];
  }
  function mergeHourInfo(item, record) {
    const { userId, userName, userAvatar } = record;
    return Object.assign(item, { userId, userName, userAvatar });
  }

  function handleEditRecord(item, record) {
    if (props.disabled) return;
    (props as Recordable)?.actions?.[0].action({ data: mergeHourInfo(item, record) });
  }

  return [
    register,
    {
      currentCategory,
      getCacheValue,
      handleChangeCategory,
      hourDetail,
      tableColumns,
      computedPercent,
      getHourType,
      loadHourDetail,
      mergeHourInfo,
      handleEditRecord,
    },
  ] as const;
}
