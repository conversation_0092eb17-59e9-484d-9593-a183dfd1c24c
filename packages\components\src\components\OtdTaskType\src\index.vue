<template>
  <div class="otd-task-type" :class="{ 'is-select-mode': isSelectMode }">
    <Dropdown :disabled="disabled" trigger="click" :overlay-style="{ minWidth: '180px' }">
      <div
        class="otd-task-type__trigger otd-box-left"
        :class="{ 'otd-task-type-disabled': disabled, 'is-icon': isIconMode }"
      >
        <template v-if="modelValue">
          <TaskTypeTrigger :is-icon="isIconMode" :value="currentValue" />
        </template>
        <div class="placehoder-hover placeholder-text" style="flex: 1" v-else>
          {{ placeholder ?? t('common.chooseText') }}
        </div>
        <DownOutlined class="placeholder-text" :style="{ fontSize: '12px' }" v-if="!isIconMode" />
      </div>
      <template #overlay>
        <Menu class="otd-task-type__dropdown" @click="handleSelectType">
          <slot name="title">
            <div class="otd-task-type__dropdown-title">
              <div class="otd-task-type__dropdown-title-left otd-box-left">
                <span>{{ title }}</span>
                <Tooltip :title="help" v-if="help"> <QuestionCircleFilled /> </Tooltip>
              </div>
              <div class="otd-task-type__dropdown-title-right"><slot name="other"></slot></div>
            </div>
          </slot>
          <MenuItem v-for="item in options" :key="item.value">
            <div class="otd-task-type__item otd-box-left">
              <div class="otd-box-left">
                <OtdIconPark :type="item.icon" theme="filled" />
                <span>{{ item.label }}</span>
              </div>
              <CheckOutlined v-if="currentValue.value === item.value" />
            </div>
          </MenuItem>
        </Menu>
      </template>
    </Dropdown>
  </div>
</template>
<script lang="ts" setup>
  import { Dropdown, Menu, MenuItem, Tooltip } from 'ant-design-vue';
  import { CheckOutlined, DownOutlined, QuestionCircleFilled } from '@ant-design/icons-vue';
  import { computed, nextTick, unref } from 'vue';
  import { OtdIconPark } from '/@/components/BasicIcon';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { TaskTypeOptionType } from './type';
  import { getProps } from './props';
  import TaskTypeTrigger from './TaskTypeTrigger.vue';

  const props = defineProps(getProps());
  const emit = defineEmits(['update:value', 'change']);
  const { t } = useI18n();

  const modelValue = computed({
    get: () => {
      if (props.defaultValue && !props.value) {
        emit('update:value', props.defaultValue);
      }
      return props.value;
    },
    set: (val) => emit('update:value', val),
  });

  const ValueMap = computed(() => new Map(props.options?.map((item) => [item.value, item])));
  const currentValue = computed(
    () => ((modelValue.value && ValueMap.value.get(modelValue.value)) ?? {}) as TaskTypeOptionType,
  );

  const isSelectMode = computed(() => props.mode === 'select');
  const isIconMode = computed(() => props.mode === 'icon');

  function handleSelectType({ key }) {
    modelValue.value = key;
    nextTick(() => emit('change', unref(currentValue)));
  }
</script>
<style lang="less" scoped>
  @prefix: ~'otd-task-type';
  .@{prefix} {
    &.is-select-mode {
      width: 100%;
      height: 100%;
      .@{prefix}__trigger {
        width: 100%;
        height: 100%;
        &:hover {
          background-color: inherit;
          border: 1px solid var(--otd-border-primary);
        }
      }
    }
    &__trigger {
      width: fit-content;
      column-gap: 6px;
      cursor: pointer;
      border-radius: var(--otd-default-radius);
      line-height: 1;
      padding: 4px;
      &:not(.is-icon) {
        padding: 6px;
        border: 1px solid var(--otd-border-gray);
      }
      .i-icon {
        font-size: 14px;
      }
      > span {
        font-size: 12px;
      }
      &:hover {
        background-color: var(--otd-gray3-hover);
      }
      .placehoder-hover {
        font-size: 14px;
      }
    }
    &-disabled {
      cursor: no-drop;
      background-color: inherit !important;
      border-color: var(--otd-border-gray) !important;
    }
    &__dropdown {
      &-title {
        font-size: 14px;
        font-weight: bold;
        padding: 6px 12px 4px 12px;
        display: flex;
        justify-content: space-between;
        color: var(--otd-header-text);
        &-left {
          column-gap: 6px;
          .anticon-question-circle {
            font-size: 12px;
            color: var(--otd-header-text);
          }
        }
        &-right {
          font-weight: normal;
        }
      }
    }
    &__item {
      column-gap: 10px;
      .otd-box-left {
        column-gap: 6px;
        flex: 1;
      }
    }
  }
</style>
