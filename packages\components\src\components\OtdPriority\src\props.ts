import { PropType } from 'vue';
import { mutable } from '/@/utils/props';
import { ColorOptionType } from '../../OtdColor';

export const getProps = () => ({
  value: {
    type: [String, Number],
  },
  defaultValue: {
    type: Object as PropType<ColorOptionType>,
  },
  placeholder: {
    type: String,
  },
  tipLabel: {
    type: String,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  clearable: {
    type: Boolean,
    default: false,
  },
  isSimple: {
    type: Boolean,
    default: false,
  },
  options: {
    type: Object as PropType<ColorOptionType[]>,
  },
});

const emit = ['update:value', 'change', 'clear'] as const;

export const getEmits = () => mutable(emit);
