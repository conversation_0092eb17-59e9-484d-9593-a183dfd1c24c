import { ExtractPropTypes } from 'vue';
import { OtdHistoryFieldsType } from '/@/components/OtdHistory';
import { getProps, RelateEnum } from './props';
import { Recordable } from '/#/global';

export type OtdHistoryCardFieldsType = OtdHistoryFieldsType & {
  relatedId: string;
  relatedTitle: string;
  relatedType: string;
  relatedName: string;
  i18NRelatedType: string;
  i18NSubRelatedType: string;
  status: string;
};

export type OtdHistoryCardPropsType = ExtractPropTypes<ReturnType<typeof getProps>>;

export type OtdHistoryCardDataType = {
  relatedId?: string;
  relatedTitle?: string;
  relatedType?: RelateEnum;
  relatedName?: string;
  i18NRelatedType?: string;
  i18NSubRelatedType?: string;
  status?: number;
  taskType?: number;
  list?: Recordable[];
};
