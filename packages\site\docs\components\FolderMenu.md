## 文件夹目录

<demo src="../demo/FolderMenu/basic.vue" title="文件夹目录"></demo>

## 属性

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| title | 标题 | String | -- |  | 1.0 |
| data | 数据 | DataNode[] | -- | -- | 1.0 |
| expanded | 展开数据 | String[] | -- | -- | 1.0 |
| selected | 选择数据 | String[] | -- | -- | 1.0 |
| hideAction | 隐藏操作 | Boolean | -- | -- | 1.0 |
| resource | 查询包含非文件夹的数据 | Boolean | -- | -- | 1.0 |
| actions | 自定义操作 | [MoreActionItem](/components/MoreAction.html#actions)[] | -- | -- | 1.0 |
| loadData | 数据请求接口 | Function | -- | -- | 1.0 |
| folderClick | 文件夹点击 | Boolean | -- | -- | 1.0 |
| notRoot | 是否显示根目录 | Boolean | -- | -- | 1.0 |
| folderRequest | 文件夹数据请求配置 | [FolderRequestType](#folderrequesttype-类型说明) | -- | -- | 1.0 |

## FolderRequestType 类型说明

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| getList | 获取列表接口 | (parentDirectory: string) => Promise\<any\> | -- |  | 1.0 |
| getResourceList | 获取带资源列表接口 | (parentDirectory: string) => Promise\<any\> | -- |  | 1.0 |
| create | 创建接口 | (formState) => Promise\<any\> | -- |  | 1.0 |
| update | 更新接口 | (formState) => Promise\<any\> | -- |  | 1.0 |
| delete | 删除接口 | (id: string) => Promise\<any\> | -- |  | 1.0 |
| move | 移动接口 | (parentDirectory: string, chooseFolderId: string) => Promise\<any\> | -- |  | 1.0 |
