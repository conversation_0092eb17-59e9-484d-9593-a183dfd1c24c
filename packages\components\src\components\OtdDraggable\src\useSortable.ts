import Sortable from 'sortablejs';

export function useSortable(getTarget: () => HTMLElement, options?) {
  //初始化表格拖动
  const initSortable = () => {
    const target = getTarget();
    new Sortable(target, {
      group: 'name',
      handle: '.ant-table-row.ant-table-row-level-0',
      draggable: '.ant-table-row.ant-table-row-level-0',
      ghostClass: 'blue-background-class',
      animation: 150,
      sort: true,
      ...options,
    });
  };
  return {
    initSortable,
  };
}
