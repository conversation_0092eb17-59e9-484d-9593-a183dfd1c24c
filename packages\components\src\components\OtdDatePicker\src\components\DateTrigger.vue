<template>
  <Tooltip :open="isSimple ? undefined : false" :title="placeholderText">
    <div class="otd-date-picker-trigger placeholder-hover">
      <i class="otdIconfont otd-icon-riqi placeholder-text"></i>
      <div class="otd-date-picker-trigger__placeholder placeholder-text" v-if="isNotChooseDate && !isSimple">
        <span>{{ placeholderText }}</span>
      </div>
      <div class="otd-date-picker-trigger__selected" v-else-if="!isNotChooseDate">
        <span :class="getDateTipColor(dates[0], 'start')" v-if="dates[0] && !hideStartTime">
          {{ getDateFormat(dates[0]!) }}
        </span>
        <span
          :class="getDateTipColor(dates[1], 'end')"
          v-if="(dates[1] && !hideEndTime) || (hideStartTime && hideEndTime)"
        >
          {{ getDateFormat(dates[1]!) }}
        </span>
        <i class="otdIconfont otd-icon-lingdang-xianxing" v-if="remind"></i>
        <i class="otdIconfont otd-icon-caozuo-xunhuan1" v-if="repeat"></i>
      </div>
    </div>
  </Tooltip>
</template>
<script lang="ts" setup>
  import type { DateValueType } from '../type';
  import { computed, type PropType } from 'vue';
  import { getDateFormat } from '../useDatePicker';
  import dayjs from 'dayjs';
  import { DATE_FORMAT, DATE_TIME, isToTime } from '/@/tool';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { Tooltip } from 'ant-design-vue';

  const props = defineProps({
    dates: {
      type: Array as unknown as PropType<[DateValueType, DateValueType]>,
      default: () => [null, null],
    },
    notTipColor: {
      type: [Boolean, Object] as PropType<boolean | { start: boolean; end: boolean }>,
      default: false,
    },
    hideStartTime: {
      type: Boolean,
      default: false,
    },
    hideEndTime: {
      type: Boolean,
      default: false,
    },
    remind: String,
    repeat: String,
    placeholder: String,
    isSimple: {
      type: Boolean,
      default: false,
    },
  });

  const { t } = useI18n();

  const placeholderText = computed(() => props.placeholder ?? t('common.datePicker.placeholder'));

  const isNotChooseDate = computed(() => {
    const { dates } = props;
    return (
      !(dates && (dates?.[0] ?? dates?.[1])) ||
      (props.hideStartTime && !dates?.[1]) ||
      (props.hideEndTime && !dates?.[0])
    );
  });

  // 判断时间
  function judgTime(date, action, type) {
    const time = type === 'start' ? '00:00' : '23:59:59';
    const value = dayjs(date);
    const content = isToTime(value) ? value.format(DATE_TIME) : `${value.format(DATE_FORMAT)} ${time}`;
    const today = dayjs();
    const judgMap = {
      // 今天并未超时
      today: () => today.isSame(content, 'd') && !judgMap['timeout'](),
      // 超时
      timeout: () => today.isAfter(content, 'ms'),
    };
    return judgMap[action]();
  }
  // 获取日期提示颜色
  function getDateTipColor(value, type) {
    return getShowDateTip(type)
      ? ''
      : judgTime(value, 'timeout', type)
      ? 'is-timeout'
      : judgTime(value, 'today', type)
      ? 'is-today'
      : '';
  }
  // 获取是否展示日期颜色提示
  function getShowDateTip(type) {
    if (typeof props.notTipColor === 'boolean') {
      return props.notTipColor;
    }
    return props.notTipColor![type];
  }
</script>
<style lang="less" scoped>
  .otd-date-picker-trigger {
    &__placeholder {
      display: flex;
      align-items: center;
      column-gap: 4px;
    }

    &__selected {
      display: flex;
      align-items: center;
      column-gap: 2px;
      > span {
        & + span::before {
          content: '-';
          color: var(--otd-basic-text);
        }
      }
      .is-today {
        color: var(--otd-primary-text);
      }
      .is-timeout {
        color: var(--otd-error-color);
      }
      .otdIconfont {
        font-size: 14px;
      }
    }
  }
</style>
