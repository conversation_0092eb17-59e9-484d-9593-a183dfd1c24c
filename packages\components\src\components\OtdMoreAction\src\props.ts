import { Trigger } from 'ant-design-vue/es/dropdown/props';
import { ComponentSize } from '/@/utils/types';
import { PropType } from 'vue';
import { ActionTypeItem, MoreActionItem } from './type';
import { Recordable } from '/#/global';

export const getProps = () => ({
  // 数据
  data: {
    type: Object as PropType<Recordable>,
    default: null,
  },
  // 下标
  index: {
    type: Number,
    default: undefined,
  },
  // 数据列表
  list: {
    type: Array as PropType<Recordable[]>,
    default: () => [],
  },
  // 操作
  actions: {
    type: Array as PropType<MoreActionItem[]>,
    default: () => [],
  },
  // 展开数量，超出隐藏
  expandNumber: {
    type: Number,
    default: 0,
  },
  // 间隔
  interval: {
    type: Number,
    default: 10,
  },
  // 禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  white: {
    type: Boolean,
    default: false,
  },
  // 隐藏展开文本
  hideExpandName: {
    type: Boolean,
    default: false,
  },
  // 关闭后是否销毁 Dropdown
  destroyPopupOnHide: {
    type: Boolean,
    default: false,
  },
  // 隐藏样式类
  hideClass: {
    type: String,
    default: 'hide-action',
  },
  // 触发方式
  trigger: {
    type: Array as PropType<Trigger[]>,
    default: () => ['click'],
  },
  // 尺寸
  size: {
    type: String as PropType<ComponentSize>,
    default: 'default',
  },
  // 按钮类型
  actionType: {
    type: String as PropType<ActionTypeItem>,
    default: 'btn',
  },
  // 是否搜索
  search: {
    type: Boolean,
    default: false,
  },
  // 是否外部搜索
  emitSearch: {
    type: Boolean,
    default: false,
  },
});
