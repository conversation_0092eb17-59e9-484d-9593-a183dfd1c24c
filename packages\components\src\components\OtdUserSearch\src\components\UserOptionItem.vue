<template>
  <div class="otd-user-search__item-info" :title="item.label">
    <OtdAvatar :size="size" :url="item.img || getAvatar?.(item.value)" v-if="type === AtMentionEnum.User" />
    <i class="otdIconfont otd-icon-bx_task" v-else></i>
    <span class="otd-user-search__item-info__name otd-truncate">{{ item.label }}</span>
  </div>
</template>
<script lang="ts" setup>
  import { UserOptionItemType } from '../type';
  import { PropType } from 'vue';
  import { OtdAvatar } from '/@/components/OtdAvatar';
  import { AtMentionEnum } from '/@/components/OtdTinymce/src/plugins/mention/MentionPopover/useMentionPopover';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';

  defineProps({
    item: {
      type: Object as PropType<UserOptionItemType>,
      required: true,
    },
    type: {
      type: String as PropType<AtMentionEnum>,
      default: AtMentionEnum.User,
    },
    size: {
      type: String,
      default: '30px',
    },
  });

  const { getGlobalProvide } = useGlobalConfig();
  const { getAvatar } = getGlobalProvide();
</script>
<style lang="less" scoped>
  @user-search: ~'@{namespace}-user-search';
  .@{user-search}__item {
    &-info {
      display: flex;
      align-items: center;
      column-gap: 8px;
      flex: 1;
      overflow: hidden;
      &__name {
        flex: 1;
        font-size: 14px;
        color: var(--otd-basic-text);
        line-height: 16px;
      }
      .otdIconfont {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: middle;
        background-color: var(--otd-gray4-color);
        width: 24px;
        height: 24px;
        color: var(--otd-body-text);
        border-radius: var(--otd-circle-radius);
        line-height: 1;
      }
    }
  }
</style>
