<template>
  <div class="otd-history-timeline">
    <div class="otd-history-timeline__header">
      <div class="otd-history-timeline__search">
        <FormItem :label="label">
          <Input v-model:value="keyword" :placeholder="placeholder" allowClear />
        </FormItem>
      </div>
      <HistoryFilter
        v-bind="$props"
        v-model:checkedAll="checkedAll"
        v-model:value="currenLightAction"
        :history-options="getHistoryOptions"
      />
    </div>
    <Timeline>
      <TimelineItem v-for="(item, index) in dataList" :key="index" class="otd-history-timeline-group" color="gray">
        <template #dot>
          <OtdAvatar :url="getAvatar(item[getFields.creatorId])" size="26px" />
        </template>
        <div class="otd-history-timeline-item">
          <div class="otd-history-timeline-item__info">
            <span class="otd-title-text" v-if="item[getFields.creatorName]">{{ item[getFields.creatorName] }}</span>
            <span class="otd-desc-text">{{ formatToTime(item[getFields.creationTime]) }}</span>
          </div>
          <div class="otd-history-timeline-item__content">
            <HistoryContent :data="item" :fields="getFields" :historyOptions="getHistoryOptions" />
          </div>
        </div>
      </TimelineItem>
    </Timeline>
  </div>
</template>
<script lang="ts" setup>
  import { FormItem, Input, Timeline, TimelineItem } from 'ant-design-vue';
  import { getProps } from '../props';
  import { computed, ref, unref } from 'vue';
  import HistoryFilter from './HistoryFilter/index.vue';
  import { OtdAvatar } from '/@/components/OtdAvatar';
  import { formatToTime } from '/@/tool';
  import { useHistoryTimeline, HistoryContent } from './useHistoryTimeline';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';

  const props = defineProps(getProps());

  const { getGlobalProvide } = useGlobalConfig();
  const { getAvatar } = getGlobalProvide();
  const { getFields, DefaultHistoryOptions } = useHistoryTimeline();
  const keyword = ref('');
  const checkedAll = ref(true);
  const getHistoryOptions = computed(() =>
    Object.assign({}, props.historyOptions ?? DefaultHistoryOptions, props.moreHistoryOptions),
  );
  const currenLightAction = ref<string[]>([]);

  const filterData = computed(() =>
    props.data.filter((item) => unref(currenLightAction).includes(item.type.toString())),
  );
  const dataList = computed(() => {
    if (!keyword.value && checkedAll.value) {
      //没有关键字，且全选状态 返回所有值
      return props.data;
    } else {
      const { creatorName, content } = unref(getFields);
      //其余情况根据两者的交叉条件进行过滤
      return unref(filterData).filter(
        (item) => item?.[creatorName]?.includes(keyword.value) || item?.[content]?.includes(keyword.value),
      );
    }
  });
</script>
<style lang="less" scoped>
  .otd-history-timeline {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    &__header {
      display: flex;
      margin-bottom: 20px;
      justify-content: space-between;
      .otdIconfont {
        cursor: pointer;
      }
    }
    &__search {
      display: flex;
      .ant-form-item {
        margin-bottom: 0;
        .ant-input {
          width: 160px;
        }
      }
    }
    &-group {
      display: flex;
      align-items: center;
      :deep(.ant-history-item-head) {
        background-color: inherit;
      }
      :deep(.ant-timeline-item-content) {
        max-width: calc(100% - 26px);
      }
      :deep(.otd-time-line-content) {
        &.otd-column-gap {
          > span:first-of-type {
            white-space: nowrap;
          }
        }
      }
    }
    &-item {
      display: flex;
      flex-direction: column;
      margin-left: 6px;
      line-height: 26px;

      &__info {
        display: flex;
        align-items: center;
        column-gap: 10px;
        .otd-title-text {
          font-weight: normal;
        }
        .otd-desc-text {
          font-size: 14px;
        }
      }
      &__content {
        margin-top: 8px;
        color: var(--otd-basic-text);
      }
    }
    :deep(.ant-timeline) {
      padding-top: 10px;
      padding-left: 10px;
      flex: 1;
      overflow: auto;
      .ant-timeline-item-tail {
        inset-block-start: 26px;
        height: calc(100% - 42px);
        color: var(--otd-border-gray);
      }
      .ant-timeline-item-head {
        background-color: inherit;
      }
    }
  }
</style>
