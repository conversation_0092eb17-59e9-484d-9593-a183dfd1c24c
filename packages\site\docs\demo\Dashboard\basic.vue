<template>
  <OtdDashboardCard
      v-for="item in chartList"
      :key="item.id"
      v-bind="item"
      @register="(dashboard) => registerDashboard(item.id as string, dashboard)"
  />
</template>
<script lang="ts" setup>
import { OtdDashboardCard } from '@otd/otd-ui';
import { useData } from './userData';

const { registerDashboard, chartList } = useData();

</script>
<style lang="less" scoped></style>
