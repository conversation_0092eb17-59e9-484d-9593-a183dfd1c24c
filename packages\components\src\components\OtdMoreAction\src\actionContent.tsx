import { MoreActionItem } from './type';

type PropsType = {
  item: MoreActionItem;
  data: any;
  hideExpandName?: boolean;
};

export function ActionContent({ item, data, hideExpandName = false }: PropsType) {
  if (item.customRender) return item.customRender({ data, item });
  else
    return (
      <>
        <i class={`otdIconfont ${item.icon}`} style={getCssStyle(item)}></i>
        {item.name && !hideExpandName ? <span class="add-icon__text">{getName(item, data)}</span> : undefined}
      </>
    );
}

function getCssStyle(item) {
  return {
    color: item.color,
    'font-size': item.iconSize || undefined,
  };
}

export const getName = (item: MoreActionItem, data) => {
  if (item.name instanceof Function) {
    return item.name(data);
  } else {
    return item.name;
  }
};
