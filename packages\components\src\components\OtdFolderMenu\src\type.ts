import type { ExtractPropTypes } from 'vue';
import { getProps } from './props';

export type FolderRequestType = Partial<{
  // 获取列表
  getList: (parentDirectory?: string) => Promise<any>;
  // 获取带资源列表
  getResourceList: (parentDirectory?: string) => Promise<any>;
  // 创建
  create: (formState) => Promise<any>;
  // 更新
  update: (formState) => Promise<any>;
  // 删除
  delete: (id: string) => Promise<any>;
  // 移动
  move: (parentDirectory: string, chooseFolderId: string) => Promise<any>;
}>;

export type FolderPropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
