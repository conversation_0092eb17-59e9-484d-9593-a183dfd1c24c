<template>
  <div class="otd-notify-page" v-loading="loading">
    <List class="otd-card-list__group" bordered>
      <template v-if="list.length > 0">
        <div>
          <template v-for="(item, index) in list" :key="item.id">
            <ListItem class="list-item">
              <ListItemMeta>
                <template #description>
                  <NoticeCard :item="item" :index="index" :list="list" />
                </template>
              </ListItemMeta>
            </ListItem>
          </template>
        </div>
        <div class="otd-notify-page__action">
          <!-- 全部已读 -->
          <Button type="link" @click="handleSetReadAll">
            {{ t('common.readAll') }}
          </Button>
          <!-- 分页 -->
          <Pagination
            v-model:current="currentPage"
            :total="total"
            :defaultPageSize="pageSize"
            :showSizeChanger="false"
            size="small"
            simple
            showLessItem
            @change="onLoadMore"
          />
        </div>
      </template>
      <div class="otd-notify-page__empty" v-else>
        <Empty />
      </div>
    </List>
  </div>
</template>
<script lang="ts" setup>
  import { PropType, ref } from 'vue';
  import { List, ListItem, ListItemMeta, Button, Pagination, Empty, message } from 'ant-design-vue';
  import { MessageType, PagingNotificationListOutput } from './data';
  import { useNotifyStorage } from '/@/storage/notifyStorage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
  import NoticeCard from './NoticeCard.vue';
  import { MessageKeyMap } from '/@/hooks/web/useSignalR';

  const props = defineProps({
    list: {
      type: Array as PropType<PagingNotificationListOutput[]>,
      default: () => [],
    },
    total: {
      type: Number,
      default: 0,
    },
    pageSize: {
      type: Number,
      default: 5,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    type: {
      type: [Number, String] as PropType<MessageType>,
      default: MessageType.Common,
    },
  });
  const emit = defineEmits(['load-more']);
  const { t } = useI18n();
  const { setNotifyCount } = useNotifyStorage();
  const { getGlobalProvide } = useGlobalConfig();
  const { readAllNotify } = getGlobalProvide();

  // 全部已读
  function handleSetReadAll() {
    readAllNotify?.(props.type)?.then(() => {
      message.success(t('common.operationSuccess'));
      props.list.map((item) => (item.read = true));
      setNotifyCount(MessageKeyMap[props.type], 0);
    });
  }

  const currentPage = ref(1);
  function onLoadMore(page, pageSize) {
    emit('load-more', page, pageSize);
  }
</script>
<style lang="less" scoped>
  .otd-notify-page {
    min-width: 360px;
    padding: 8px;
    margin-top: 6px;
    padding-bottom: 0;
    &__empty {
      padding: 20px;
    }
    &__action {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
      :deep(.ant-pagination) {
        .ant-pagination-simple-pager {
          input {
            width: 40px;
          }
        }
      }
    }
  }
</style>
