<template>
  <Select
    v-model:value="timeValue"
    class="otd-select"
    popupClassName="otd-select-dropdown"
    :options="options"
    v-bind="$attrs"
    @select="handleTimeSelect"
  />
</template>
<script lang="ts" setup>
  import { Select } from 'ant-design-vue';
  import { ref, unref, watch } from 'vue';

  const props = defineProps({
    value: {
      type: [String, Number],
      default: undefined,
    },
    step: {
      type: Number,
      default: 15,
    },
    startHour: {
      type: Number,
      default: 0,
    },
    endHour: {
      type: Number,
      default: 24,
    },
    maxMinute: {
      type: Number,
      default: 60,
    },
    defaultValue: {
      type: String,
      default: undefined,
    },
    hideFirst: {
      type: <PERSON>olean,
      default: false,
    },
  });

  const emits = defineEmits(['update:value', 'select']);

  const timeValue = ref<string | number | undefined>(undefined);

  const options = ref<{ value: string }[]>([]);

  // 初始化时间选择数据
  function initTimePickerData() {
    let minute = 0;
    for (let i = props.startHour; i < props.endHour; i++) {
      const hour = padStart(i);
      while (1) {
        options.value.push({ value: `${hour}:${padStart(minute)}` });
        minute += props.step;
        minute = minute >= props.maxMinute ? 0 : minute;
        if (minute === 0) break;
      }
    }
    if (props.hideFirst) {
      options.value.shift();
    }
  }

  // 不足补0
  function padStart(value) {
    const length = props.endHour.toString().length;
    return value.toString().padStart(length, '0');
  }

  initTimePickerData();

  if (props.defaultValue && !props.value) {
    timeValue.value = props.defaultValue.toString();
    emits('update:value', unref(timeValue));
  }

  watch(
    () => props.value,
    (value) => {
      timeValue.value = value;
    },
    { immediate: true },
  );

  function handleTimeSelect(...args) {
    emits('update:value', unref(timeValue));
    emits('select', ...args);
  }
</script>
<style lang="less" scoped></style>
