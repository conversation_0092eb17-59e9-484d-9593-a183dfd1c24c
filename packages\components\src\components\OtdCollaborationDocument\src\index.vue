<template>
  <div class="otd-collaboration-document">
    <div class="otd-collaboration-document__header">
      <div class="otd-collaboration-document__header-title">{{ headerText }}</div>
      <OtdMoreAction :expand-number="1" :actions="topActions" action-type="icon" />
    </div>

    <div class="otd-collaboration-document__body">
      <template v-if="documentData.length > 0">
        <OtdTable :data-source="documentData" @register="register"> </OtdTable>
      </template>

      <div v-else class="placeholder-hover otd-action-btn" @click="handleAdd">
        <i class="otdIconfont otd-icon-add-2"></i>
        <span>{{ props.placeholder || t('common.add') }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
  import { OtdMoreAction } from '/@/components/OtdMoreAction/index';
  import { watch } from 'vue';
  import { OtdTable, useTable } from '/@/components/OtdTable/index';
  import { getProps, getEmits } from './props';
  import { useCollaborationDocument } from './useCollaborationDocument';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const { documentData, columns, handleAdd, topActions } = useCollaborationDocument();
  const props = defineProps(getProps());
  const emits = defineEmits(getEmits());

  watch(
    () => props.value,
    (value) => {
      if (value && Array.isArray(value)) {
        documentData.value = JSON.parse(JSON.stringify(value)) || [];
      }
    },
    { immediate: true, deep: true },
  );

  const [register] = useTable(
    Object.assign(
      {
        columns: columns.concat(props.tableColumn),
        pagination: {
          hideOnSinglePage: true,
        },
        bordered: true,
        rowKey: 'key',
      },
      { ...props.allocation },
    ),
  );
</script>
<style lang="less" scoped>
  .otd-collaboration-document {
    &__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
      &-title {
        color: var(--otd-header-text);
        font-size: 14px;
      }
    }
    &__body {
      .otd-action-btn {
        width: 100%;
      }
      :deep(.otd-table) {
        .ant-table-wrapper {
          .ant-table-thead {
            tr .ant-table-cell {
              padding: 8px 16px;
            }
          }
          .ant-table-tbody {
            tr .ant-table-cell {
              padding: 10px 16px;
            }
          }
        }
      }
    }
  }
</style>
