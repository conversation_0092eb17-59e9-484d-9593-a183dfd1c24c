<template>
  <div class="otd-progress">
    <Tooltip :title="disabled ? title : tip">
      <Popconfirm
        v-model:open="visible"
        overlay-class-name="otd-popover hide-icon"
        placement="bottom"
        :disabled="disabled"
        :cancel-text="t('common.cancelText')"
        :ok-text="t('common.okText')"
        @confirm="handleConfirm"
        @open-change="handleVisibleChange"
      >
        <template #title>
          <div class="w-180px">
            <div class="mb-6px">{{ title }}</div>
            <InputNumber
              ref="inputRef"
              class="otd-input-number"
              v-model:value="percentValue"
              addon-after="%"
              :min="0"
              :max="99"
              :placeholder="title"
              @press-enter="handleConfirm"
            />
          </div>
        </template>
        <Progress :class="{ 'is-value': value > 0 }" :percent="value ?? 0" size="small" :stroke-width="16" />
      </Popconfirm>
    </Tooltip>
  </div>
</template>
<script lang="ts" setup>
  import { ref, unref } from 'vue';
  import { Progress, Tooltip, Popconfirm, InputNumber } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const props = defineProps({
    value: {
      type: Number,
      default: 0,
    },
    tip: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['update:value', 'confirm']);

  const percentValue = ref(0);
  const visible = ref(false);
  const inputRef = ref();

  // 确认事件
  function handleConfirm() {
    emit('update:value', unref(percentValue));
    emit('confirm', unref(percentValue));
    visible.value = false;
  }

  function handleVisibleChange(value) {
    if (value && !props.disabled) {
      percentValue.value = props.value ?? 0;
      setTimeout(() => {
        inputRef.value.focus();
      }, 100);
    }
  }
</script>
<style lang="less" scoped>
  .otd-input-number {
    margin-top: 10px;
    margin-bottom: 6px;
  }
  .otd-progress {
    width: 120px;
    .ant-progress {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-bottom: 0;
      &.is-value {
        :deep(.ant-progress-bg::after) {
          width: calc(100% + 2px) !important;
        }
      }
      :deep(.ant-progress-outer) {
        display: inline-flex;
        .ant-progress-inner {
          border-radius: var(--otd-small-radius);
          border: 1px solid var(--otd-help-color);
          overflow: unset;
          .ant-progress-bg {
            background-color: transparent;
            height: calc(100% + 2px) !important;
            margin: -1px;
            &::after {
              content: '';
              height: 100%;
              width: 100%;
              display: inline-flex;
              background-color: var(--otd-primary-text);
              border-radius: var(--otd-small-radius);
            }
          }
        }
      }
      :deep(.ant-progress-text) {
        display: inline-flex;
        align-items: center;
        font-size: 14px;
        margin-inline-start: 6px;
        .anticon {
          font-size: 12px;
        }
      }
    }
    .anticon-exclamation-circle {
      display: none;
    }
  }
</style>
