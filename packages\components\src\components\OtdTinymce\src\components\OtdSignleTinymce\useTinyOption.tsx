import { getCurrentInstance, unref } from 'vue';
import { RawEditorSettings } from 'tinymce';
import { ref } from 'vue';
import { useGlobalConfig } from '/@/hooks/web/useGlobalConfig';
import { AutoCompleteOptionType } from '../../plugins/mention/index';
import { UserOptionItemType } from '/@/components/OtdUserSearch/src/type';
import { SignleTinymceEmitType, SignleTinymcePropsType } from './type';
import { AtMentionEnum } from '../../plugins/mention/MentionPopover/useMentionPopover';
import { PRIMARY_COLOR } from '/@/setting';

export function useTinyOption(options) {
  const { emit, props } = getCurrentInstance() as unknown as {
    props: SignleTinymcePropsType;
    emit: SignleTinymceEmitType;
  };
  const { getGlobalProvide } = useGlobalConfig();
  const { getRootContainer } = getGlobalProvide();
  const rootBody = getRootContainer?.() ?? document.body;
  const atMentionData = ref<UserOptionItemType[]>();
  const AtMentionRequestMap = {
    [AtMentionEnum.User]: (filter, success) => {
      if (filter.query === '' && !unref(atMentionData)) {
        props.getMentionsRequest?.(props.detail.id, filter).then((res) => {
          atMentionData.value = res;
          success(atMentionData.value);
        });
      } else {
        const data = atMentionData.value?.filter((item) =>
          item.label!.toLowerCase().includes(filter.query.toLowerCase()),
        );
        success(data);
      }
    },
    [AtMentionEnum.Task]: (filter, success) => {
      props.getMentionsRequest?.(props.detail.id, filter).then((res) => {
        atMentionData.value = res;
        success(atMentionData.value);
      });
    },
  };
  const tinyOptions: Partial<RawEditorSettings & { mentions: Partial<AutoCompleteOptionType> }> = {
    image_resize: true,
    file_picker_types: 'file',
    statusbar: false,
    convert_urls: false,
    content_style: `
      body {
        background-color: var(--otd-basic-bg);
      }
      body.mce-content-readonly {
        cursor: not-allowed;
        background-color: var(--otd-disabled-bg);
      }
      p {
        margin: 6px 0;
        font-size: 14px;
        color: var(--otd-basic-text);
      }
      /* Firefox浏览器 */
      * {
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.2) #f2f2f2;
      }
      
      /* WebKit浏览器（包括Chrome和Safari）*/
      *::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      *::-webkit-scrollbar-track {
        background-color: #f2f2f2;
      }
      *::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
      }
      *::-webkit-scrollbar-thumb:hover {
        background-color: #b6b7b9;
      }
      body[data-mce-placeholder]:before {
        content: attr(data-mce-placeholder);
        color: #aaa;
        font-size: 14px;
        position: absolute;
      }
      img {
        display: block;
      }
      img:not([width]) {
        width: 120px;
      }
      .attachment { display: inline-flex; align-items: center; vertical-align: bottom; }
      .attachment > img { width: 20px !important; margin-right: 4px; }
      .attachment > a { color: ${PRIMARY_COLOR}; }
    `,
    attachment_icons_path: '/resource/tinymce/icons',
    // 上传附件方法
    attachment_upload_handler: function (file, success, failure, progress) {
      emit('uploadAttachment', file, props.detail, success, failure, progress);
    },
    paste_data_images: true,
    // 上传图片方法
    images_upload_handler: function (blobInfo, success, failure, progress) {
      emit('uploadImage', blobInfo, props.detail, success, failure, progress);
    },
    mentions: {
      rootBody,
      itemRender(user) {
        return <span>{user.name}</span>;
      },
      setOffset() {
        return { x: -248, y: -80 };
      },
      source(data, success) {
        const tab = data.tab ?? AtMentionEnum.User;

        if (data.isChangeTab) {
          atMentionData.value = undefined;
        }
        AtMentionRequestMap[tab](data, success);
      },
    },
    ...options,
  };
  return {
    tinyOptions,
  };
}
