import * as IconData from '@icon-park/vue-next/es/map';
import { IconMapItemType, IconsType } from '../../type';

const icons = Object.keys(IconData).map(
  (key) => [key, { value: key, isCustom: false }] as [IconsType, IconMapItemType],
);
const CustomIcon: IconMapItemType[] = [
  { value: 'Task', isCustom: true },
  { value: 'Milestone', isCustom: true },
];
const IconMaps = CustomIcon.map((item) => [item.value, item] as const).concat(icons);

const IconMap = new Map<string, IconMapItemType>(IconMaps);
const IconMapData = Array.from(IconMap.values());

export { IconMapData, IconMap };
