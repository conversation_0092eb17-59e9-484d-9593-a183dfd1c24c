<template>
  <div class="otd-comment-card">
    <div class="otd-comment-card__info">
      <OtdAvatar :url="comment.commentUserAvatar" size="28px" />
      <span class="otd-comment-card__info-name"> {{ comment.commentUser }} </span>
      <span class="otd-comment-card__info-time"> {{ formatToTime(comment.creationTime) }} </span>
      <div class="otd-comment-card__info-action">
        <OtdMoreAction
          action-type="icon"
          :data="comment"
          :list="list"
          :index="index"
          :actions="commentAction"
          :expand-number="3"
          hide-expand-name
        />
      </div>
    </div>
    <div class="otd-comment-card__content">
      <OtdPreview v-if="!comment.isEditDesc" :images="imagePreviewSet(comment.content)">
        <div class="otd-comment-card__content-description otd-tiny-description" v-html="comment.content"></div>
        <div class="otd-comment-card__emoticon" v-if="comment.emojiReplies?.length">
          <div
            class="otd-comment-card__emoticon-item"
            v-for="item in setEmoticon(comment.emojiReplies)"
            :key="item.emoji"
          >
            <Tooltip :title="t(`common.emoticons.${item.emoji}`)">
              <OtdEmoticonIcon :icon="item.emoji" :size="22" @click="handlerMoticon(comment, item.emoji)" />
            </Tooltip>
            <div class="emoticon-text"> {{ item.showUser.join(', ') }} </div>
            <Tooltip :title="item.hideUser.join(', ')">
              <div class="emoticon-text" v-if="item.hideUser.length"> , +{{ item.hideUser.length }} more </div>
            </Tooltip>
          </div>
        </div>
      </OtdPreview>
      <OtdSignleTinymce
        v-else
        v-bind="$props"
        :placeholder="t('common.addContent', { content: t('common.comment') })"
        :bordered="false"
        :default-value="comment.content"
        :plugins="['mention']"
        right-toolbar="| cancel confirm"
        @cancel="(data) => handleCancelDesc(data, comment)"
        @confirm="(data) => handleConfirmDesc(data, comment)"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { PropType } from 'vue';
  import { OtdAvatar } from '/@/components/OtdAvatar';
  import { OtdPreview } from '/@/components/OtdPreview';
  import { OtdMoreAction } from '/@/components/OtdMoreAction';
  import { OtdSignleTinymce } from '/@/components/OtdTinymce';
  import { formatToTime } from '/@/utils/dateUtil';
  import { imagePreviewSet } from '/@/utils/tool';
  import { useComment } from '../useComment';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getEmits } from '../props';
  import { Recordable } from '/#/global';
  import { getProps } from '/@/components/OtdTinymce/src/components/OtdSignleTinymce/props';
  // import { BasicIcon } from '/@/components/BasicIcon';
  import { Tooltip } from 'ant-design-vue';
  import { OtdEmoticonIcon } from '/@/components/OtdEmoticon';

  defineProps({
    comment: {
      type: Object as PropType<Recordable>,
      default: () => ({}),
    },
    list: {
      type: Array as PropType<Recordable[]>,
      default: () => [],
    },
    index: {
      type: Number,
    },
    ...getProps(),
  });
  defineEmits(getEmits());

  const { commentAction, handlerMoticon, handleCancelDesc, handleConfirmDesc, setEmoticon } = useComment();

  const { t } = useI18n();
</script>
<style lang="less" scoped>
  .otd-comment-card {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    padding-bottom: 16px;
    &:not(:last-child) {
      border-bottom: 1px solid var(--otd-border-color);
    }
    &:hover :deep(.otd-more-action),
    :deep(.otd-more-action__open) {
      display: flex !important;
    }

    &__info {
      display: flex;
      position: relative;
      line-height: 26px;
      &-name {
        margin-left: 10px;
        color: var(--otd-basic-text);
        margin-right: 10px;
      }
      &-time {
        color: var(--otd-icon-text);
        font-size: 12px;
      }
      &-action {
        position: absolute;
        top: 0;
        right: 0;
        :deep(.otd-more-action) {
          display: none;
        }
      }
    }
    &__content {
      padding-left: 38px;
      margin-top: 10px;
      :deep(.otd-tinymce-container) {
        margin-left: -38px;
      }
    }
    &__emoticon {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      margin-top: 6px;
      cursor: pointer;
      &-item {
        display: flex;
        align-items: center;
        padding: 2px 8px 2px 6px;
        font-size: 12px;
        border-radius: var(--otd-large-radius);
        background-color: var(--otd-content-bg);
        .emoticon-text {
          letter-spacing: 0.5px;
        }
        .otd-emoticon-icon {
          outline: unset;
          & + .emoticon-text::before {
            content: '|';
            margin: 0 4px;
            color: var(--otd-gray3-color);
            opacity: 0.2;
          }
        }
      }
    }
  }
</style>
