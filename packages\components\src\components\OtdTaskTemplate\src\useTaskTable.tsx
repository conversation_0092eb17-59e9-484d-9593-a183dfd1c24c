import { reactive, ref } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { Switch, TableColumnProps } from 'ant-design-vue';
import { NOTDATA } from '/@/settings/const';
import { Recordable } from '/#/global';
import { isUnDef } from '/@/tool';
// import { OtdUserSearch } from '/@/components/OtdUserSearch';
// import { OtdEditCellItem } from '/@/components/OtdEditCell';

export function useTaskTable() {
  const { t } = useI18n();
  const templateId = ref<string | undefined>('');
  const isLoadEnd = ref(false);
  const tableForm = reactive({ data: [] });
  // 表格列数据
  const tableColumns: TableColumnProps<Recordable>[] = [
    // 任务模板名字
    {
      title: `${t('common.subtask.taskName')}`,
      dataIndex: 'title',
      width: '250px',
      fixed: 'left',
    },
    // 负责人
    {
      title: `${t('common.subtask.resPerson')}`,
      dataIndex: 'responsibleUserName',
      width: '100px',
      align: 'center',
      ellipsis: true,
      customRender: ({ record }) => record.responsibleUserName || NOTDATA,
    },
    // 关注人
    {
      title: `${t('common.subtask.watcher')}`,
      dataIndex: 'followUsers',
      width: '160px',
      align: 'center',
      ellipsis: true,
      customRender: ({ record }) => {
        return (
          <div>
            <span v-show={record.followUsers}>{record.followUsers.map((item) => item.name).join('，')}</span>
            <span v-show={!record?.followUsers.length}>{NOTDATA}</span>
          </div>
        );
      },
    },
    // 确认人
    {
      title: `${t('common.subtask.verifier')}`,
      dataIndex: 'checkUsers',
      width: '160px',
      align: 'center',
      ellipsis: true,
      customRender: ({ record }) => {
        return (
          <div>
            <span v-show={record.checkUsers}>{record.checkUsers.map((item) => item.name).join('，')}</span>
            <span v-show={!record?.checkUsers.length}>{NOTDATA}</span>
          </div>
        );
      },
    },

    // 负责人部门
    // {
    //   title: `${t('common.resDepartment')}`,
    //   dataIndex: 'responsibleOrganizeName',
    //   width: '160px',
    //   align: 'center',
    //   ellipsis: true,
    //   customRender: ({ record }) => record.responsibleOrganizeName || NOTDATA,
    // },
    // // 确认人部门
    // {
    //   title: `${t('common.verificationDepartment')}`,
    //   dataIndex: 'checkerOrganizeName',
    //   width: '160px',
    //   align: 'center',
    //   ellipsis: true,
    //   customRender: ({ record }) => record.checkerOrganizeName || NOTDATA,
    // },
    // 前置任务
    {
      title: `${t('common.predecessorTask')}`,
      dataIndex: 'preTaskItemTitle',
      width: '160px',
      align: 'center',
      ellipsis: true,
      customRender: ({ record }) => record.preTaskItemTitle || NOTDATA,
    },
    // 自动完成
    {
      title: `${t('common.autoCompletion')}`,
      dataIndex: 'autoCompletion',
      width: '100px',
      align: 'center',
      customRender({ record, column }) {
        const { dataIndex } = column as { dataIndex: string };
        if (isUnDef(record[dataIndex])) {
          record[dataIndex] = true;
        }
        return <Switch v-model:checked={record[dataIndex]} />;
      },
    },
  ];

  return {
    tableForm,
    tableColumns,
    templateId,
    isLoadEnd,
  };
}
