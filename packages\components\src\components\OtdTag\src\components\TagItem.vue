<template>
  <Input
    ref="inputRef"
    class="otd-input otd-tags-content__input"
    v-model:value="editTagValue"
    size="small"
    @blur="handleEditTag(item)"
    @press-enter="($event.target as HTMLInputElement).blur()"
    v-if="isEdit"
  />
  <Tooltip :title="isShowTooltip ? item.tagName : undefined" v-else>
    <div
      ref="tagItemRef"
      class="otd-tags-content__item"
      :class="{ 'is-full': isFull }"
      @click="$emit('item-click', item)"
    >
      <div :class="{ 'is-private': item.tagAuth }" class="otd-tags-content__item-container" :style="getTagNameStyle">
        <span class="otd-tags-content__item-text otd-truncate" @mouseenter="visibilityChange($event)">
          {{ item.tagName }}
        </span>
      </div>
      <OtdMoreAction
        v-if="!isCard"
        ref="actionRef"
        class="ml-4px"
        :style="isFull ? undefined : getTagNameStyle"
        :data="item"
        :trigger="['click']"
        :actions="tagActions"
        :interval="0"
        :expand-number="expandNumber"
        actionType="icon"
        hide-expand-name
        destroyPopupOnHide
      />
    </div>
  </Tooltip>
</template>
<script lang="ts" setup>
  import { ref, nextTick, PropType, inject, computed, CSSProperties } from 'vue';
  import { Tooltip, Input, message } from 'ant-design-vue';
  import { MoreActionItem, OtdMoreAction } from '/@/components/OtdMoreAction';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { copyTextToClipboard } from '/@/hooks/web/useCopyToClipboard';
  import { OtdColor } from '/@/components/OtdColor';
  import { h } from 'vue';
  import { TagDto, colorNameToHex, getLightColor } from '../types';
  import { Recordable } from '/#/global';

  const { t } = useI18n();
  const props = defineProps({
    item: {
      type: Object as PropType<TagDto>,
      default: () => ({}),
    },
    index: {
      type: Number,
      default: -1,
    },
    list: {
      type: Array as PropType<TagDto[]>,
      default: () => [],
    },
    isCard: {
      type: Boolean,
      default: false,
    },
    hideAction: {
      type: Boolean,
      default: false,
    },
    closeable: {
      type: Boolean,
      default: false,
    },
    isFull: {
      type: Boolean,
      default: false,
    },
    // 展开数量，超出隐藏
    expandNumber: {
      type: Number,
      default: 1,
    },
  });

  defineEmits(['item-click']);

  const tagHandlerMap = inject('tagHandlerMap', {}) as Recordable<(data) => void>;
  const tagItemRef = ref();
  const editTagValue = ref<string | undefined>(undefined);
  const isEdit = ref(false);
  const inputRef = ref();

  const isShowTooltip = ref(false);
  // 超出容器宽度显示tooltip
  function visibilityChange(event) {
    const ev = event.target || event;
    const ev_weight = ev?.scrollWidth || 0; // 文本的实际宽度
    const content_weight = ev?.clientWidth || 0; // 文本的可视宽度
    if (ev_weight > content_weight) {
      // 实际宽度 > 可视宽度  文字溢出
      isShowTooltip.value = true;
    } else {
      // 否则为不溢出
      isShowTooltip.value = false;
    }
  }

  // 标签操作
  const tagActions: MoreActionItem[] = [
    // 关闭
    {
      id: 0,
      name: t('common.clearText'),
      icon: 'otd-icon-a-catthicksize24',
      iconSize: '10px',
      action: (data) => {
        tagHandlerMap.itemRemove({
          value: data,
          index: props.index,
          list: props.list,
        });
      },
      isHide() {
        return !props.closeable;
      },
    },
    // 删除
    {
      id: 1,
      name: t('common.tag.delText'),
      icon: 'otd-icon-a-catdeletesize24',
      color: '#ed6f6f',
      action: (data: TagDto) => {
        tagHandlerMap.delete({
          value: data,
          index: props.index,
          list: props.list,
        });
        // eslint-disable-next-line vue/no-mutating-props
        // props.list.splice(props.index, 1);
      },
      isHide() {
        return props.hideAction;
      },
    },
    // 复制
    {
      id: 2,
      name: t('common.tag.copy'),
      icon: 'otd-icon-fuzhi',
      action: (data: TagDto) => {
        copyTextToClipboard(data.tagName as string);
        message.success(t('common.tag.copySuccessTip'));
      },
      isHide() {
        return props.hideAction;
      },
    },
    // 重命名
    {
      id: 3,
      name: t('common.tag.rename'),
      icon: 'otd-icon-a-cateditsize24',
      action: (data: TagDto) => {
        editTagValue.value = data.tagName;
        isEdit.value = true;
        nextTick(() => {
          inputRef.value.focus();
        });
      },
      isHide() {
        return props.hideAction;
      },
    },
    // 修改颜色
    {
      id: 4,
      name: t('common.tag.changeColor'),
      icon: 'otd-icon-a-catcolorsize24',
      action: () => {},
      expand: h(OtdColor, {
        onSave: handleSaveTagColor,
      }),
      isHide() {
        return props.hideAction;
      },
    },
    // 设置为私有或者共有
    // {
    //   id: 5,
    //   name: props.item.tagAuth ? t('common.tag.setPublic') : t('common.tag.setPrivate'),
    //   icon: props.item.tagAuth ? 'otd-icon-gonggong' : 'otd-icon-a-catlocksize24',
    //   action: () => {
    //     tagHandlerMap.changeType(props.item);
    //   },
    //   isHide() {
    //     return props.hideAction;
    //   },
    // },
  ];

  const getTagNameStyle = computed((): CSSProperties => {
    const color = colorNameToHex(props.item.color);
    const lightColor = getLightColor(color, 0.8) ?? undefined;
    return {
      // 以选中色为文字色，背景变浅
      fontWeight: 500,
      '--action-color': color,
      '--action-bg': lightColor,
    };
  });

  // 编辑标签
  function handleEditTag(data: TagDto) {
    if (!editTagValue.value) {
      inputRef.value.focus();
      return false;
    }
    const lastValue = data.tagName;
    data.tagName = editTagValue.value;
    isEdit.value = false;
    tagHandlerMap.rename({
      value: data,
      index: props.index,
      lastValue,
      list: props.list,
    });
  }

  const actionRef = ref();
  // 保存标签颜色
  function handleSaveTagColor(data) {
    if (data.isPicker) {
      actionRef.value.handleExpandClose();
    }
    tagHandlerMap.save({ ...data, value: props.item });
  }
</script>
<style lang="less" scoped>
  .otd-input {
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 10px;
  }
  :deep(.otd-more-action) {
    .hide-action-drop {
      margin-left: 0 !important;
    }
  }
</style>
