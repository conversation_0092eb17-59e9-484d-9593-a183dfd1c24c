<template>
  <OtdMoreAction
      :data="props.item"
      :list="props.list"
      size="large"
      :actions="Actions"
      :expand-number="3"
      hide-expand-name
      destroy-popup-on-hide
  />
</template>
<script lang="ts" setup>
import { OtdMoreAction, MoreActionItem } from "@otd/otd-ui";
import { ref, defineProps } from 'vue'
import { message } from 'ant-design-vue';

const props = defineProps({
  item: {
    type: Object,
    default: {
      fixed: true,
    },
  },
  index: {
    type: Number,
    default: -1,
  },
  list: {
    type: Array,
    default: () => [],
  },
  hideAction: {
    type: Boolean,
    default: false,
  }
});
const item = ref([{}])

// 操作
const Actions: MoreActionItem[] = [
  {
    id: 0,
    icon: 'otd-icon-a-cateditsize24',
    tip: 'tip',
    action: () => {
      message.info('编辑')
    },
  },
  {
    id: 1,
    icon: 'otd-icon-a-Property128',
    name: '新增',
    action: () => {
      message.info('新增')
    },
  },
  {
    id: 2,
    icon: 'otd-icon-a-cattagsize24',
    name: '标签',
    action: () => {
      message.info('标签')
    },
  },
  {
    id: 3,
    name: '删除',
    icon: 'otd-icon-a-catdeletesize24',
    color: 'red',
    action: () => {
      message.info('删除')
    }
  },
  {
    id: 4,
    name: '禁用',
    icon: 'otd-icon-a-catlocksize24',
    strict: true,
  }
];
</script>
<style lang="less" scoped></style>
