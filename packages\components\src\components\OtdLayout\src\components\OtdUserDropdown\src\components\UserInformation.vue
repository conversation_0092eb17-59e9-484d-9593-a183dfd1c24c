<template>
  <div class="otd-user-information">
    <div class="otd-user-information__info">
      <label class="otd-user-information__avatar">
        <OtdAvatar size="50px" :url="userInfo.avatar" />
        <input
          type="file"
          ref="uploadImgRef"
          accept="image/jpeg,image/png, image/pjp, image/jpep, image/webp"
          @change="uploadImg($event)"
        />
      </label>

      <div class="otd-user-information__content" v-if="!hideInfo">
        <div class="otd-user-information__content-name otd-truncate otd-title-text" :title="userInfo.realName">
          {{ userInfo.realName }}
        </div>
        <div
          class="otd-user-information__content-account otd-truncate otd-desc-text"
          v-if="userInfo?.email || userInfo?.phoneNumber"
          :title="userInfo?.email || userInfo?.phoneNumber"
        >
          <template v-if="userInfo?.email">
            <span>{{ t('common.email') }}: </span>{{ userInfo.email }}
          </template>
          <template v-else>
            <span>{{ t('common.phone') }}: </span>{{ userInfo.phoneNumber }}
          </template>
        </div>
      </div>
    </div>
    <div
      class="otd-user-information__tenant otd-truncate otd-desc-text"
      :title="userInfo.tenantInfo.name"
      v-if="userInfo?.tenantInfo?.name && !hideInfo"
    >
      {{ userInfo.tenantInfo.name }}
    </div>

    <BasicModal
      :title="t('common.upload.modalTitle')"
      :cancelText="t('common.cancelText')"
      :okText="t('common.okText')"
      :confirm-loading="confirmLoading"
      :can-fullscreen="false"
      @ok="handleSubmitCut"
      @cancel="handleCancelCut"
      @register="register"
    >
      <div class="otd-cropper">
        <OtdCropper
          v-if="getVisible"
          ref="cropperRef"
          class="otd-cropper__content"
          alt="Source Image"
          :src="UploadImg"
        />
      </div>
    </BasicModal>
  </div>
</template>
<script lang="ts" setup>
  import { OtdCropper } from '/@/components/OtdCropper';
  import { message } from 'ant-design-vue';
  import { OtdAvatar } from '/@/components/OtdAvatar';
  import { BasicModal, useModal } from '/@/components/BasicModal';
  import { PropType, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    userInfo: {
      type: Object,
      default: () => ({}),
    },
    uploadAvatar: {
      type: Function as PropType<(data) => Promise<any>>,
    },
    hideInfo: {
      type: Boolean,
      default: false,
    },
  });

  const confirmLoading = ref(false);
  const UploadImg = ref('');
  const cropperRef = ref();
  const uploadImgRef = ref();
  const fileName = ref('');

  const [register, { openModal, closeModal, getVisible }] = useModal();

  function uploadImg(e) {
    if (
      ['jpg', 'png', 'jpeg', 'pjpeg', 'jfif', 'wepg', 'webp', 'pjp'].includes(
        e.target.files[0].name.split('.').reverse()[0],
      )
    ) {
      const reader = new FileReader();
      reader.readAsDataURL(e.target.files[0]);
      fileName.value = e.target.files[0].name;
      reader.onload = function () {
        UploadImg.value = this.result as string;
        openModal();
      };
    } else {
      return message.warning(`${t('common.upload.cutMessageWarning')}`);
    }
  }

  function handleSubmitCut() {
    try {
      if (props?.uploadAvatar) {
        confirmLoading.value = true;
        const canvas = cropperRef.value.getCroppedCanvas();

        props.uploadAvatar({ data: dataURLtoFile(canvas.toDataURL()), fileName: fileName.value }).finally(() => {
          handleCancelCut();
        });
      } else {
        closeModal();
      }
    } catch (error) {
      handleCancelCut();
    }
  }

  function dataURLtoFile(url) {
    let arr = url.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bst = atob(arr[1]),
      n = bst.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bst.charCodeAt(n);
    }
    return new File([u8arr], fileName.value, { type: mime });
  }

  function handleCancelCut() {
    closeModal();
    uploadImgRef.value.value = null;
    UploadImg.value = '';
    confirmLoading.value = false;
  }
</script>
<style lang="less" scoped>
  .otd-user-information {
    padding: 0 4px;
    width: 192px;
    &__info {
      display: flex;
      align-items: center;
      padding: 8px 0;
    }
    &__avatar {
      position: relative;
      cursor: pointer;
      > input {
        display: none;
      }
      &:hover {
        &::after {
          content: '\e613';
          font-family: otdIconfont;
          position: absolute;
          width: 100%;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 50%;
          font-size: 28px;
          color: var(--otd-white-text);
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba(0, 0, 0, 0.2);
        }
      }
    }

    &__content {
      padding: 8px 0;
      margin-left: 10px;
      max-width: 156px;
      overflow: hidden;
      &-name {
        margin-bottom: 8px;
      }
      &-account {
      }
    }
    &__tenant {
      // margin-top: 8px;
    }
  }
  .otd-cropper {
    height: 300px;
    padding: 6px;
    &__content {
      height: 100%;
      width: 100%;
    }
  }
</style>
