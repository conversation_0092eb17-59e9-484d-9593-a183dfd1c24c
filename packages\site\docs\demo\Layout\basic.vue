<template>
  <div class="layout-basic">
    <OtdLayout
      v-model:selectedKeys="selectedKeys"
      :menu-items="items"
      :user-info="userInfo"
      :custom-quick-actions="quickAction"
    >
      <template #config>
        <Button class="otd-gradient-button">111121</Button>
      </template>
      <span>1234</span>
    </OtdLayout>
  </div>
</template>
<script lang="tsx" setup>
  import { OtdLayout, Button, MoreActionItem, OtdAvatar } from '@otd/otd-ui';
  import { reactive, ref } from 'vue';
  import { flatten } from 'lodash-es';

  const selectedKeys = ref(['/micro/tms0', 'task/list00', 'task/2list00']);
  const items = reactive(
    new Array(8).fill(0).map((_, i) => ({
      path: '/micro/tms' + i,
      name: 'OTDTMS',
      meta: {
        orderNo: 110, // 路由排序顺序
        icon: 'otd-icon-lieshezhi',
        title: '一级菜单' + i,
        policy: 'TaskManagement',
      },
      children:
        i % 3 === 0
          ? flatten(
              new Array(3).fill(0).map((_, j) => [
                // 简易任务列表
                {
                  path: `simpleList${i}${j}`,
                  name: 'SimpleList',
                  component: 'LAYOUT',
                  meta: {
                    title: '二级菜单(一)' + i + j,
                    hideLayout: true,
                    hideMenu: true,
                  },
                },
                // 任务列表
                {
                  path: `task/list${i}${j}`,
                  name: 'TaskList',
                  component: 'LAYOUT',
                  meta: {
                    affix: true,
                    title: '二级菜单(二)' + i + j,
                    icon: 'otd-icon-lieshezhi',
                    policy: 'TaskManagement.TaskItemPermit.Query',
                  },
                  children: [
                    // 任务列表
                    {
                      path: `task/2list${i}${j}`,
                      name: 'TaskList',
                      component: 'LAYOUT',
                      meta: {
                        affix: true,
                        title: '三级菜单' + i + j,
                        icon: 'otd-icon-lieshezhi',
                        policy: 'TaskManagement.TaskItemPermit.Query',
                        // hideMenu: true,
                      },
                    },
                    // {
                    //   path: `task/3list${i}${j}`,
                    //   name: 'TaskList',
                    //   component: 'LAYOUT',
                    //   meta: {
                    //     affix: true,
                    //     title: '三级菜单' + i + j,
                    //     icon: 'otd-icon-lieshezhi',
                    //     policy: 'TaskManagement.TaskItemPermit.Query',
                    //   },
                    // },
                  ],
                },
                // 任务模板
                {
                  path: `task/template${i}${j}`,
                  name: 'TaskTemplate',
                  component: 'LAYOUT',
                  meta: {
                    title: '二级菜单(三)' + i + j,
                    // icon: "otd-icon-lieshezhi",
                    policy: 'TaskManagement.TaskTemplatePermit.Query',
                  },
                },
              ]),
            )
          : undefined,
    })),
  );

  const userInfo = {
    avatar: 'https://picsum.photos/200/300',
    email: '<EMAIL>',
    token:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************.qeLFqTxOmOGXs_nj7yuJtnbveSBjZmGjIqdtxFkEafk',
    idToken: '',
    isSts: false,
    realName: 'limin',
    roles: ['admin'],
    tenantInfo: {
      concurrencyStamp: '453c3c4abc064248a91319098c96a911',
      extraProperties: {},
      id: '3a0c30d8-7129-86d7-e665-f1eef29d82fa',
      name: '苏州奥斯坦丁软件科技有限公司',
    },
    userId: '3a08bb39-64b3-9116-983e-e9327b78bd47',
    username: 'admin',
  };

  const quickAction: MoreActionItem[] = reactive([
    {
      id: '111',
      icon: 'otd-icon-zidongwancheng',
      name: '11111',
      fixed: true,
    },
    {
      id: '333',
      icon: 'otd-icon-zidongwancheng',
      name: '33333',
      // fixed: true,
      pin: true,
    },
    {
      id: '222',
      icon: () => <OtdAvatar size="24px" url="https://picsum.photos/200/300" />,
      name: '11111',
      action: () => {
        console.log(111);
      },
    },
  ]);
  // setTimeout(() => {
  //   quickAction.push({
  //     id: '66766',
  //     icon: () => <OtdAvatar size="24px" url="https://picsum.photos/200/300" />,
  //     name: '7777',
  //   });
  // }, 10000);
</script>
<style lang="less" scoped>
  .layout-basic {
    height: 600px;
  }
</style>
