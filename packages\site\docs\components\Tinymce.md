## 富文本

<demo src="../demo/Tinymce/basic.vue" title="富文本"></demo>

## 属性

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 版本 |
| --- | --- | --- | --- | --- | --- |
| options | tinymce 配置 | Object | -- |  | 1.0 |
| value | 双向绑定的值 | String | -- |  | 1.0 |
| modelValue | 双向绑定的值 | String | -- |  | 1.0 |
| plugins | tinymce 扩展 | String[] | -- |  | 1.0 |
| height | 高度 | Number \| String | -- | `400` | 1.0 |
| maxHeight | 最大高度 | Number | -- |  | 1.0 |
| minHeight | 最下高度 | Number | -- |  | 1.0 |
| width | 宽度 | Number \| String | -- | `auto` | 1.0 |
| placeholder | 提示词 | String | -- |  | 1.0 |
| bordered | 边框 | Boolean | -- | `false` | 1.0 |
| disabled | 禁用 | Boolean | -- | `false` | 1.0 |
| detail | 数据详情 | Object | -- |  | 1.0 |
| notBind | 绑定默认值 | Boolean | -- | `false` | 1.0 |
| defaultValue | 默认值 | String | -- |  | 1.0 |
| leftToolbar | 左侧工具栏 | String | -- |  | 1.0 |
| rightToolbar | 右侧工具栏 | String | -- |  | 1.0 |
| getMentionsRequest | @的远程接口 | (id: string, filter: { query: string, tab: AtMentionEnum}) => Promise<UserOptionItemType[]> | -- |  | 1.0 |
