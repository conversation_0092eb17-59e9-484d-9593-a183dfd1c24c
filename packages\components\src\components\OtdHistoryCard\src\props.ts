import { PropType } from 'vue';
import { Recordable } from '/#/global';
import { OtdHistoryCardFieldsType } from './type';
import { DefaultFields } from '/@/components/OtdHistory/src/props';

export enum RelateEnum {
  Meeting = 0,
  ThirdParty = 1,
  Others = 2,
  Project = 3,
  Manually = 4,
  OtdCrm = 5,
}

export const DefaultCardFields = {
  ...DefaultFields,
  relatedId: 'relatedId',
  relatedTitle: 'relatedTitle',
  relatedType: 'relatedType',
  relatedName: 'relatedName',
  i18NRelatedType: 'i18NRelatedType',
  i18NSubRelatedType: 'i18NSubRelatedType',
  status: 'status',
  taskType: 'taskType',
};

export const getProps = () => ({
  value: {
    type: Array as PropType<Recordable[]>,
    default: () => [],
  },
  height: {
    type: String,
  },
  fields: {
    type: Object as PropType<Partial<OtdHistoryCardFieldsType>>,
    default: () => ({}),
  },
  loadMore: {
    type: Function as PropType<(data) => Promise<{ items?: Recordable[]; totalCount?: number }>>,
  },
  immediateLoad: {
    type: Boolean,
    default: true,
  },
});
