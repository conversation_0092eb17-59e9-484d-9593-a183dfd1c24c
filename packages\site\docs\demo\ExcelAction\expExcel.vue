<template>
  <!--  <Button-->
  <!--      preIcon="ant-design:cloud-download-outlined"-->
  <!--      type="primary"-->
  <!--      @click="handleExport"-->
  <!--      v-auth="'AbpIdentity.Users.Export'"-->
  <!--  >-->
  <!--    导出-->
  <!--  </Button>-->
</template>

<script lang="ts" setup>
  // import { Button, message } from 'ant-design-vue';
  // import { OtdExpExcelModal, ExcelData, TableColumnPropsType } from '@otd/otd-ui';
  //
  // import { ref } from 'vue';
  // const tableListRef = ref<any>([]);
  // const handleExport = () => {
  //   const { getFieldsValue } = getForm();
  //   let request = getFieldsValue();
  //   exportAsync({ request });
  // };
</script>

<style scoped lang="less"></style>
