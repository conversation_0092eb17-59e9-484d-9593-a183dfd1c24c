import { ExtractPropTypes } from 'vue';
import { getProps } from './props';
import { DrawContentEnum } from './useDrawPoster';

// type CoordinateType<T> = T extends 'text'
//   ? [number, number]
//   : T extends 'image'
//   ? [[number, number], [number, number]]
//   : never;
type CoordinateType = [number, number];

export type DrawOptionType<T extends keyof typeof DrawContentEnum = any> = {
  type: T;
  content: string | (() => Promise<string>);
  coordinate: CoordinateType;
  style?: Partial<{
    width: number;
    height: number;
    color: string;
    fontSize: number;
    fontFamily: string;
    borderRadius: number;
  }>;
};

export type PosterPropsType = ExtractPropTypes<ReturnType<typeof getProps>>;
