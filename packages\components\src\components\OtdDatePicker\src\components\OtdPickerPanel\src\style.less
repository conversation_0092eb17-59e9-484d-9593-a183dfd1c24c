@prefixCls: ~'otd-picker';

.@{prefixCls}-panel {
  width: 100%;
  height: 100%;

  :deep(.@{prefixCls}-date-panel),
  :deep(.@{prefixCls}-month-panel),
  :deep(.@{prefixCls}-decade-panel),
  :deep(.@{prefixCls}-year-panel) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  :deep(.@{prefixCls}-header) {
    display: flex;
    align-items: center;
    padding: 0 12px;
    line-height: 32px;
    &-super-prev-btn,
    &-prev-btn,
    &-super-next-btn,
    &-next-btn {
      cursor: pointer;
      padding: 4px;
      font-size: 22px;
      color: var(--otd-gray3-color);
      transform: translateY(-1px);
      background-color: transparent;
      &:hover {
        color: var(--otd-primary-text);
      }
    }
    &-super-prev-btn,
    &-prev-btn {
      float: left;
    }
    .@{prefixCls}-header-view {
      overflow: hidden;
      text-align: center;
      width: 100%;
      font-size: 16px;
      > button {
        background-color: transparent;
        &:hover {
          color: var(--otd-primary-text);
        }
        & + button {
          margin-left: 6px;
        }
      }
      .@{prefixCls}-year-btn {
      }
    }
    &-super-next-btn,
    &-next-btn {
      float: right;
    }
  }
  :deep(&.otd-picker-panel-has-range),
  :deep(&.otd-picker-panel-has-range-hover) {
    .@{prefixCls}-cell-range-start-single,
    .@{prefixCls}-cell-range-end-single {
      &::before {
        content: '' !important;
      }
    }
  }
  :deep(.@{prefixCls}-body) {
    flex: 1;
    .@{prefixCls}-content {
      width: 100%;
      height: 100%;
      > thead > tr > th,
      > tbody > tr > td {
        padding: 4px 0;
        line-height: 30px;
        text-align: center;
        background-color: transparent;
        font-size: 14px;
      }
      > tbody > tr > td {
        cursor: pointer;
        color: var(--otd-gray4-color);
        position: relative;
        .@{prefixCls}-cell-inner {
          border-radius: var(--otd-default-radius);
          line-height: 30px;
          min-width: 32px;
          padding: 0 6px;
          display: inline-block;
          border: 1px solid transparent;
          white-space: nowrap;
          position: relative;
          z-index: 1;
          &:hover {
            background-color: var(--otd-gray3-hover);
          }
        }
        &.@{prefixCls}-cell-in-view {
          color: var(--otd-basic-text);
        }
        &.@{prefixCls}-cell-selected,
        &.@{prefixCls}-cell-range-start,
        &.@{prefixCls}-cell-range-hover-start,
        &.@{prefixCls}-cell-range-end,
        &.@{prefixCls}-cell-range-hover-end,
        &.@{prefixCls}-cell-in-view:hover {
          .@{prefixCls}-cell-inner {
            background-color: var(--otd-primary-text);
            color: var(--otd-white-text);
            border-color: var(--otd-primary-text);
          }
        }
        &.@{prefixCls}-cell-today {
          .@{prefixCls}-cell-inner {
            border-color: var(--otd-primary-text);
          }
          &.@{prefixCls}-cell-range-hover,
          &.@{prefixCls}-cell-in-range {
            .@{prefixCls}-cell-inner {
              border-color: transparent;
            }
          }
        }

        &.@{prefixCls}-cell-range-start,
        &.@{prefixCls}-cell-range-hover-start,
        &.@{prefixCls}-cell-range-end,
        &.@{prefixCls}-cell-range-hover-end,
        &.@{prefixCls}-cell-in-range,
        &.@{prefixCls}-cell-range-hover {
          &::before {
            position: absolute;
            background-color: var(--otd-basic-active);
            top: 50%;
            inset-inline-start: 0;
            inset-inline-end: 0;
            z-index: 0;
            height: 32px;
            transform: translateY(-50%);
            content: '';
          }
        }
        &.@{prefixCls}-cell-range-start-single,
        &.@{prefixCls}-cell-range-end-single {
          &::before {
            content: unset;
          }
        }
        &.@{prefixCls}-cell-range-start:not(.otd-picker-cell-range-start-single),
        &.@{prefixCls}-cell-range-start.@{prefixCls}-cell-range-hover-start,
        &.@{prefixCls}-cell-range-hover-start:not(.otd-picker-cell-range-start-single) {
          &::before {
            inset-inline-start: 50%;
          }
        }
        &.@{prefixCls}-cell-range-end:not(.otd-picker-cell-range-end-single),
        &.@{prefixCls}-cell-range-end.@{prefixCls}-cell-range-hover-end,
        &.@{prefixCls}-cell-range-hover-end:not(.otd-picker-cell-range-end-single) {
          &::before {
            inset-inline-end: 50%;
          }
        }

        &.@{prefixCls}-cell-in-range {
          .@{prefixCls}-cell-inner {
            background-color: var(--otd-basic-active);
            border-color: transparent;
          }
        }
      }
    }
  }
}
