# 同步账号

<p style="font-size:26px">代码演示</p>

## 基础

<demo src="../demo/SynchronizeAccounts/basic.vue" title="基础"></demo>

### 属性

| 参数            | 说明               | 类型     | 可选值 | 默认值               | 版本 |
| --------------- | ------------------ | -------- | ------ | -------------------- | ---- |
| getList         | 获取用户列表       | Function | --     | () => {}             | 1.0  |
| batchUpdateUser | 保存本次更新的用户 | Function | --     | ({ saveData }) => {} | 1.0  |
| ref             | 参下表[Ref](#Ref)  | --       | --     | --                   | 1.0  |

### <span id='Ref'>Ref</span>

| 参数 | 说明     | 类型     | 可选值 | 默认值     | 版本 |
| ---- | -------- | -------- | ------ | ---------- | ---- |
| show | 显示弹窗 | Function | --     | () => ｛｝ | 1.0  |
