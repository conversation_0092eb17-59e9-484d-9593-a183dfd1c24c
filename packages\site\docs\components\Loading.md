## Loading

<demo src="../demo/Loading/basic.vue" title="loading加载"></demo>

## 属性

| 参数       | 说明             | 类型    | 可选值                    | 默认值    | 版本 |
| ---------- | ---------------- | ------- | ------------------------- | --------- | ---- |
| tip        | loading 加载文案 | String  |                           |           | 1.0  |
| width      | 自定义尺寸       | Number  |                           |           | 1.0  |
| size       | 固定尺寸         | String  | `default`,`small`,`large` | `default` | 1.0  |
| absolute   | 相对定位         | Boolean |                           | `false`   | 1.0  |
| loading    | 加载状态         | Boolean |                           | `false`   | 1.0  |
| background | 背景颜色         | String  |                           |           | 1.0  |
| theme      | 主题色           | String  | `dark` , `light`          |           | 1.0  |
