<template>
  <div class="otd-layout__menu__content">
    <OtdScrollbar>
      <OtdMenu
        style="--text-color: var(--otd-menu-text)"
        v-bind="$attrs"
        mode="inline"
        inline-collapsed
        :items="items"
      />
    </OtdScrollbar>
  </div>
</template>
<script lang="ts" setup>
  import { OtdScrollbar } from '/@/components/OtdScrollbar';
  import { PropType } from 'vue';
  import { MenuType, OtdMenu } from '/@/components/OtdMenu';

  defineProps({
    items: {
      type: Array as PropType<MenuType[]>,
      default: () => [],
    },
  });
</script>
<style lang="less" scoped>
  .otd-layout__menu__content {
    flex: 1;
    overflow: hidden;
    padding-bottom: 32px;
    :deep(.ant-menu) {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
      margin: 0;
      border-inline-end: unset;
      .ant-menu-item,
      .ant-menu-submenu {
        border-radius: var(--otd-circle-radius);
        color: var(--text-color);
        margin: 0;
        width: 46px;
        height: 46px;
        line-height: 46px;
        text-align: center;
        display: flex;
        justify-content: center;
        padding: 0;
        list-style: none;
        transition: unset;
        > div {
          width: 100%;
          display: flex;
          justify-content: center;
        }
        .ant-menu-item-icon,
        .ant-menu-title-content {
          transition: unset;
        }
        cursor: pointer;
        .ant-menu-submenu-arrow {
          display: none;
        }
        &.ant-menu-item-selected,
        &.ant-menu-submenu-selected {
          background-image: linear-gradient(180deg, #3749fc 0%, #394efb 32%, #3290ff 100%);
          color: var(--otd-white-text);
          .ant-menu-submenu-title {
            color: var(--otd-white-text);
          }
        }
        &:hover,
        &.ant-menu-submenu-open {
          background-image: linear-gradient(180deg, #536cff 6%, #4c35ff 55%, #6051ff 100%);
          color: var(--otd-white-text);
          .ant-menu-submenu-title {
            color: var(--otd-white-text);
            background-color: unset;
          }
        }
      }
      .ant-menu-submenu-title {
        margin: 0;
        width: 100%;
        height: 100%;
        line-height: unset;
      }
    }
  }
</style>
